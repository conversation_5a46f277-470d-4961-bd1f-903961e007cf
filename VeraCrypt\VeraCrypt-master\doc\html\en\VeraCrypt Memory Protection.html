<!DOCTYPE html>
<html lang="en">
<head>
<meta http-equiv="content-type" content="text/html; charset=utf-8" />
<title>VeraCrypt - Free Open source disk encryption with strong security for the Paranoid</title>
<meta name="description" content="VeraCrypt is free open-source disk encryption software for Windows, Mac OS X and Linux. In case an attacker forces you to reveal the password, VeraCrypt provides plausible deniability. In contrast to file encryption, data encryption performed by VeraCrypt is real-time (on-the-fly), automatic, transparent, needs very little memory, and does not involve temporary unencrypted files."/>
<meta name="keywords" content="encryption, security"/>
<link href="styles.css" rel="stylesheet" type="text/css" />
</head>
<body>

<div>
<a href="Documentation.html"><img src="VeraCrypt128x128.png" alt="VeraCrypt"/></a>
</div>

<div id="menu">
	<ul>
	  <li><a href="Home.html">Home</a></li>
	  <li><a href="Code.html">Source Code</a></li>
	  <li><a href="Downloads.html">Downloads</a></li>
	  <li><a class="active" href="Documentation.html">Documentation</a></li>
	  <li><a href="Donation.html">Donate</a></li>
	  <li><a href="https://sourceforge.net/p/veracrypt/discussion/" target="_blank">Forums</a></li>
	</ul>
</div>

<div>
<p>
<a href="Documentation.html">Documentation</a>
<img src="arrow_right.gif" alt=">>" style="margin-top: 5px">
<a href="Security%20Requirements%20and%20Precautions.html">Security Requirements and Precautions</a>
<img src="arrow_right.gif" alt=">>" style="margin-top: 5px">
<a href="VeraCrypt%20Memory%20Protection.html">VeraCrypt Memory Protection</a>
</p></div>

<div class="wikidoc">
<h1>VeraCrypt Memory Protection Mechanism</h1>
<h2>Introduction</h2>
<p>VeraCrypt always strives to enhance user experience while maintaining the highest level of security. The memory protection mechanism is one such security feature. However, understanding the need for accessibility, we have also provided an option to disable this mechanism for certain users. This page provides in-depth information on both.</p>
<h2>Memory Protection Mechanism: An Overview</h2>
<p>
The memory protection mechanism ensures that non-administrator processes are prohibited from accessing the VeraCrypt process memory. This serves two primary purposes:
<ul>
	<li>Security Against Malicious Activities: The mechanism prevents non-admin processes from injecting harmful data or code inside the VeraCrypt process.</li>
	<li>Protection of Sensitive Data: Although VeraCrypt is designed to not leave sensitive data in memory, this feature offers an extra layer of assurance by ensuring other non-admin processes cannot access or extract potentially sensitive information.</li>
</ul>
</p>
<h2>Why Introduce An Option To Disable Memory Protection?</h2>
<p>
	Some accessibility tools, like screen readers, require access to a software's process memory to effectively interpret and interact with its user interface (UI). VeraCrypt's memory protection unintentionally hindered the functioning of such tools. To ensure that users relying on accessibility tools can still use VeraCrypt without impediments, we introduced this option.
</p>
<h2>How to Enable/Disable the Memory Protection Mechanism?</h2>
<p>
	By default, the memory protection mechanism is enabled. However, you can disable through VeraCrypt main UI or during installation.
	<ol>
		<li>During installation:
			<ul>
				<li>In the setup wizard, you'll encounter the <b>"Disable memory protection for Accessibility tools compatibility"</b> checkbox.</li>
				<li>Check the box if you want to disable the memory protection. Leave it unchecked to keep using memory protection.</li>
				<li>Proceed with the rest of the installation.</li>
			</ul>
		</li>
		<li>Post-Installation:
			<ul>
				<li>Open VeraCrypt main UI and navigate to the menu Settings -> "Performance/Driver Configuration".</li>
				<li>Locate and check/uncheck the <b>"Disable memory protection for Accessibility tools compatibility"</b> option as per your needs. You will be notified that an OS reboot is required for the change to take effect.</li>
				<li>Click <b>OK</b>.</li>
			</ul>
		</li>
		<li>During Upgrade or Repair/Reinstall
			<ul>
				<li>In the setup wizard, you'll encounter the <b>"Disable memory protection for Accessibility tools compatibility"</b> checkbox.</li>
				<li>Check/uncheck the <b>"Disable memory protection for Accessibility tools compatibility"</b> option as per your needs.</li>
				<li>Proceed with the rest of the upgrade or repair/reinstall.</li>
				<li>You will be notified that an OS reboot is required if you have changed the memory protection setting.</li>
			</ul>

		</li>
	</ol>
<h2>Risks and Considerations</h2>
<p>
While disabling the memory protection mechanism can be essential for some users, it's crucial to understand the risks:
<ul>
	<li><b>Potential Exposure:</b> Disabling the mechanism could expose the VeraCrypt process memory to malicious processes.</li>
	<li><b>Best Practice:</b> If you don't require accessibility tools to use VeraCrypt, it's recommended to leave the memory protection enabled.</li>
</ul>
</p>
<h2>FAQ</h2>
<p>
	<b>Q: What is the default setting for the memory protection mechanism?</b><br>
	<b>A:</b> The memory protection mechanism is enabled by default.
</p>
<p>
	<b>Q: How do I know if the memory protection mechanism is enabled or disabled?</b><br>
	<b>A:</b> You can check the status of the memory protection mechanism in the VeraCrypt main UI. Navigate to the menu Settings -> "Performance/Driver Configuration". If the <b>"Disable memory protection for Accessibility tools compatibility"</b> option is checked, the memory protection mechanism is disabled. If the option is unchecked, the memory protection mechanism is enabled.
</p>
<p>
	<b>Q: Will disabling memory protection reduce the encryption strength of VeraCrypt?</b><br>
	<b>A:</b> No, the encryption algorithms and their strength remain the same. Only the protection against potential memory snooping and injection by non-admin processes is affected.
</p>
<p>
	<b>Q: I don't use accessibility tools. Should I disable this feature?</b><br>
	<b>A:</b> No, it's best to keep the memory protection mechanism enabled for added security.
</p>
</div><div class="ClearBoth"></div></body></html>
