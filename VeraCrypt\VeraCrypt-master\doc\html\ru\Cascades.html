﻿<!DOCTYPE html>
<html lang="ru">
<head>
<meta http-equiv="content-type" content="text/html; charset=utf-8" />
<title>VeraCrypt - Бесплатное надёжное шифрование дисков с открытым исходным кодом</title>
<meta name="description" content="VeraCrypt это бесплатное программное обеспечение для шифрования дисков с открытым исходным кодом для Windows, Mac OS X (macOS) и Linux. В случае, если злоумышленник вынуждает вас раскрыть пароль, VeraCrypt обеспечивает правдоподобное отрицание наличия шифрования. В отличие от пофайлового шифрования, VeraCrypt шифрует данные в реальном времени (на лету), автоматически, прозрачно, требует очень мало памяти и не использует временные незашифрованные файлы."/>
<meta name="keywords" content="encryption, security, шифрование, безопасность"/>
<link href="styles.css" rel="stylesheet" type="text/css" />
</head>
<body>

<div>
<a href="Documentation.html"><img src="VeraCrypt128x128.png" alt="VeraCrypt"/></a>
</div>

<div id="menu">
	<ul>
	  <li><a href="Home.html">Начало</a></li>
	  <li><a href="Code.html">Исходный код</a></li>
	  <li><a href="Downloads.html">Загрузить</a></li>
	  <li><a class="active" href="Documentation.html">Документация</a></li>
	  <li><a href="Donation.html">Поддержать разработку</a></li>
	  <li><a href="https://sourceforge.net/p/veracrypt/discussion/" target="_blank">Форум</a></li>
	</ul>
</div>

<div>
<p>
<a href="Documentation.html">Документация</a>
<img src="arrow_right.gif" alt=">>" style="margin-top: 5px">
<a href="Encryption%20Algorithms.html">Алгоритмы шифрования</a>
<img src="arrow_right.gif" alt=">>" style="margin-top: 5px">
<a href="Cascades.html">Каскады шифров</a>
</p></div>

<div class="wikidoc">
<h1>Каскады шифров</h1>
<p>&nbsp;</p>
<h2>AES-Twofish</h2>
<p>Последовательно выполняемые (каскадом) [15, 16] два шифра, работающие в режиме XTS (см. раздел <a href="Modes%20of%20Operation.html">
<em>Режимы работы</em></a>). Каждый блок размером 128 бит сначала шифруется алгоритмом Twofish (с ключом размером 256 бит) в режиме XTS, а затем алгоритмом AES (с ключом размером 256 бит) также в режиме XTS. Каждый из этих каскадных шифров использует свой собственный ключ. Все ключи шифрования не зависят друг от друга
 (обратите внимание, что ключи заголовка тоже независимы, хотя и получены в результате формирования одного пароля &ndash; см. раздел
<a href="Header Key Derivation.html"><em>Формирование ключа заголовка, соль и количество итераций</em></a>). Информация о каждом отдельном шифре приведена выше.</p>
<h2>AES-Twofish-Serpent</h2>
<p>Последовательно выполняемые (каскадом) [15, 16] три шифра, работающие в режиме XTS (см. раздел <a href="Modes%20of%20Operation.html">
<em>Режимы работы</em></a>). Каждый блок размером 128 бит сначала шифруется алгоритмом Serpent (с ключом размером 256 бит) в режиме XTS, затем алгоритмом Twofish (с ключом размером 256 бит) в режиме XTS, и, наконец, алгоритмом AES (с ключом размером 256 бит) в режиме XTS. Каждый из этих каскадных шифров использует свой собственный ключ.
 Все ключи шифрования не зависят друг от друга (обратите внимание, что ключи заголовка тоже независимы, хотя и получены в результате формирования одного пароля &ndash; см. раздел
<a href="Header Key Derivation.html"><em>Формирование ключа заголовка, соль и количество итераций</em></a>). Информация о каждом отдельном шифре приведена выше.</p>
<h2>Camellia-Kuznyechik</h2>
<p>Последовательно выполняемые (каскадом) [15, 16] два шифра, работающие в режиме XTS (см. раздел <a href="Modes%20of%20Operation.html">
<em>Режимы работы</em></a>). Каждый блок размером 128 бит сначала шифруется алгоритмом Kuznyechik (с ключом размером 256 бит) в режиме XTS, а затем алгоритмом Camellia (с ключом размером 256 бит) в режиме XTS. Каждый из этих каскадных шифров использует свой собственный ключ. Все ключи шифрования не зависят друг от друга
 (обратите внимание, что ключи заголовка тоже независимы, хотя и получены в результате формирования одного пароля &ndash; см. раздел
<a href="Header Key Derivation.html"><em>Формирование ключа заголовка, соль и количество итераций</em></a>). Информация о каждом отдельном шифре приведена выше.</p>
<h2>Camellia-Serpent</h2>
<p>Последовательно выполняемые (каскадом) [15, 16] два шифра, работающие в режиме XTS (см. раздел <a href="Modes%20of%20Operation.html">
<em>Режимы работы</em></a>). Каждый блок размером 128 бит сначала шифруется алгоритмом Serpent (с ключом размером 256 бит) в режиме XTS, а затем алгоритмом Camellia (с ключом размером 256 бит) в режиме XTS. Каждый из этих каскадных шифров использует свой собственный ключ. Все ключи шифрования не зависят друг от друга
 (обратите внимание, что ключи заголовка тоже независимы, хотя и получены в результате формирования одного пароля &ndash; см. раздел
<a href="Header Key Derivation.html"><em>Формирование ключа заголовка, соль и количество итераций</em></a>). Информация о каждом отдельном шифре приведена выше.</p>
<h2>Kuznyechik-AES</h2>
<p>Последовательно выполняемые (каскадом) [15, 16] два шифра, работающие в режиме XTS (см. раздел <a href="Modes%20of%20Operation.html">
<em>Режимы работы</em></a>). Каждый блок размером 128 бит сначала шифруется алгоритмом AES (с ключом размером 256 бит) в режиме XTS, а затем алгоритмом Kuznyechik (с ключом размером 256 бит) в режиме XTS. Каждый из этих каскадных шифров использует свой собственный ключ. Все ключи шифрования не зависят друг от друга
 (обратите внимание, что ключи заголовка тоже независимы, хотя и получены в результате формирования одного пароля &ndash; см. раздел
<a href="Header Key Derivation.html"><em>Формирование ключа заголовка, соль и количество итераций</em></a>). Информация о каждом отдельном шифре приведена выше.</p>
<h2>Kuznyechik-Serpent-Camellia</h2>
<p>Последовательно выполняемые (каскадом) [15, 16] три шифра, работающие в режиме XTS (см. раздел <a href="Modes%20of%20Operation.html">
<em>Режимы работы</em></a>). Каждый блок размером 128 бит сначала шифруется алгоритмом Camellia (с ключом размером 256 бит) в режиме XTS, затем алгоритмом Serpent (с ключом размером 256 бит) в режиме XTS, и, наконец, алгоритмом Kuznyechik (с ключом размером 256 бит) в режиме XTS. Каждый из этих каскадных шифров использует свой собственный ключ.
 Все ключи шифрования не зависят друг от друга (обратите внимание, что ключи заголовка тоже независимы, хотя и получены в результате формирования одного пароля &ndash; см. раздел
<a href="Header Key Derivation.html"><em>Формирование ключа заголовка, соль и количество итераций</em></a>). Информация о каждом отдельном шифре приведена выше.</p>
<h2>Kuznyechik-Twofish</h2>
<p>Последовательно выполняемые (каскадом) [15, 16] два шифра, работающие в режиме XTS (см. раздел <a href="Modes%20of%20Operation.html">
<em>Режимы работы</em></a>). Каждый блок размером 128 бит сначала шифруется алгоритмом Twofish (с ключом размером 256 бит) в режиме XTS, а затем алгоритмом Kuznyechik (с ключом размером 256 бит) в режиме XTS. Каждый из этих каскадных шифров использует свой собственный ключ. Все ключи шифрования не зависят друг от друга
 (обратите внимание, что ключи заголовка тоже независимы, хотя и получены в результате формирования одного пароля &ndash; см. раздел
<a href="Header Key Derivation.html"><em>Формирование ключа заголовка, соль и количество итераций</em></a>). Информация о каждом отдельном шифре приведена выше.</p>
<h2>Serpent-AES</h2>
<p>Последовательно выполняемые (каскадом) [15, 16] два шифра, работающие в режиме XTS (см. раздел <a href="Modes%20of%20Operation.html">
<em>Режимы работы</em></a>). Каждый блок размером 128 бит сначала шифруется алгоритмом AES (с ключом размером 256 бит) в режиме XTS, а затем алгоритмом Serpent (с ключом размером 256 бит) в режиме XTS. Каждый из этих каскадных шифров использует свой собственный ключ. Все ключи шифрования не зависят друг от друга
 (обратите внимание, что ключи заголовка тоже независимы, хотя и получены в результате формирования одного пароля &ndash; см. раздел
<a href="Header Key Derivation.html"><em>Формирование ключа заголовка, соль и количество итераций</em></a>). Информация о каждом отдельном шифре приведена выше.</p>
<h2>Serpent-Twofish-AES</h2>
<p>Последовательно выполняемые (каскадом) [15, 16] три шифра, работающие в режиме XTS (см. раздел <a href="Modes%20of%20Operation.html">
<em>Режимы работы</em></a>). Каждый блок размером 128 бит сначала шифруется алгоритмом AES (с ключом размером 256 бит) в режиме XTS, затем алгоритмом Twofish (с ключом размером 256 бит) в режиме XTS, и, наконец, алгоритмом Serpent (с ключом размером 256 бит) в режиме XTS. Каждый из этих каскадных шифров использует свой собственный ключ.
 Все ключи шифрования не зависят друг от друга (обратите внимание, что ключи заголовка тоже независимы, хотя и получены в результате формирования одного пароля &ndash; см. раздел
<a href="Header Key Derivation.html"><em>Формирование ключа заголовка, соль и количество итераций</em></a>). Информация о каждом отдельном шифре приведена выше.</p>
<h2>Twofish-Serpent</h2>
<p>Последовательно выполняемые (каскадом) [15, 16] два шифра, работающие в режиме XTS (см. раздел <a href="Modes%20of%20Operation.html">
<em>Режимы работы</em></a>). Каждый блок размером 128 бит сначала шифруется алгоритмом Serpent (с ключом размером 256 бит) в режиме XTS, а затем алгоритмом Twofish (с ключом размером 256 бит) в режиме XTS. Каждый из этих каскадных шифров использует свой собственный ключ. Все ключи шифрования не зависят друг от друга
 (обратите внимание, что ключи заголовка тоже независимы, хотя и получены в результате формирования одного пароля &ndash; см. раздел
<a href="Header Key Derivation.html"><em>Формирование ключа заголовка, соль и количество итераций</em></a>). Информация о каждом отдельном шифре приведена выше.</p>

</div>
</body></html>
