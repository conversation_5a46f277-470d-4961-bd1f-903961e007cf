<!DOCTYPE html>
<head>
<meta http-equiv="content-type" content="text/html; charset=utf-8" />
<title>VeraCrypt - Free Open source disk encryption with strong security for the Paranoid</title>
<meta name="description" content="VeraCrypt is free open-source disk encryption software for Windows, Mac OS X and Linux. In case an attacker forces you to reveal the password, VeraCrypt provides plausible deniability. In contrast to file encryption, data encryption performed by VeraCrypt is real-time (on-the-fly), automatic, transparent, needs very little memory, and does not involve temporary unencrypted files."/>
<meta name="keywords" content="encryption, security"/>
<link href="styles.css" rel="stylesheet" type="text/css" />
</head>
<body>

<div>
<a href="Documentation.html"><img src="VeraCrypt128x128.png" alt="VeraCrypt"/></a>
</div>

<div id="menu">
	<ul>
	  <li><a href="Home.html">Home</a></li>
	  <li><a href="Code.html">Source Code</a></li>
	  <li><a href="Downloads.html">Downloads</a></li>
	  <li><a class="active" href="Documentation.html">Documentation</a></li>
	  <li><a href="Donation.html">Donate</a></li>
	  <li><a href="https://sourceforge.net/p/veracrypt/discussion/" target="_blank">Forums</a></li>
	</ul>
</div>

<div>
<p>
<a href="Documentation.html">Documentation</a>
<img src="arrow_right.gif" alt=">>" style="margin-top: 5px">
<a href="Technical%20Details.html">Technical Details</a>
<img src="arrow_right.gif" alt=">>" style="margin-top: 5px">
<a href="VeraCrypt%20Volume%20Format%20Specification.html">VeraCrypt Volume Format Specification</a>
</p></div>

<div class="wikidoc">
<h1>VeraCrypt Volume Format Specification</h1>
<div style="text-align:left; margin-top:19px; margin-bottom:19px; padding-top:0px; padding-bottom:0px">
The format of file-hosted volumes is identical to the format of partition/device-hosted volumes (however, the &quot;volume header&quot;, or key data, for a system partition/drive is stored in the last 512 bytes of the first logical drive track). VeraCrypt volumes have
 no &quot;signature&quot; or ID strings. Until decrypted, they appear to consist solely of random data.</div>
<div style="text-align:left; margin-top:19px; margin-bottom:19px; padding-top:0px; padding-bottom:0px">
Free space on each VeraCrypt volume is filled with random data when the volume is created.* The random data is generated as follows: Right before VeraCrypt volume formatting begins, a temporary encryption key and a temporary secondary key (XTS mode) are generated
 by the random number generator (see the section <a href="Random%20Number%20Generator.html" style="text-align:left; color:#0080c0; text-decoration:none">
Random Number Generator</a>). The encryption algorithm that the user selected is initialized with the temporary keys. The encryption algorithm is then used to encrypt plaintext blocks consisting of random bytes generated by the random number generator. The
 encryption algorithm operates in XTS mode (see the section <a href="Hidden%20Volume.html" style="text-align:left; color:#0080c0; text-decoration:none">
Hidden Volume</a>). The resulting ciphertext blocks are used to fill (overwrite) the free space on the volume. The temporary keys are stored in RAM and are erased after formatting finishes.</div>
<p><br style="text-align:left">
VeraCrypt Volume Format Specification:</p>
<table style="border-collapse:separate; border-spacing:0px; width:608px; text-align:left; font-size:11px; line-height:13px; font-family:Verdana,Arial,Helvetica,sans-serif; border-width:0px 0px 1px 1px; border-style:solid; border-color:#ffffff #ffffff #000000 #000000">
<tbody style="text-align:left">
<tr style="text-align:left">
<th style="width:76px; font-weight:normal; text-align:center; vertical-align:middle; color:#000000; border-width:1px 1px 1px 0px; border-style:solid solid solid none; padding:9px 0px; border-color:#000000 #000000 #000000 white">
Offset (bytes)</th>
<th style="width:50px; font-weight:normal; text-align:center; vertical-align:middle; color:#000000; border-width:1px 1px 1px 0px; border-style:solid solid solid none; padding:9px 0px; border-color:#000000 #000000 #000000 white">
Size (bytes)</th>
<th style="width:98px; font-weight:normal; text-align:center; vertical-align:middle; color:#000000; border-width:1px 1px 1px 0px; border-style:solid solid solid none; padding:9px 0px; border-color:#000000 #000000 #000000 white">
Encryption<br>
Status&dagger;</th>
<th style="width:382px; font-weight:normal; text-align:center; vertical-align:middle; color:#000000; border-width:1px 1px 1px 0px; border-style:solid solid solid none; padding:9px 0px; border-color:#000000 #000000 #000000 white">
Description</th>
</tr>
<tr style="text-align:left">
<td style="color:#000000; font-size:11px; line-height:13px; font-family:Verdana,Arial,Helvetica,sans-serif; text-align:center; vertical-align:middle; border-width:0px 1px 0px 0px; border-style:none solid solid none; padding:0px 5px; border-color:white #000000 #ffffff white">
&nbsp;</td>
<td style="color:#000000; font-size:11px; line-height:13px; font-family:Verdana,Arial,Helvetica,sans-serif; text-align:center; vertical-align:middle; border-width:0px 1px 0px 0px; border-style:none solid solid none; padding:0px 5px; border-color:white #000000 #ffffff white">
&nbsp;</td>
<td style="color:#000000; font-size:11px; line-height:13px; font-family:Verdana,Arial,Helvetica,sans-serif; text-align:center; vertical-align:middle; border-width:0px 1px 0px 0px; border-style:none solid solid none; padding:0px 5px; border-color:white #000000 #ffffff white">
&nbsp;</td>
<td style="color:#000000; font-size:11px; line-height:13px; font-family:Verdana,Arial,Helvetica,sans-serif; text-align:center; vertical-align:middle; border-width:0px 1px 0px 0px; border-style:none solid solid none; padding:0px 5px; border-color:white #000000 #ffffff white">
&nbsp;</td>
</tr>
<tr style="text-align:left">
<td style="color:#000000; font-size:11px; line-height:13px; font-family:Verdana,Arial,Helvetica,sans-serif; text-align:center; vertical-align:middle; border-width:0px 1px 0px 0px; border-style:none solid solid none; padding:0px 5px; border-color:white #000000 #ffffff white">
<div align="right">0&nbsp;</div>
</td>
<td style="color:#000000; font-size:11px; line-height:13px; font-family:Verdana,Arial,Helvetica,sans-serif; text-align:center; vertical-align:middle; border-width:0px 1px 0px 0px; border-style:none solid solid none; padding:0px 5px; border-color:white #000000 #ffffff white">
<div align="right">64&nbsp;</div>
</td>
<td style="color:#000000; font-size:11px; line-height:13px; font-family:Verdana,Arial,Helvetica,sans-serif; text-align:center; vertical-align:middle; border-width:0px 1px 0px 0px; border-style:none solid solid none; padding:0px 5px; border-color:white #000000 #ffffff white">
<div align="left">&nbsp;Unencrypted&sect;</div>
</td>
<td style="color:#000000; font-size:11px; line-height:13px; font-family:Verdana,Arial,Helvetica,sans-serif; text-align:center; vertical-align:middle; border-width:0px 1px 0px 0px; border-style:none solid solid none; padding:0px 5px; border-color:white #000000 #ffffff white">
<div align="left">&nbsp;Salt</div>
</td>
</tr>
<tr style="text-align:left">
<td style="color:#000000; font-size:11px; line-height:13px; font-family:Verdana,Arial,Helvetica,sans-serif; text-align:center; vertical-align:middle; border-width:0px 1px 0px 0px; border-style:none solid solid none; padding:0px 5px; border-color:white #000000 #ffffff white">
&nbsp;</td>
<td style="color:#000000; font-size:11px; line-height:13px; font-family:Verdana,Arial,Helvetica,sans-serif; text-align:center; vertical-align:middle; border-width:0px 1px 0px 0px; border-style:none solid solid none; padding:0px 5px; border-color:white #000000 #ffffff white">
&nbsp;</td>
<td style="color:#000000; font-size:11px; line-height:13px; font-family:Verdana,Arial,Helvetica,sans-serif; text-align:center; vertical-align:middle; border-width:0px 1px 0px 0px; border-style:none solid solid none; padding:0px 5px; border-color:white #000000 #ffffff white">
&nbsp;</td>
<td style="color:#000000; font-size:11px; line-height:13px; font-family:Verdana,Arial,Helvetica,sans-serif; text-align:center; vertical-align:middle; border-width:0px 1px 0px 0px; border-style:none solid solid none; padding:0px 5px; border-color:white #000000 #ffffff white">
&nbsp;</td>
</tr>
<tr style="text-align:left">
<td style="color:#000000; font-size:11px; line-height:13px; font-family:Verdana,Arial,Helvetica,sans-serif; text-align:center; vertical-align:middle; border-width:0px 1px 0px 0px; border-style:none solid solid none; padding:0px 5px; border-color:white #000000 #ffffff white">
<div align="right">64&nbsp;</div>
</td>
<td style="color:#000000; font-size:11px; line-height:13px; font-family:Verdana,Arial,Helvetica,sans-serif; text-align:center; vertical-align:middle; border-width:0px 1px 0px 0px; border-style:none solid solid none; padding:0px 5px; border-color:white #000000 #ffffff white">
<div align="right">4&nbsp;</div>
</td>
<td style="color:#000000; font-size:11px; line-height:13px; font-family:Verdana,Arial,Helvetica,sans-serif; text-align:center; vertical-align:middle; border-width:0px 1px 0px 0px; border-style:none solid solid none; padding:0px 5px; border-color:white #000000 #ffffff white">
<div align="left">&nbsp;Encrypted</div>
</td>
<td style="color:#000000; font-size:11px; line-height:13px; font-family:Verdana,Arial,Helvetica,sans-serif; text-align:center; vertical-align:middle; border-width:0px 1px 0px 0px; border-style:none solid solid none; padding:0px 5px; border-color:white #000000 #ffffff white">
<div align="left">&nbsp;ASCII string &quot;VERA&quot;</div>
</td>
</tr>
<tr style="text-align:left">
<td style="color:#000000; font-size:11px; line-height:13px; font-family:Verdana,Arial,Helvetica,sans-serif; text-align:center; vertical-align:middle; border-width:0px 1px 0px 0px; border-style:none solid solid none; padding:0px 5px; border-color:white #000000 #ffffff white">
&nbsp;</td>
<td style="color:#000000; font-size:11px; line-height:13px; font-family:Verdana,Arial,Helvetica,sans-serif; text-align:center; vertical-align:middle; border-width:0px 1px 0px 0px; border-style:none solid solid none; padding:0px 5px; border-color:white #000000 #ffffff white">
&nbsp;</td>
<td style="color:#000000; font-size:11px; line-height:13px; font-family:Verdana,Arial,Helvetica,sans-serif; text-align:center; vertical-align:middle; border-width:0px 1px 0px 0px; border-style:none solid solid none; padding:0px 5px; border-color:white #000000 #ffffff white">
&nbsp;</td>
<td style="color:#000000; font-size:11px; line-height:13px; font-family:Verdana,Arial,Helvetica,sans-serif; text-align:center; vertical-align:middle; border-width:0px 1px 0px 0px; border-style:none solid solid none; padding:0px 5px; border-color:white #000000 #ffffff white">
&nbsp;</td>
</tr>
<tr style="text-align:left">
<td style="color:#000000; font-size:11px; line-height:13px; font-family:Verdana,Arial,Helvetica,sans-serif; text-align:center; vertical-align:middle; border-width:0px 1px 0px 0px; border-style:none solid solid none; padding:0px 5px; border-color:white #000000 #ffffff white">
<div align="right">68&nbsp;</div>
</td>
<td style="color:#000000; font-size:11px; line-height:13px; font-family:Verdana,Arial,Helvetica,sans-serif; text-align:center; vertical-align:middle; border-width:0px 1px 0px 0px; border-style:none solid solid none; padding:0px 5px; border-color:white #000000 #ffffff white">
<div align="right">2&nbsp;</div>
</td>
<td style="color:#000000; font-size:11px; line-height:13px; font-family:Verdana,Arial,Helvetica,sans-serif; text-align:center; vertical-align:middle; border-width:0px 1px 0px 0px; border-style:none solid solid none; padding:0px 5px; border-color:white #000000 #ffffff white">
<div align="left">&nbsp;Encrypted</div>
</td>
<td style="color:#000000; font-size:11px; line-height:13px; font-family:Verdana,Arial,Helvetica,sans-serif; text-align:center; vertical-align:middle; border-width:0px 1px 0px 0px; border-style:none solid solid none; padding:0px 5px; border-color:white #000000 #ffffff white">
<div align="left">&nbsp;Volume header format version (2)</div>
</td>
</tr>
<tr style="text-align:left">
<td style="color:#000000; font-size:11px; line-height:13px; font-family:Verdana,Arial,Helvetica,sans-serif; text-align:center; vertical-align:middle; border-width:0px 1px 0px 0px; border-style:none solid solid none; padding:0px 5px; border-color:white #000000 #ffffff white">
&nbsp;</td>
<td style="color:#000000; font-size:11px; line-height:13px; font-family:Verdana,Arial,Helvetica,sans-serif; text-align:center; vertical-align:middle; border-width:0px 1px 0px 0px; border-style:none solid solid none; padding:0px 5px; border-color:white #000000 #ffffff white">
&nbsp;</td>
<td style="color:#000000; font-size:11px; line-height:13px; font-family:Verdana,Arial,Helvetica,sans-serif; text-align:center; vertical-align:middle; border-width:0px 1px 0px 0px; border-style:none solid solid none; padding:0px 5px; border-color:white #000000 #ffffff white">
&nbsp;</td>
<td style="color:#000000; font-size:11px; line-height:13px; font-family:Verdana,Arial,Helvetica,sans-serif; text-align:center; vertical-align:middle; border-width:0px 1px 0px 0px; border-style:none solid solid none; padding:0px 5px; border-color:white #000000 #ffffff white">
&nbsp;</td>
</tr>
<tr style="text-align:left">
<td style="color:#000000; font-size:11px; line-height:13px; font-family:Verdana,Arial,Helvetica,sans-serif; text-align:center; vertical-align:middle; border-width:0px 1px 0px 0px; border-style:none solid solid none; padding:0px 5px; border-color:white #000000 #ffffff white">
<div align="right">70&nbsp;</div>
</td>
<td style="color:#000000; font-size:11px; line-height:13px; font-family:Verdana,Arial,Helvetica,sans-serif; text-align:center; vertical-align:middle; border-width:0px 1px 0px 0px; border-style:none solid solid none; padding:0px 5px; border-color:white #000000 #ffffff white">
<div align="right">2&nbsp;</div>
</td>
<td style="color:#000000; font-size:11px; line-height:13px; font-family:Verdana,Arial,Helvetica,sans-serif; text-align:center; vertical-align:middle; border-width:0px 1px 0px 0px; border-style:none solid solid none; padding:0px 5px; border-color:white #000000 #ffffff white">
<div align="left">&nbsp;Encrypted</div>
</td>
<td style="color:#000000; font-size:11px; line-height:13px; font-family:Verdana,Arial,Helvetica,sans-serif; text-align:center; vertical-align:middle; border-width:0px 1px 0px 0px; border-style:none solid solid none; padding:0px 5px; border-color:white #000000 #ffffff white">
<div align="left">&nbsp;Minimum program version required to open the volume</div>
</td>
</tr>
<tr style="text-align:left">
<td style="color:#000000; font-size:11px; line-height:13px; font-family:Verdana,Arial,Helvetica,sans-serif; text-align:center; vertical-align:middle; border-width:0px 1px 0px 0px; border-style:none solid solid none; padding:0px 5px; border-color:white #000000 #ffffff white">
&nbsp;</td>
<td style="color:#000000; font-size:11px; line-height:13px; font-family:Verdana,Arial,Helvetica,sans-serif; text-align:center; vertical-align:middle; border-width:0px 1px 0px 0px; border-style:none solid solid none; padding:0px 5px; border-color:white #000000 #ffffff white">
&nbsp;</td>
<td style="color:#000000; font-size:11px; line-height:13px; font-family:Verdana,Arial,Helvetica,sans-serif; text-align:center; vertical-align:middle; border-width:0px 1px 0px 0px; border-style:none solid solid none; padding:0px 5px; border-color:white #000000 #ffffff white">
&nbsp;</td>
<td style="color:#000000; font-size:11px; line-height:13px; font-family:Verdana,Arial,Helvetica,sans-serif; text-align:center; vertical-align:middle; border-width:0px 1px 0px 0px; border-style:none solid solid none; padding:0px 5px; border-color:white #000000 #ffffff white">
&nbsp;</td>
</tr>
<tr style="text-align:left">
<td style="color:#000000; font-size:11px; line-height:13px; font-family:Verdana,Arial,Helvetica,sans-serif; text-align:center; vertical-align:middle; border-width:0px 1px 0px 0px; border-style:none solid solid none; padding:0px 5px; border-color:white #000000 #ffffff white">
<div align="right">72&nbsp;</div>
</td>
<td style="color:#000000; font-size:11px; line-height:13px; font-family:Verdana,Arial,Helvetica,sans-serif; text-align:center; vertical-align:middle; border-width:0px 1px 0px 0px; border-style:none solid solid none; padding:0px 5px; border-color:white #000000 #ffffff white">
<div align="right">4&nbsp;</div>
</td>
<td style="color:#000000; font-size:11px; line-height:13px; font-family:Verdana,Arial,Helvetica,sans-serif; text-align:center; vertical-align:middle; border-width:0px 1px 0px 0px; border-style:none solid solid none; padding:0px 5px; border-color:white #000000 #ffffff white">
<div align="left">&nbsp;Encrypted</div>
</td>
<td style="color:#000000; font-size:11px; line-height:13px; font-family:Verdana,Arial,Helvetica,sans-serif; text-align:center; vertical-align:middle; border-width:0px 1px 0px 0px; border-style:none solid solid none; padding:0px 5px; border-color:white #000000 #ffffff white">
<div align="left">&nbsp;CRC-32 checksum of the (decrypted) bytes 256-511</div>
</td>
</tr>
<tr style="text-align:left">
<td style="color:#000000; font-size:11px; line-height:13px; font-family:Verdana,Arial,Helvetica,sans-serif; text-align:center; vertical-align:middle; border-width:0px 1px 0px 0px; border-style:none solid solid none; padding:0px 5px; border-color:white #000000 #ffffff white">
&nbsp;</td>
<td style="color:#000000; font-size:11px; line-height:13px; font-family:Verdana,Arial,Helvetica,sans-serif; text-align:center; vertical-align:middle; border-width:0px 1px 0px 0px; border-style:none solid solid none; padding:0px 5px; border-color:white #000000 #ffffff white">
&nbsp;</td>
<td style="color:#000000; font-size:11px; line-height:13px; font-family:Verdana,Arial,Helvetica,sans-serif; text-align:center; vertical-align:middle; border-width:0px 1px 0px 0px; border-style:none solid solid none; padding:0px 5px; border-color:white #000000 #ffffff white">
&nbsp;</td>
<td style="color:#000000; font-size:11px; line-height:13px; font-family:Verdana,Arial,Helvetica,sans-serif; text-align:center; vertical-align:middle; border-width:0px 1px 0px 0px; border-style:none solid solid none; padding:0px 5px; border-color:white #000000 #ffffff white">
&nbsp;</td>
</tr>
<tr style="text-align:left">
<td style="color:#000000; font-size:11px; line-height:13px; font-family:Verdana,Arial,Helvetica,sans-serif; text-align:center; vertical-align:middle; border-width:0px 1px 0px 0px; border-style:none solid solid none; padding:0px 5px; border-color:white #000000 #ffffff white">
<div align="right">76&nbsp;</div>
</td>
<td style="color:#000000; font-size:11px; line-height:13px; font-family:Verdana,Arial,Helvetica,sans-serif; text-align:center; vertical-align:middle; border-width:0px 1px 0px 0px; border-style:none solid solid none; padding:0px 5px; border-color:white #000000 #ffffff white">
<div align="right">16&nbsp;</div>
</td>
<td style="color:#000000; font-size:11px; line-height:13px; font-family:Verdana,Arial,Helvetica,sans-serif; text-align:center; vertical-align:middle; border-width:0px 1px 0px 0px; border-style:none solid solid none; padding:0px 5px; border-color:white #000000 #ffffff white">
<div align="left">&nbsp;Encrypted</div>
</td>
<td style="color:#000000; font-size:11px; line-height:13px; font-family:Verdana,Arial,Helvetica,sans-serif; text-align:center; vertical-align:middle; border-width:0px 1px 0px 0px; border-style:none solid solid none; padding:0px 5px; border-color:white #000000 #ffffff white">
<div align="left">&nbsp;Reserved (must contain zeroes)</div>
</td>
</tr>
<tr style="text-align:left">
<td style="color:#000000; font-size:11px; line-height:13px; font-family:Verdana,Arial,Helvetica,sans-serif; text-align:center; vertical-align:middle; border-width:0px 1px 0px 0px; border-style:none solid solid none; padding:0px 5px; border-color:white #000000 #ffffff white">
&nbsp;</td>
<td style="color:#000000; font-size:11px; line-height:13px; font-family:Verdana,Arial,Helvetica,sans-serif; text-align:center; vertical-align:middle; border-width:0px 1px 0px 0px; border-style:none solid solid none; padding:0px 5px; border-color:white #000000 #ffffff white">
&nbsp;</td>
<td style="color:#000000; font-size:11px; line-height:13px; font-family:Verdana,Arial,Helvetica,sans-serif; text-align:center; vertical-align:middle; border-width:0px 1px 0px 0px; border-style:none solid solid none; padding:0px 5px; border-color:white #000000 #ffffff white">
&nbsp;</td>
<td style="color:#000000; font-size:11px; line-height:13px; font-family:Verdana,Arial,Helvetica,sans-serif; text-align:center; vertical-align:middle; border-width:0px 1px 0px 0px; border-style:none solid solid none; padding:0px 5px; border-color:white #000000 #ffffff white">
&nbsp;</td>
</tr>
<tr style="text-align:left">
<td style="color:#000000; font-size:11px; line-height:13px; font-family:Verdana,Arial,Helvetica,sans-serif; text-align:center; vertical-align:middle; border-width:0px 1px 0px 0px; border-style:none solid solid none; padding:0px 5px; border-color:white #000000 #ffffff white">
<div align="right">92&nbsp;</div>
</td>
<td style="color:#000000; font-size:11px; line-height:13px; font-family:Verdana,Arial,Helvetica,sans-serif; text-align:center; vertical-align:middle; border-width:0px 1px 0px 0px; border-style:none solid solid none; padding:0px 5px; border-color:white #000000 #ffffff white">
<div align="right">8&nbsp;</div>
</td>
<td style="color:#000000; font-size:11px; line-height:13px; font-family:Verdana,Arial,Helvetica,sans-serif; text-align:center; vertical-align:middle; border-width:0px 1px 0px 0px; border-style:none solid solid none; padding:0px 5px; border-color:white #000000 #ffffff white">
<div align="left">&nbsp;Encrypted</div>
</td>
<td style="color:#000000; font-size:11px; line-height:13px; font-family:Verdana,Arial,Helvetica,sans-serif; text-align:center; vertical-align:middle; border-width:0px 1px 0px 0px; border-style:none solid solid none; padding:0px 5px; border-color:white #000000 #ffffff white">
<div align="left">&nbsp;Size of hidden volume (set to zero in non-hidden volumes)</div>
</td>
</tr>
<tr style="text-align:left">
<td style="color:#000000; font-size:11px; line-height:13px; font-family:Verdana,Arial,Helvetica,sans-serif; text-align:center; vertical-align:middle; border-width:0px 1px 0px 0px; border-style:none solid solid none; padding:0px 5px; border-color:white #000000 #ffffff white">
&nbsp;</td>
<td style="color:#000000; font-size:11px; line-height:13px; font-family:Verdana,Arial,Helvetica,sans-serif; text-align:center; vertical-align:middle; border-width:0px 1px 0px 0px; border-style:none solid solid none; padding:0px 5px; border-color:white #000000 #ffffff white">
&nbsp;</td>
<td style="color:#000000; font-size:11px; line-height:13px; font-family:Verdana,Arial,Helvetica,sans-serif; text-align:center; vertical-align:middle; border-width:0px 1px 0px 0px; border-style:none solid solid none; padding:0px 5px; border-color:white #000000 #ffffff white">
&nbsp;</td>
<td style="color:#000000; font-size:11px; line-height:13px; font-family:Verdana,Arial,Helvetica,sans-serif; text-align:center; vertical-align:middle; border-width:0px 1px 0px 0px; border-style:none solid solid none; padding:0px 5px; border-color:white #000000 #ffffff white">
&nbsp;</td>
</tr>
<tr style="text-align:left">
<td style="color:#000000; font-size:11px; line-height:13px; font-family:Verdana,Arial,Helvetica,sans-serif; text-align:center; vertical-align:middle; border-width:0px 1px 0px 0px; border-style:none solid solid none; padding:0px 5px; border-color:white #000000 #ffffff white">
<div align="right">100&nbsp;</div>
</td>
<td style="color:#000000; font-size:11px; line-height:13px; font-family:Verdana,Arial,Helvetica,sans-serif; text-align:center; vertical-align:middle; border-width:0px 1px 0px 0px; border-style:none solid solid none; padding:0px 5px; border-color:white #000000 #ffffff white">
<div align="right">8&nbsp;</div>
</td>
<td style="color:#000000; font-size:11px; line-height:13px; font-family:Verdana,Arial,Helvetica,sans-serif; text-align:center; vertical-align:middle; border-width:0px 1px 0px 0px; border-style:none solid solid none; padding:0px 5px; border-color:white #000000 #ffffff white">
<div align="left">&nbsp;Encrypted</div>
</td>
<td style="color:#000000; font-size:11px; line-height:13px; font-family:Verdana,Arial,Helvetica,sans-serif; text-align:center; vertical-align:middle; border-width:0px 1px 0px 0px; border-style:none solid solid none; padding:0px 5px; border-color:white #000000 #ffffff white">
<div align="left">&nbsp;Size of volume</div>
</td>
</tr>
<tr style="text-align:left">
<td style="color:#000000; font-size:11px; line-height:13px; font-family:Verdana,Arial,Helvetica,sans-serif; text-align:center; vertical-align:middle; border-width:0px 1px 0px 0px; border-style:none solid solid none; padding:0px 5px; border-color:white #000000 #ffffff white">
&nbsp;</td>
<td style="color:#000000; font-size:11px; line-height:13px; font-family:Verdana,Arial,Helvetica,sans-serif; text-align:center; vertical-align:middle; border-width:0px 1px 0px 0px; border-style:none solid solid none; padding:0px 5px; border-color:white #000000 #ffffff white">
&nbsp;</td>
<td style="color:#000000; font-size:11px; line-height:13px; font-family:Verdana,Arial,Helvetica,sans-serif; text-align:center; vertical-align:middle; border-width:0px 1px 0px 0px; border-style:none solid solid none; padding:0px 5px; border-color:white #000000 #ffffff white">
&nbsp;</td>
<td style="color:#000000; font-size:11px; line-height:13px; font-family:Verdana,Arial,Helvetica,sans-serif; text-align:center; vertical-align:middle; border-width:0px 1px 0px 0px; border-style:none solid solid none; padding:0px 5px; border-color:white #000000 #ffffff white">
&nbsp;</td>
</tr>
<tr style="text-align:left">
<td style="color:#000000; font-size:11px; line-height:13px; font-family:Verdana,Arial,Helvetica,sans-serif; text-align:center; vertical-align:middle; border-width:0px 1px 0px 0px; border-style:none solid solid none; padding:0px 5px; border-color:white #000000 #ffffff white">
<div align="right">108&nbsp;</div>
</td>
<td style="color:#000000; font-size:11px; line-height:13px; font-family:Verdana,Arial,Helvetica,sans-serif; text-align:center; vertical-align:middle; border-width:0px 1px 0px 0px; border-style:none solid solid none; padding:0px 5px; border-color:white #000000 #ffffff white">
<div align="right">8&nbsp;</div>
</td>
<td style="color:#000000; font-size:11px; line-height:13px; font-family:Verdana,Arial,Helvetica,sans-serif; text-align:center; vertical-align:middle; border-width:0px 1px 0px 0px; border-style:none solid solid none; padding:0px 5px; border-color:white #000000 #ffffff white">
<div align="left">&nbsp;Encrypted</div>
</td>
<td style="color:#000000; font-size:11px; line-height:13px; font-family:Verdana,Arial,Helvetica,sans-serif; text-align:center; vertical-align:middle; border-width:0px 1px 0px 0px; border-style:none solid solid none; padding:0px 5px; border-color:white #000000 #ffffff white">
<div align="left">&nbsp;Byte offset of the start of the master key scope</div>
</td>
</tr>
<tr style="text-align:left">
<td style="color:#000000; font-size:11px; line-height:13px; font-family:Verdana,Arial,Helvetica,sans-serif; text-align:center; vertical-align:middle; border-width:0px 1px 0px 0px; border-style:none solid solid none; padding:0px 5px; border-color:white #000000 #ffffff white">
&nbsp;</td>
<td style="color:#000000; font-size:11px; line-height:13px; font-family:Verdana,Arial,Helvetica,sans-serif; text-align:center; vertical-align:middle; border-width:0px 1px 0px 0px; border-style:none solid solid none; padding:0px 5px; border-color:white #000000 #ffffff white">
&nbsp;</td>
<td style="color:#000000; font-size:11px; line-height:13px; font-family:Verdana,Arial,Helvetica,sans-serif; text-align:center; vertical-align:middle; border-width:0px 1px 0px 0px; border-style:none solid solid none; padding:0px 5px; border-color:white #000000 #ffffff white">
&nbsp;</td>
<td style="color:#000000; font-size:11px; line-height:13px; font-family:Verdana,Arial,Helvetica,sans-serif; text-align:center; vertical-align:middle; border-width:0px 1px 0px 0px; border-style:none solid solid none; padding:0px 5px; border-color:white #000000 #ffffff white">
&nbsp;</td>
</tr>
<tr style="text-align:left">
<td style="color:#000000; font-size:11px; line-height:13px; font-family:Verdana,Arial,Helvetica,sans-serif; text-align:center; vertical-align:middle; border-width:0px 1px 0px 0px; border-style:none solid solid none; padding:0px 5px; border-color:white #000000 #ffffff white">
<div align="right">116&nbsp;</div>
</td>
<td style="color:#000000; font-size:11px; line-height:13px; font-family:Verdana,Arial,Helvetica,sans-serif; text-align:center; vertical-align:middle; border-width:0px 1px 0px 0px; border-style:none solid solid none; padding:0px 5px; border-color:white #000000 #ffffff white">
<div align="right">8&nbsp;</div>
</td>
<td style="color:#000000; font-size:11px; line-height:13px; font-family:Verdana,Arial,Helvetica,sans-serif; text-align:center; vertical-align:middle; border-width:0px 1px 0px 0px; border-style:none solid solid none; padding:0px 5px; border-color:white #000000 #ffffff white">
<div align="left">&nbsp;Encrypted</div>
</td>
<td style="color:#000000; font-size:11px; line-height:13px; font-family:Verdana,Arial,Helvetica,sans-serif; text-align:center; vertical-align:middle; border-width:0px 1px 0px 0px; border-style:none solid solid none; padding:0px 5px; border-color:white #000000 #ffffff white">
<div align="left">&nbsp;Size of the encrypted area within the master key scope</div>
</td>
</tr>
<tr style="text-align:left">
<td style="color:#000000; font-size:11px; line-height:13px; font-family:Verdana,Arial,Helvetica,sans-serif; text-align:center; vertical-align:middle; border-width:0px 1px 0px 0px; border-style:none solid solid none; padding:0px 5px; border-color:white #000000 #ffffff white">
&nbsp;</td>
<td style="color:#000000; font-size:11px; line-height:13px; font-family:Verdana,Arial,Helvetica,sans-serif; text-align:center; vertical-align:middle; border-width:0px 1px 0px 0px; border-style:none solid solid none; padding:0px 5px; border-color:white #000000 #ffffff white">
&nbsp;</td>
<td style="color:#000000; font-size:11px; line-height:13px; font-family:Verdana,Arial,Helvetica,sans-serif; text-align:center; vertical-align:middle; border-width:0px 1px 0px 0px; border-style:none solid solid none; padding:0px 5px; border-color:white #000000 #ffffff white">
&nbsp;</td>
<td style="color:#000000; font-size:11px; line-height:13px; font-family:Verdana,Arial,Helvetica,sans-serif; text-align:center; vertical-align:middle; border-width:0px 1px 0px 0px; border-style:none solid solid none; padding:0px 5px; border-color:white #000000 #ffffff white">
&nbsp;</td>
</tr>
<tr style="text-align:left">
<td style="color:#000000; font-size:11px; line-height:13px; font-family:Verdana,Arial,Helvetica,sans-serif; text-align:center; vertical-align:middle; border-width:0px 1px 0px 0px; border-style:none solid solid none; padding:0px 5px; border-color:white #000000 #ffffff white">
<div align="right">124&nbsp;</div>
</td>
<td style="color:#000000; font-size:11px; line-height:13px; font-family:Verdana,Arial,Helvetica,sans-serif; text-align:center; vertical-align:middle; border-width:0px 1px 0px 0px; border-style:none solid solid none; padding:0px 5px; border-color:white #000000 #ffffff white">
<div align="right">4&nbsp;</div>
</td>
<td style="color:#000000; font-size:11px; line-height:13px; font-family:Verdana,Arial,Helvetica,sans-serif; text-align:center; vertical-align:middle; border-width:0px 1px 0px 0px; border-style:none solid solid none; padding:0px 5px; border-color:white #000000 #ffffff white">
<div align="left">&nbsp;Encrypted</div>
</td>
<td style="color:#000000; font-size:11px; line-height:13px; font-family:Verdana,Arial,Helvetica,sans-serif; text-align:center; vertical-align:middle; border-width:0px 1px 0px 0px; border-style:none solid solid none; padding:0px 5px; border-color:white #000000 #ffffff white">
<div align="left">&nbsp;Flag bits (bit 0 set: system encryption; bit 1 set: non-system</div>
</td>
</tr>
<tr style="text-align:left">
<td style="color:#000000; font-size:11px; line-height:13px; font-family:Verdana,Arial,Helvetica,sans-serif; text-align:center; vertical-align:middle; border-width:0px 1px 0px 0px; border-style:none solid solid none; padding:0px 5px; border-color:white #000000 #ffffff white">
<div align="right">&nbsp;</div>
</td>
<td style="color:#000000; font-size:11px; line-height:13px; font-family:Verdana,Arial,Helvetica,sans-serif; text-align:center; vertical-align:middle; border-width:0px 1px 0px 0px; border-style:none solid solid none; padding:0px 5px; border-color:white #000000 #ffffff white">
<div align="right">&nbsp;</div>
</td>
<td style="color:#000000; font-size:11px; line-height:13px; font-family:Verdana,Arial,Helvetica,sans-serif; text-align:center; vertical-align:middle; border-width:0px 1px 0px 0px; border-style:none solid solid none; padding:0px 5px; border-color:white #000000 #ffffff white">
<div align="left">&nbsp;</div>
</td>
<td style="color:#000000; font-size:11px; line-height:13px; font-family:Verdana,Arial,Helvetica,sans-serif; text-align:center; vertical-align:middle; border-width:0px 1px 0px 0px; border-style:none solid solid none; padding:0px 5px; border-color:white #000000 #ffffff white">
<div align="left">&nbsp;in-place-encrypted/decrypted volume; bits 2&ndash;31 are reserved)</div>
</td>
</tr>
<tr style="text-align:left">
<td style="color:#000000; font-size:11px; line-height:13px; font-family:Verdana,Arial,Helvetica,sans-serif; text-align:center; vertical-align:middle; border-width:0px 1px 0px 0px; border-style:none solid solid none; padding:0px 5px; border-color:white #000000 #ffffff white">
&nbsp;</td>
<td style="color:#000000; font-size:11px; line-height:13px; font-family:Verdana,Arial,Helvetica,sans-serif; text-align:center; vertical-align:middle; border-width:0px 1px 0px 0px; border-style:none solid solid none; padding:0px 5px; border-color:white #000000 #ffffff white">
&nbsp;</td>
<td style="color:#000000; font-size:11px; line-height:13px; font-family:Verdana,Arial,Helvetica,sans-serif; text-align:center; vertical-align:middle; border-width:0px 1px 0px 0px; border-style:none solid solid none; padding:0px 5px; border-color:white #000000 #ffffff white">
&nbsp;</td>
<td style="color:#000000; font-size:11px; line-height:13px; font-family:Verdana,Arial,Helvetica,sans-serif; text-align:center; vertical-align:middle; border-width:0px 1px 0px 0px; border-style:none solid solid none; padding:0px 5px; border-color:white #000000 #ffffff white">
&nbsp;</td>
</tr>
<tr style="text-align:left">
<td style="color:#000000; font-size:11px; line-height:13px; font-family:Verdana,Arial,Helvetica,sans-serif; text-align:center; vertical-align:middle; border-width:0px 1px 0px 0px; border-style:none solid solid none; padding:0px 5px; border-color:white #000000 #ffffff white">
<div align="right">128&nbsp;</div>
</td>
<td style="color:#000000; font-size:11px; line-height:13px; font-family:Verdana,Arial,Helvetica,sans-serif; text-align:center; vertical-align:middle; border-width:0px 1px 0px 0px; border-style:none solid solid none; padding:0px 5px; border-color:white #000000 #ffffff white">
<div align="right">4&nbsp;</div>
</td>
<td style="color:#000000; font-size:11px; line-height:13px; font-family:Verdana,Arial,Helvetica,sans-serif; text-align:center; vertical-align:middle; border-width:0px 1px 0px 0px; border-style:none solid solid none; padding:0px 5px; border-color:white #000000 #ffffff white">
<div align="left">&nbsp;Encrypted</div>
</td>
<td style="color:#000000; font-size:11px; line-height:13px; font-family:Verdana,Arial,Helvetica,sans-serif; text-align:center; vertical-align:middle; border-width:0px 1px 0px 0px; border-style:none solid solid none; padding:0px 5px; border-color:white #000000 #ffffff white">
<div align="left">&nbsp;Sector size (in bytes)</div>
</td>
</tr>
<tr style="text-align:left">
<td style="color:#000000; font-size:11px; line-height:13px; font-family:Verdana,Arial,Helvetica,sans-serif; text-align:center; vertical-align:middle; border-width:0px 1px 0px 0px; border-style:none solid solid none; padding:0px 5px; border-color:white #000000 #ffffff white">
&nbsp;</td>
<td style="color:#000000; font-size:11px; line-height:13px; font-family:Verdana,Arial,Helvetica,sans-serif; text-align:center; vertical-align:middle; border-width:0px 1px 0px 0px; border-style:none solid solid none; padding:0px 5px; border-color:white #000000 #ffffff white">
&nbsp;</td>
<td style="color:#000000; font-size:11px; line-height:13px; font-family:Verdana,Arial,Helvetica,sans-serif; text-align:center; vertical-align:middle; border-width:0px 1px 0px 0px; border-style:none solid solid none; padding:0px 5px; border-color:white #000000 #ffffff white">
&nbsp;</td>
<td style="color:#000000; font-size:11px; line-height:13px; font-family:Verdana,Arial,Helvetica,sans-serif; text-align:center; vertical-align:middle; border-width:0px 1px 0px 0px; border-style:none solid solid none; padding:0px 5px; border-color:white #000000 #ffffff white">
&nbsp;</td>
</tr>
<tr style="text-align:left">
<td style="color:#000000; font-size:11px; line-height:13px; font-family:Verdana,Arial,Helvetica,sans-serif; text-align:center; vertical-align:middle; border-width:0px 1px 0px 0px; border-style:none solid solid none; padding:0px 5px; border-color:white #000000 #ffffff white">
<div align="right">132&nbsp;</div>
</td>
<td style="color:#000000; font-size:11px; line-height:13px; font-family:Verdana,Arial,Helvetica,sans-serif; text-align:center; vertical-align:middle; border-width:0px 1px 0px 0px; border-style:none solid solid none; padding:0px 5px; border-color:white #000000 #ffffff white">
<div align="right">120&nbsp;</div>
</td>
<td style="color:#000000; font-size:11px; line-height:13px; font-family:Verdana,Arial,Helvetica,sans-serif; text-align:center; vertical-align:middle; border-width:0px 1px 0px 0px; border-style:none solid solid none; padding:0px 5px; border-color:white #000000 #ffffff white">
<div align="left">&nbsp;Encrypted</div>
</td>
<td style="color:#000000; font-size:11px; line-height:13px; font-family:Verdana,Arial,Helvetica,sans-serif; text-align:center; vertical-align:middle; border-width:0px 1px 0px 0px; border-style:none solid solid none; padding:0px 5px; border-color:white #000000 #ffffff white">
<div align="left">&nbsp;Reserved (must contain zeroes)</div>
</td>
</tr>
<tr style="text-align:left">
<td style="color:#000000; font-size:11px; line-height:13px; font-family:Verdana,Arial,Helvetica,sans-serif; text-align:center; vertical-align:middle; border-width:0px 1px 0px 0px; border-style:none solid solid none; padding:0px 5px; border-color:white #000000 #ffffff white">
&nbsp;</td>
<td style="color:#000000; font-size:11px; line-height:13px; font-family:Verdana,Arial,Helvetica,sans-serif; text-align:center; vertical-align:middle; border-width:0px 1px 0px 0px; border-style:none solid solid none; padding:0px 5px; border-color:white #000000 #ffffff white">
&nbsp;</td>
<td style="color:#000000; font-size:11px; line-height:13px; font-family:Verdana,Arial,Helvetica,sans-serif; text-align:center; vertical-align:middle; border-width:0px 1px 0px 0px; border-style:none solid solid none; padding:0px 5px; border-color:white #000000 #ffffff white">
&nbsp;</td>
<td style="color:#000000; font-size:11px; line-height:13px; font-family:Verdana,Arial,Helvetica,sans-serif; text-align:center; vertical-align:middle; border-width:0px 1px 0px 0px; border-style:none solid solid none; padding:0px 5px; border-color:white #000000 #ffffff white">
&nbsp;</td>
</tr>
<tr style="text-align:left">
<td style="color:#000000; font-size:11px; line-height:13px; font-family:Verdana,Arial,Helvetica,sans-serif; text-align:center; vertical-align:middle; border-width:0px 1px 0px 0px; border-style:none solid solid none; padding:0px 5px; border-color:white #000000 #ffffff white">
<div align="right">252&nbsp;</div>
</td>
<td style="color:#000000; font-size:11px; line-height:13px; font-family:Verdana,Arial,Helvetica,sans-serif; text-align:center; vertical-align:middle; border-width:0px 1px 0px 0px; border-style:none solid solid none; padding:0px 5px; border-color:white #000000 #ffffff white">
<div align="right">4&nbsp;</div>
</td>
<td style="color:#000000; font-size:11px; line-height:13px; font-family:Verdana,Arial,Helvetica,sans-serif; text-align:center; vertical-align:middle; border-width:0px 1px 0px 0px; border-style:none solid solid none; padding:0px 5px; border-color:white #000000 #ffffff white">
<div align="left">&nbsp;Encrypted</div>
</td>
<td style="color:#000000; font-size:11px; line-height:13px; font-family:Verdana,Arial,Helvetica,sans-serif; text-align:center; vertical-align:middle; border-width:0px 1px 0px 0px; border-style:none solid solid none; padding:0px 5px; border-color:white #000000 #ffffff white">
<div align="left">&nbsp;CRC-32 checksum of the (decrypted) bytes 64-251</div>
</td>
</tr>
<tr style="text-align:left">
<td style="color:#000000; font-size:11px; line-height:13px; font-family:Verdana,Arial,Helvetica,sans-serif; text-align:center; vertical-align:middle; border-width:0px 1px 0px 0px; border-style:none solid solid none; padding:0px 5px; border-color:white #000000 #ffffff white">
&nbsp;</td>
<td style="color:#000000; font-size:11px; line-height:13px; font-family:Verdana,Arial,Helvetica,sans-serif; text-align:center; vertical-align:middle; border-width:0px 1px 0px 0px; border-style:none solid solid none; padding:0px 5px; border-color:white #000000 #ffffff white">
&nbsp;</td>
<td style="color:#000000; font-size:11px; line-height:13px; font-family:Verdana,Arial,Helvetica,sans-serif; text-align:center; vertical-align:middle; border-width:0px 1px 0px 0px; border-style:none solid solid none; padding:0px 5px; border-color:white #000000 #ffffff white">
&nbsp;</td>
<td style="color:#000000; font-size:11px; line-height:13px; font-family:Verdana,Arial,Helvetica,sans-serif; text-align:center; vertical-align:middle; border-width:0px 1px 0px 0px; border-style:none solid solid none; padding:0px 5px; border-color:white #000000 #ffffff white">
&nbsp;</td>
</tr>
<tr style="text-align:left">
<td style="color:#000000; font-size:11px; line-height:13px; font-family:Verdana,Arial,Helvetica,sans-serif; text-align:center; vertical-align:middle; border-width:0px 1px 0px 0px; border-style:none solid solid none; padding:0px 5px; border-color:white #000000 #ffffff white">
<div align="right">256&nbsp;</div>
</td>
<td style="color:#000000; font-size:11px; line-height:13px; font-family:Verdana,Arial,Helvetica,sans-serif; text-align:center; vertical-align:middle; border-width:0px 1px 0px 0px; border-style:none solid solid none; padding:0px 5px; border-color:white #000000 #ffffff white">
<div align="right"><em>Var.</em>&nbsp;</div>
</td>
<td style="color:#000000; font-size:11px; line-height:13px; font-family:Verdana,Arial,Helvetica,sans-serif; text-align:center; vertical-align:middle; border-width:0px 1px 0px 0px; border-style:none solid solid none; padding:0px 5px; border-color:white #000000 #ffffff white">
<div align="left">&nbsp;Encrypted</div>
</td>
<td style="color:#000000; font-size:11px; line-height:13px; font-family:Verdana,Arial,Helvetica,sans-serif; text-align:center; vertical-align:middle; border-width:0px 1px 0px 0px; border-style:none solid solid none; padding:0px 5px; border-color:white #000000 #ffffff white">
<div align="left">&nbsp;Concatenated primary and secondary master keys**</div>
</td>
</tr>
<tr style="text-align:left">
<td style="color:#000000; font-size:11px; line-height:13px; font-family:Verdana,Arial,Helvetica,sans-serif; text-align:center; vertical-align:middle; border-width:0px 1px 0px 0px; border-style:none solid solid none; padding:0px 5px; border-color:white #000000 #ffffff white">
&nbsp;</td>
<td style="color:#000000; font-size:11px; line-height:13px; font-family:Verdana,Arial,Helvetica,sans-serif; text-align:center; vertical-align:middle; border-width:0px 1px 0px 0px; border-style:none solid solid none; padding:0px 5px; border-color:white #000000 #ffffff white">
&nbsp;</td>
<td style="color:#000000; font-size:11px; line-height:13px; font-family:Verdana,Arial,Helvetica,sans-serif; text-align:center; vertical-align:middle; border-width:0px 1px 0px 0px; border-style:none solid solid none; padding:0px 5px; border-color:white #000000 #ffffff white">
&nbsp;</td>
<td style="color:#000000; font-size:11px; line-height:13px; font-family:Verdana,Arial,Helvetica,sans-serif; text-align:center; vertical-align:middle; border-width:0px 1px 0px 0px; border-style:none solid solid none; padding:0px 5px; border-color:white #000000 #ffffff white">
&nbsp;</td>
</tr>
<tr style="text-align:left">
<td style="color:#000000; font-size:11px; line-height:13px; font-family:Verdana,Arial,Helvetica,sans-serif; text-align:center; vertical-align:middle; border-width:0px 1px 0px 0px; border-style:none solid solid none; padding:0px 5px; border-color:white #000000 #ffffff white">
<div align="right">512&nbsp;</div>
</td>
<td style="color:#000000; font-size:11px; line-height:13px; font-family:Verdana,Arial,Helvetica,sans-serif; text-align:center; vertical-align:middle; border-width:0px 1px 0px 0px; border-style:none solid solid none; padding:0px 5px; border-color:white #000000 #ffffff white">
<div align="right">65024&nbsp;</div>
</td>
<td style="color:#000000; font-size:11px; line-height:13px; font-family:Verdana,Arial,Helvetica,sans-serif; text-align:center; vertical-align:middle; border-width:0px 1px 0px 0px; border-style:none solid solid none; padding:0px 5px; border-color:white #000000 #ffffff white">
<div align="left">&nbsp;Encrypted</div>
</td>
<td style="color:#000000; font-size:11px; line-height:13px; font-family:Verdana,Arial,Helvetica,sans-serif; text-align:center; vertical-align:middle; border-width:0px 1px 0px 0px; border-style:none solid solid none; padding:0px 5px; border-color:white #000000 #ffffff white">
<div align="left">&nbsp;Reserved (for system encryption, this item is omitted&Dagger;&Dagger;)</div>
</td>
</tr>
<tr style="text-align:left">
<td style="color:#000000; font-size:11px; line-height:13px; font-family:Verdana,Arial,Helvetica,sans-serif; text-align:center; vertical-align:middle; border-width:0px 1px 0px 0px; border-style:none solid solid none; padding:0px 5px; border-color:white #000000 #ffffff white">
&nbsp;</td>
<td style="color:#000000; font-size:11px; line-height:13px; font-family:Verdana,Arial,Helvetica,sans-serif; text-align:center; vertical-align:middle; border-width:0px 1px 0px 0px; border-style:none solid solid none; padding:0px 5px; border-color:white #000000 #ffffff white">
&nbsp;</td>
<td style="color:#000000; font-size:11px; line-height:13px; font-family:Verdana,Arial,Helvetica,sans-serif; text-align:center; vertical-align:middle; border-width:0px 1px 0px 0px; border-style:none solid solid none; padding:0px 5px; border-color:white #000000 #ffffff white">
&nbsp;</td>
<td style="color:#000000; font-size:11px; line-height:13px; font-family:Verdana,Arial,Helvetica,sans-serif; text-align:center; vertical-align:middle; border-width:0px 1px 0px 0px; border-style:none solid solid none; padding:0px 5px; border-color:white #000000 #ffffff white">
&nbsp;</td>
</tr>
<tr style="text-align:left">
<td style="color:#000000; font-size:11px; line-height:13px; font-family:Verdana,Arial,Helvetica,sans-serif; text-align:center; vertical-align:middle; border-width:0px 1px 0px 0px; border-style:none solid solid none; padding:0px 5px; border-color:white #000000 #ffffff white">
<div align="right">65536&nbsp;</div>
</td>
<td style="color:#000000; font-size:11px; line-height:13px; font-family:Verdana,Arial,Helvetica,sans-serif; text-align:center; vertical-align:middle; border-width:0px 1px 0px 0px; border-style:none solid solid none; padding:0px 5px; border-color:white #000000 #ffffff white">
<div align="right">65536&nbsp;</div>
</td>
<td style="color:#000000; font-size:11px; line-height:13px; font-family:Verdana,Arial,Helvetica,sans-serif; text-align:center; vertical-align:middle; border-width:0px 1px 0px 0px; border-style:none solid solid none; padding:0px 5px; border-color:white #000000 #ffffff white">
<div align="left">&nbsp;Encrypted /</div>
</td>
<td style="color:#000000; font-size:11px; line-height:13px; font-family:Verdana,Arial,Helvetica,sans-serif; text-align:center; vertical-align:middle; border-width:0px 1px 0px 0px; border-style:none solid solid none; padding:0px 5px; border-color:white #000000 #ffffff white">
<div align="left">&nbsp;Area for hidden volume header (if there is no hidden volume</div>
</td>
</tr>
<tr style="text-align:left">
<td style="color:#000000; font-size:11px; line-height:13px; font-family:Verdana,Arial,Helvetica,sans-serif; text-align:center; vertical-align:middle; border-width:0px 1px 0px 0px; border-style:none solid solid none; padding:0px 5px; border-color:white #000000 #ffffff white">
<div align="right">&nbsp;</div>
</td>
<td style="color:#000000; font-size:11px; line-height:13px; font-family:Verdana,Arial,Helvetica,sans-serif; text-align:center; vertical-align:middle; border-width:0px 1px 0px 0px; border-style:none solid solid none; padding:0px 5px; border-color:white #000000 #ffffff white">
<div align="right">&nbsp;</div>
</td>
<td style="color:#000000; font-size:11px; line-height:13px; font-family:Verdana,Arial,Helvetica,sans-serif; text-align:center; vertical-align:middle; border-width:0px 1px 0px 0px; border-style:none solid solid none; padding:0px 5px; border-color:white #000000 #ffffff white">
<div align="left">&nbsp;Unencrypted&sect;</div>
</td>
<td style="color:#000000; font-size:11px; line-height:13px; font-family:Verdana,Arial,Helvetica,sans-serif; text-align:center; vertical-align:middle; border-width:0px 1px 0px 0px; border-style:none solid solid none; padding:0px 5px; border-color:white #000000 #ffffff white">
<div align="left">&nbsp;within the volume, this area contains random data&dagger;&dagger;). For</div>
</td>
</tr>
<tr style="text-align:left">
<td style="color:#000000; font-size:11px; line-height:13px; font-family:Verdana,Arial,Helvetica,sans-serif; text-align:center; vertical-align:middle; border-width:0px 1px 0px 0px; border-style:none solid solid none; padding:0px 5px; border-color:white #000000 #ffffff white">
<div align="right">&nbsp;</div>
</td>
<td style="color:#000000; font-size:11px; line-height:13px; font-family:Verdana,Arial,Helvetica,sans-serif; text-align:center; vertical-align:middle; border-width:0px 1px 0px 0px; border-style:none solid solid none; padding:0px 5px; border-color:white #000000 #ffffff white">
<div align="right">&nbsp;</div>
</td>
<td style="color:#000000; font-size:11px; line-height:13px; font-family:Verdana,Arial,Helvetica,sans-serif; text-align:center; vertical-align:middle; border-width:0px 1px 0px 0px; border-style:none solid solid none; padding:0px 5px; border-color:white #000000 #ffffff white">
<div align="left">&nbsp;</div>
</td>
<td style="color:#000000; font-size:11px; line-height:13px; font-family:Verdana,Arial,Helvetica,sans-serif; text-align:center; vertical-align:middle; border-width:0px 1px 0px 0px; border-style:none solid solid none; padding:0px 5px; border-color:white #000000 #ffffff white">
<div align="left">&nbsp;system encryption, this item is omitted.&Dagger;&Dagger; See bytes 0&ndash;65535.</div>
</td>
</tr>
<tr style="text-align:left">
<td style="color:#000000; font-size:11px; line-height:13px; font-family:Verdana,Arial,Helvetica,sans-serif; text-align:center; vertical-align:middle; border-width:0px 1px 0px 0px; border-style:none solid solid none; padding:0px 5px; border-color:white #000000 #ffffff white">
&nbsp;</td>
<td style="color:#000000; font-size:11px; line-height:13px; font-family:Verdana,Arial,Helvetica,sans-serif; text-align:center; vertical-align:middle; border-width:0px 1px 0px 0px; border-style:none solid solid none; padding:0px 5px; border-color:white #000000 #ffffff white">
&nbsp;</td>
<td style="color:#000000; font-size:11px; line-height:13px; font-family:Verdana,Arial,Helvetica,sans-serif; text-align:center; vertical-align:middle; border-width:0px 1px 0px 0px; border-style:none solid solid none; padding:0px 5px; border-color:white #000000 #ffffff white">
&nbsp;</td>
<td style="color:#000000; font-size:11px; line-height:13px; font-family:Verdana,Arial,Helvetica,sans-serif; text-align:center; vertical-align:middle; border-width:0px 1px 0px 0px; border-style:none solid solid none; padding:0px 5px; border-color:white #000000 #ffffff white">
&nbsp;</td>
</tr>
<tr style="text-align:left">
<td style="color:#000000; font-size:11px; line-height:13px; font-family:Verdana,Arial,Helvetica,sans-serif; text-align:center; vertical-align:middle; border-width:0px 1px 0px 0px; border-style:none solid solid none; padding:0px 5px; border-color:white #000000 #ffffff white">
<div align="right">131072&nbsp;</div>
</td>
<td style="color:#000000; font-size:11px; line-height:13px; font-family:Verdana,Arial,Helvetica,sans-serif; text-align:center; vertical-align:middle; border-width:0px 1px 0px 0px; border-style:none solid solid none; padding:0px 5px; border-color:white #000000 #ffffff white">
<div align="right"><em>Var.</em>&nbsp;</div>
</td>
<td style="color:#000000; font-size:11px; line-height:13px; font-family:Verdana,Arial,Helvetica,sans-serif; text-align:center; vertical-align:middle; border-width:0px 1px 0px 0px; border-style:none solid solid none; padding:0px 5px; border-color:white #000000 #ffffff white">
<div align="left">&nbsp;Encrypted</div>
</td>
<td style="color:#000000; font-size:11px; line-height:13px; font-family:Verdana,Arial,Helvetica,sans-serif; text-align:center; vertical-align:middle; border-width:0px 1px 0px 0px; border-style:none solid solid none; padding:0px 5px; border-color:white #000000 #ffffff white">
<div align="left">&nbsp;Data area (master key scope). For system encryption, offset</div>
</td>
</tr>
<tr style="text-align:left">
<td style="color:#000000; font-size:11px; line-height:13px; font-family:Verdana,Arial,Helvetica,sans-serif; text-align:center; vertical-align:middle; border-width:0px 1px 0px 0px; border-style:none solid solid none; padding:0px 5px; border-color:white #000000 #ffffff white">
<div align="right">&nbsp;</div>
</td>
<td style="color:#000000; font-size:11px; line-height:13px; font-family:Verdana,Arial,Helvetica,sans-serif; text-align:center; vertical-align:middle; border-width:0px 1px 0px 0px; border-style:none solid solid none; padding:0px 5px; border-color:white #000000 #ffffff white">
<div align="right">&nbsp;</div>
</td>
<td style="color:#000000; font-size:11px; line-height:13px; font-family:Verdana,Arial,Helvetica,sans-serif; text-align:center; vertical-align:middle; border-width:0px 1px 0px 0px; border-style:none solid solid none; padding:0px 5px; border-color:white #000000 #ffffff white">
<div align="left">&nbsp;</div>
</td>
<td style="color:#000000; font-size:11px; line-height:13px; font-family:Verdana,Arial,Helvetica,sans-serif; text-align:center; vertical-align:middle; border-width:0px 1px 0px 0px; border-style:none solid solid none; padding:0px 5px; border-color:white #000000 #ffffff white">
<div align="left">&nbsp;may be different (depending on offset of system partition).</div>
</td>
</tr>
<tr style="text-align:left">
<td style="color:#000000; font-size:11px; line-height:13px; font-family:Verdana,Arial,Helvetica,sans-serif; text-align:center; vertical-align:middle; border-width:0px 1px 0px 0px; border-style:none solid solid none; padding:0px 5px; border-color:white #000000 #ffffff white">
&nbsp;</td>
<td style="color:#000000; font-size:11px; line-height:13px; font-family:Verdana,Arial,Helvetica,sans-serif; text-align:center; vertical-align:middle; border-width:0px 1px 0px 0px; border-style:none solid solid none; padding:0px 5px; border-color:white #000000 #ffffff white">
&nbsp;</td>
<td style="color:#000000; font-size:11px; line-height:13px; font-family:Verdana,Arial,Helvetica,sans-serif; text-align:center; vertical-align:middle; border-width:0px 1px 0px 0px; border-style:none solid solid none; padding:0px 5px; border-color:white #000000 #ffffff white">
&nbsp;</td>
<td style="color:#000000; font-size:11px; line-height:13px; font-family:Verdana,Arial,Helvetica,sans-serif; text-align:center; vertical-align:middle; border-width:0px 1px 0px 0px; border-style:none solid solid none; padding:0px 5px; border-color:white #000000 #ffffff white">
&nbsp;</td>
</tr>
<tr style="text-align:left">
<td style="color:#000000; font-size:11px; line-height:13px; font-family:Verdana,Arial,Helvetica,sans-serif; text-align:center; vertical-align:middle; border-width:0px 1px 0px 0px; border-style:none solid solid none; padding:0px 5px; border-color:white #000000 #ffffff white">
<div align="right"><em>S</em>-131072&Dagger;</div>
</td>
<td style="color:#000000; font-size:11px; line-height:13px; font-family:Verdana,Arial,Helvetica,sans-serif; text-align:center; vertical-align:middle; border-width:0px 1px 0px 0px; border-style:none solid solid none; padding:0px 5px; border-color:white #000000 #ffffff white">
<div align="right">65536&nbsp;</div>
</td>
<td style="color:#000000; font-size:11px; line-height:13px; font-family:Verdana,Arial,Helvetica,sans-serif; text-align:center; vertical-align:middle; border-width:0px 1px 0px 0px; border-style:none solid solid none; padding:0px 5px; border-color:white #000000 #ffffff white">
<div align="left">&nbsp;Encrypted /</div>
</td>
<td style="color:#000000; font-size:11px; line-height:13px; font-family:Verdana,Arial,Helvetica,sans-serif; text-align:center; vertical-align:middle; border-width:0px 1px 0px 0px; border-style:none solid solid none; padding:0px 5px; border-color:white #000000 #ffffff white">
<div align="left">&nbsp;Backup header (encrypted with a different header key derived</div>
</td>
</tr>
<tr style="text-align:left">
<td style="color:#000000; font-size:11px; line-height:13px; font-family:Verdana,Arial,Helvetica,sans-serif; text-align:center; vertical-align:middle; border-width:0px 1px 0px 0px; border-style:none solid solid none; padding:0px 5px; border-color:white #000000 #ffffff white">
<div align="right">&nbsp;</div>
</td>
<td style="color:#000000; font-size:11px; line-height:13px; font-family:Verdana,Arial,Helvetica,sans-serif; text-align:center; vertical-align:middle; border-width:0px 1px 0px 0px; border-style:none solid solid none; padding:0px 5px; border-color:white #000000 #ffffff white">
<div align="right">&nbsp;</div>
</td>
<td style="color:#000000; font-size:11px; line-height:13px; font-family:Verdana,Arial,Helvetica,sans-serif; text-align:center; vertical-align:middle; border-width:0px 1px 0px 0px; border-style:none solid solid none; padding:0px 5px; border-color:white #000000 #ffffff white">
<div align="left">&nbsp;Unencrypted&sect;</div>
</td>
<td style="color:#000000; font-size:11px; line-height:13px; font-family:Verdana,Arial,Helvetica,sans-serif; text-align:center; vertical-align:middle; border-width:0px 1px 0px 0px; border-style:none solid solid none; padding:0px 5px; border-color:white #000000 #ffffff white">
<div align="left">&nbsp;using a different salt). For system encryption, this item is</div>
</td>
</tr>
<tr style="text-align:left">
<td style="color:#000000; font-size:11px; line-height:13px; font-family:Verdana,Arial,Helvetica,sans-serif; text-align:center; vertical-align:middle; border-width:0px 1px 0px 0px; border-style:none solid solid none; padding:0px 5px; border-color:white #000000 #ffffff white">
<div align="right">&nbsp;</div>
</td>
<td style="color:#000000; font-size:11px; line-height:13px; font-family:Verdana,Arial,Helvetica,sans-serif; text-align:center; vertical-align:middle; border-width:0px 1px 0px 0px; border-style:none solid solid none; padding:0px 5px; border-color:white #000000 #ffffff white">
<div align="right">&nbsp;</div>
</td>
<td style="color:#000000; font-size:11px; line-height:13px; font-family:Verdana,Arial,Helvetica,sans-serif; text-align:center; vertical-align:middle; border-width:0px 1px 0px 0px; border-style:none solid solid none; padding:0px 5px; border-color:white #000000 #ffffff white">
<div align="left">&nbsp;</div>
</td>
<td style="color:#000000; font-size:11px; line-height:13px; font-family:Verdana,Arial,Helvetica,sans-serif; text-align:center; vertical-align:middle; border-width:0px 1px 0px 0px; border-style:none solid solid none; padding:0px 5px; border-color:white #000000 #ffffff white">
<div align="left">&nbsp;omitted.&Dagger;&Dagger; See bytes 0&ndash;65535.</div>
</td>
</tr>
<tr style="text-align:left">
<td style="color:#000000; font-size:11px; line-height:13px; font-family:Verdana,Arial,Helvetica,sans-serif; text-align:center; vertical-align:middle; border-width:0px 1px 0px 0px; border-style:none solid solid none; padding:0px 5px; border-color:white #000000 #ffffff white">
&nbsp;</td>
<td style="color:#000000; font-size:11px; line-height:13px; font-family:Verdana,Arial,Helvetica,sans-serif; text-align:center; vertical-align:middle; border-width:0px 1px 0px 0px; border-style:none solid solid none; padding:0px 5px; border-color:white #000000 #ffffff white">
&nbsp;</td>
<td style="color:#000000; font-size:11px; line-height:13px; font-family:Verdana,Arial,Helvetica,sans-serif; text-align:center; vertical-align:middle; border-width:0px 1px 0px 0px; border-style:none solid solid none; padding:0px 5px; border-color:white #000000 #ffffff white">
&nbsp;</td>
<td style="color:#000000; font-size:11px; line-height:13px; font-family:Verdana,Arial,Helvetica,sans-serif; text-align:center; vertical-align:middle; border-width:0px 1px 0px 0px; border-style:none solid solid none; padding:0px 5px; border-color:white #000000 #ffffff white">
&nbsp;</td>
</tr>
<tr style="text-align:left">
<td style="color:#000000; font-size:11px; line-height:13px; font-family:Verdana,Arial,Helvetica,sans-serif; text-align:center; vertical-align:middle; border-width:0px 1px 0px 0px; border-style:none solid solid none; padding:0px 5px; border-color:white #000000 #ffffff white">
<div align="right"><em>S</em>-65536&Dagger;</div>
</td>
<td style="color:#000000; font-size:11px; line-height:13px; font-family:Verdana,Arial,Helvetica,sans-serif; text-align:center; vertical-align:middle; border-width:0px 1px 0px 0px; border-style:none solid solid none; padding:0px 5px; border-color:white #000000 #ffffff white">
<div align="right">65536&nbsp;</div>
</td>
<td style="color:#000000; font-size:11px; line-height:13px; font-family:Verdana,Arial,Helvetica,sans-serif; text-align:center; vertical-align:middle; border-width:0px 1px 0px 0px; border-style:none solid solid none; padding:0px 5px; border-color:white #000000 #ffffff white">
<div align="left">&nbsp;Encrypted /</div>
</td>
<td style="color:#000000; font-size:11px; line-height:13px; font-family:Verdana,Arial,Helvetica,sans-serif; text-align:center; vertical-align:middle; border-width:0px 1px 0px 0px; border-style:none solid solid none; padding:0px 5px; border-color:white #000000 #ffffff white">
<div align="left">&nbsp;Backup header for hidden volume (encrypted with a different</div>
</td>
</tr>
<tr style="text-align:left">
<td style="color:#000000; font-size:11px; line-height:13px; font-family:Verdana,Arial,Helvetica,sans-serif; text-align:center; vertical-align:middle; border-width:0px 1px 0px 0px; border-style:none solid solid none; padding:0px 5px; border-color:white #000000 #ffffff white">
<div align="right">&nbsp;</div>
</td>
<td style="color:#000000; font-size:11px; line-height:13px; font-family:Verdana,Arial,Helvetica,sans-serif; text-align:center; vertical-align:middle; border-width:0px 1px 0px 0px; border-style:none solid solid none; padding:0px 5px; border-color:white #000000 #ffffff white">
<div align="right">&nbsp;</div>
</td>
<td style="color:#000000; font-size:11px; line-height:13px; font-family:Verdana,Arial,Helvetica,sans-serif; text-align:center; vertical-align:middle; border-width:0px 1px 0px 0px; border-style:none solid solid none; padding:0px 5px; border-color:white #000000 #ffffff white">
<div align="left">&nbsp;Unencrypted&sect;</div>
</td>
<td style="color:#000000; font-size:11px; line-height:13px; font-family:Verdana,Arial,Helvetica,sans-serif; text-align:center; vertical-align:middle; border-width:0px 1px 0px 0px; border-style:none solid solid none; padding:0px 5px; border-color:white #000000 #ffffff white">
<div align="left">&nbsp;header key derived using a different salt). If there is no hidden</div>
</td>
</tr>
<tr style="text-align:left">
<td style="color:#000000; font-size:11px; line-height:13px; font-family:Verdana,Arial,Helvetica,sans-serif; text-align:center; vertical-align:middle; border-width:0px 1px 0px 0px; border-style:none solid solid none; padding:0px 5px; border-color:white #000000 #ffffff white">
<div align="right">&nbsp;</div>
</td>
<td style="color:#000000; font-size:11px; line-height:13px; font-family:Verdana,Arial,Helvetica,sans-serif; text-align:center; vertical-align:middle; border-width:0px 1px 0px 0px; border-style:none solid solid none; padding:0px 5px; border-color:white #000000 #ffffff white">
<div align="right">&nbsp;</div>
</td>
<td style="color:#000000; font-size:11px; line-height:13px; font-family:Verdana,Arial,Helvetica,sans-serif; text-align:center; vertical-align:middle; border-width:0px 1px 0px 0px; border-style:none solid solid none; padding:0px 5px; border-color:white #000000 #ffffff white">
<div align="left">&nbsp;</div>
</td>
<td style="color:#000000; font-size:11px; line-height:13px; font-family:Verdana,Arial,Helvetica,sans-serif; text-align:center; vertical-align:middle; border-width:0px 1px 0px 0px; border-style:none solid solid none; padding:0px 5px; border-color:white #000000 #ffffff white">
<div align="left">&nbsp;volume within the volume, this area contains random data.&dagger;&dagger;</div>
</td>
</tr>
<tr style="text-align:left">
<td style="color:#000000; font-size:11px; line-height:13px; font-family:Verdana,Arial,Helvetica,sans-serif; text-align:center; vertical-align:middle; border-width:0px 1px 0px 0px; border-style:none solid solid none; padding:0px 5px; border-color:white #000000 #ffffff white">
<div align="right">&nbsp;</div>
</td>
<td style="color:#000000; font-size:11px; line-height:13px; font-family:Verdana,Arial,Helvetica,sans-serif; text-align:center; vertical-align:middle; border-width:0px 1px 0px 0px; border-style:none solid solid none; padding:0px 5px; border-color:white #000000 #ffffff white">
<div align="right">&nbsp;</div>
</td>
<td style="color:#000000; font-size:11px; line-height:13px; font-family:Verdana,Arial,Helvetica,sans-serif; text-align:center; vertical-align:middle; border-width:0px 1px 0px 0px; border-style:none solid solid none; padding:0px 5px; border-color:white #000000 #ffffff white">
<div align="left">&nbsp;</div>
</td>
<td style="color:#000000; font-size:11px; line-height:13px; font-family:Verdana,Arial,Helvetica,sans-serif; text-align:center; vertical-align:middle; border-width:0px 1px 0px 0px; border-style:none solid solid none; padding:0px 5px; border-color:white #000000 #ffffff white">
<div align="left">&nbsp;For system encryption, this item is omitted.&Dagger;&Dagger; See bytes</div>
</td>
</tr>
<tr style="text-align:left">
<td style="color:#000000; font-size:11px; line-height:13px; font-family:Verdana,Arial,Helvetica,sans-serif; text-align:center; vertical-align:middle; border-width:0px 1px 0px 0px; border-style:none solid solid none; padding:0px 5px; border-color:white #000000 #ffffff white">
<div align="right">&nbsp;</div>
</td>
<td style="color:#000000; font-size:11px; line-height:13px; font-family:Verdana,Arial,Helvetica,sans-serif; text-align:center; vertical-align:middle; border-width:0px 1px 0px 0px; border-style:none solid solid none; padding:0px 5px; border-color:white #000000 #ffffff white">
<div align="right">&nbsp;</div>
</td>
<td style="color:#000000; font-size:11px; line-height:13px; font-family:Verdana,Arial,Helvetica,sans-serif; text-align:center; vertical-align:middle; border-width:0px 1px 0px 0px; border-style:none solid solid none; padding:0px 5px; border-color:white #000000 #ffffff white">
<div align="left">&nbsp;</div>
</td>
<td style="color:#000000; font-size:11px; line-height:13px; font-family:Verdana,Arial,Helvetica,sans-serif; text-align:center; vertical-align:middle; border-width:0px 1px 0px 0px; border-style:none solid solid none; padding:0px 5px; border-color:white #000000 #ffffff white">
<div align="left">&nbsp;0&ndash;65535.</div>
</td>
</tr>
<tr style="text-align:left">
<td style="color:#000000; font-size:11px; line-height:13px; font-family:Verdana,Arial,Helvetica,sans-serif; text-align:center; vertical-align:middle; border-width:0px 1px 0px 0px; border-style:none solid solid none; padding:0px 5px; border-color:white #000000 #ffffff white">
&nbsp;</td>
<td style="color:#000000; font-size:11px; line-height:13px; font-family:Verdana,Arial,Helvetica,sans-serif; text-align:center; vertical-align:middle; border-width:0px 1px 0px 0px; border-style:none solid solid none; padding:0px 5px; border-color:white #000000 #ffffff white">
&nbsp;</td>
<td style="color:#000000; font-size:11px; line-height:13px; font-family:Verdana,Arial,Helvetica,sans-serif; text-align:center; vertical-align:middle; border-width:0px 1px 0px 0px; border-style:none solid solid none; padding:0px 5px; border-color:white #000000 #ffffff white">
&nbsp;</td>
<td style="color:#000000; font-size:11px; line-height:13px; font-family:Verdana,Arial,Helvetica,sans-serif; text-align:center; vertical-align:middle; border-width:0px 1px 0px 0px; border-style:none solid solid none; padding:0px 5px; border-color:white #000000 #ffffff white">
&nbsp;</td>
</tr>
</tbody>
</table>
<div style="text-align:left; margin-top:19px; margin-bottom:19px; padding-top:0px; padding-bottom:0px">
The fields located at byte #0 (salt) and #256 (master keys) contain random values generated by the random number generator (see the section
<a href="Random%20Number%20Generator.html" style="text-align:left; color:#0080c0; text-decoration:none">
Random Number Generator</a>) during the volume creation process.</div>
<div style="text-align:left; margin-top:19px; margin-bottom:19px; padding-top:0px; padding-bottom:0px">
If a VeraCrypt volume hosts a hidden volume (within its free space), the header of the hidden volume is located at byte #65536 of the host volume (the header of the host/outer volume is located at byte #0 of the host volume &ndash; see the section
<a href="Hidden%20Volume.html" style="text-align:left; color:#0080c0; text-decoration:none">
Hidden Volume</a>). If there is no hidden volume within a VeraCrypt volume, bytes 65536&ndash;131071 of the volume (i.e., the area where the header of a hidden volume can reside) contain random data (see above for information on the method used to fill free
 volume space with random data when the volume is created). The layout of the header of a hidden volume is the same as the one of a standard volume (bytes 0&ndash;65535).</div>
<div style="text-align:left; margin-top:19px; margin-bottom:19px; padding-top:0px; padding-bottom:0px">
The maximum possible VeraCrypt volume size is 2<sup style="text-align:left; font-size:85%">63</sup> bytes (8,589,934,592 GB). However, due to security reasons (with respect to the 128-bit block size used by the
<a href="Encryption%20Algorithms.html" style="text-align:left; color:#0080c0; text-decoration:none">
encryption algorithms</a>), the maximum allowed volume size is 1 PB (1,048,576 GB).</div>
<h4 style="text-align:left; font-family:Arial,Helvetica,Verdana,sans-serif; font-weight:bold; margin-top:0px; font-size:12px; margin-bottom:1px">
Embedded Backup Headers</h4>
<div style="text-align:left; margin-top:19px; margin-bottom:19px; padding-top:0px; padding-bottom:0px">
Each VeraCrypt volume contains an embedded backup header, located at the end of the volume (see above). The header backup is
<em style="text-align:left">not</em> a copy of the volume header because it is encrypted with a different header key derived using a different salt (see the section
<a href="Header%20Key%20Derivation.html" style="text-align:left; color:#0080c0; text-decoration:none">
Header Key Derivation, Salt, and Iteration Count</a>).</div>
<div style="text-align:left; margin-top:19px; margin-bottom:19px; padding-top:0px; padding-bottom:0px">
When the volume password and/or PIM and/or keyfiles are changed, or when the header is restored from the embedded (or an external) header backup, both the volume header and the backup header (embedded in the volume) are re-encrypted with different header keys
 (derived using newly generated salts &ndash; the salt for the volume header is different from the salt for the backup header). Each salt is generated by the VeraCrypt random number generator (see the section
<a href="Random%20Number%20Generator.html" style="text-align:left; color:#0080c0; text-decoration:none">
Random Number Generator</a>).</div>
<div style="text-align:left; margin-top:19px; margin-bottom:19px; padding-top:0px; padding-bottom:0px">
For more information about header backups, see the subsection <a href="Program%20Menu.html#tools-restore-volume-header" style="text-align:left; color:#0080c0; text-decoration:none">
Tools &gt; Restore Volume Header</a> in the chapter <a href="Main%20Program%20Window.html" style="text-align:left; color:#0080c0; text-decoration:none">
Main Program Window</a>.</div>
<div style="text-align:left; margin-top:19px; margin-bottom:19px; padding-top:0px; padding-bottom:0px">
<a href="Standard%20Compliance.html" style="text-align:left; color:#0080c0; text-decoration:none; font-weight:bold">Next Section &gt;&gt;</a></div>
<hr align="left" size="1" width="189" style="text-align:left; height:0px; border-width:0px 1px 1px; border-style:solid; border-color:#000000">
<p><span style="text-align:left; font-size:10px; line-height:12px">* Provided that the options
<em style="text-align:left">Quick Format</em> and <em style="text-align:left">Dynamic</em> are disabled and provided that the volume does not contain a filesystem that has been encrypted in place (note that VeraCrypt does not allow the user to create a hidden
 volume within such a volume).</span><br style="text-align:left">
<span style="text-align:left; font-size:10px; line-height:12px">&dagger; The encrypted areas of the volume header are encrypted in XTS mode using the primary and secondary header keys. For more information, see the section
<a href="Encryption%20Scheme.html" style="text-align:left; color:#0080c0; text-decoration:none">
Encryption Scheme</a> and the section <a href="Header%20Key%20Derivation.html" style="text-align:left; color:#0080c0; text-decoration:none">
Header Key Derivation, Salt, and Iteration Count</a>.</span><br style="text-align:left">
<span style="text-align:left; font-size:10px; line-height:12px">&Dagger; <em style="text-align:left">
S</em> denotes the size of the volume host (in bytes).</span><br style="text-align:left">
<span style="text-align:left; font-size:10px; line-height:12px">&sect; Note that the salt does not need to be encrypted, as it does not have to be kept secret [7] (salt is a sequence of random values).</span><br style="text-align:left">
<span style="text-align:left; font-size:10px; line-height:12px">** Multiple concatenated master keys are stored here when the volume is encrypted using a cascade of ciphers (secondary master keys are used for XTS mode).</span><br style="text-align:left">
<span style="text-align:left; font-size:10px; line-height:12px">&dagger;&dagger; See above in this section for information on the method used to fill free volume space with random data when the volume is created.</span><br style="text-align:left">
<span style="text-align:left; font-size:10px; line-height:12px">&Dagger;&Dagger; Here, the meaning of &quot;system encryption&quot; does not include a hidden volume containing a hidden operating system.</span></p>
</div><div class="ClearBoth"></div></body></html>
