<!DOCTYPE html>
<html lang="en">
<head>
<meta http-equiv="content-type" content="text/html; charset=utf-8" />
<title>VeraCrypt - Free Open source disk encryption with strong security for the Paranoid</title>
<meta name="description" content="VeraCrypt is free open-source disk encryption software for Windows, Mac OS X and Linux. In case an attacker forces you to reveal the password, VeraCrypt provides plausible deniability. In contrast to file encryption, data encryption performed by VeraCrypt is real-time (on-the-fly), automatic, transparent, needs very little memory, and does not involve temporary unencrypted files."/>
<meta name="keywords" content="encryption, security"/>
<link href="styles.css" rel="stylesheet" type="text/css" />
</head>
<body>

<div>
<a href="Documentation.html"><img src="VeraCrypt128x128.png" alt="VeraCrypt"/></a>
</div>

<div id="menu">
	<ul>
	  <li><a href="Home.html">Home</a></li>
	  <li><a href="Code.html">Source Code</a></li>
	  <li><a href="Downloads.html">Downloads</a></li>
	  <li><a class="active" href="Documentation.html">Documentation</a></li>
	  <li><a href="Donation.html">Donate</a></li>
	  <li><a href="https://sourceforge.net/p/veracrypt/discussion/" target="_blank">Forums</a></li>
	</ul>
</div>

<div>
<p>
<a href="Documentation.html">Documentation</a>
<img src="arrow_right.gif" alt=">>" style="margin-top: 5px">
<a href="Converting%20TrueCrypt%20volumes%20and%20partitions.html">Converting TrueCrypt volumes and partitions</a>
</p></div>

<div class="wikidoc">
<h1>Converting TrueCrypt volumes and partitions</h1>
<p><strong>⚠️ Warning:</strong> <span style="color: red;">After conversion, ensure that the "TrueCrypt Mode" checkbox is not selected during the mount of the converted volume. Since it is no longer a TrueCrypt volume, mounting it with this option will lead to a mount failure.</span></p>
<p><strong>⚠️ Important Notice:</strong> As of version 1.26, VeraCrypt has removed support for "TrueCrypt Mode." Consequently, the conversion of TrueCrypt volumes and partitions using this method is no longer possible. Please refer to <a href="Conversion_Guide_VeraCrypt_1.26_and_Later.html">this documentation page</a> for guidance on how to proceed with TrueCrypt volumes in VeraCrypt versions 1.26 and later.</p>
<p>From version 1.0f up to and including version 1.25.9, TrueCrypt volumes and <strong>non-system</strong> partitions created with TrueCrypt versions 6.x and 7.x, starting with version 6.0 released on July 4th 2008, can be converted to VeraCrypt format using any of the following actions:</p>
<ul>
<li>Change Volume Password </li><li>Set Header Key Derivation Algorithm </li><li>Add/Remove key files </li><li>Remove all key files </li></ul>
<p>If the TrueCrypt volume contains a hidden volume, it should also be converted using the same approach, by specifying the hidden volume password and/or keyfiles.</p>
<p>🚨 After conversion of a file container, the file extension will remain as .tc. Manually change it to .hc if you want VeraCrypt 1.26 or newer to automatically recognize it.</p>
<p>&ldquo;TrueCrypt Mode&rdquo; must be checked in the dialog as shown below:</p>
<p>&nbsp;<img src="Converting TrueCrypt volumes and partitions_truecrypt_convertion.jpg" alt=""></p>
<p><strong>Note: </strong>Converting system partitions encrypted with TrueCrypt is not supported.</p>
</div><div class="ClearBoth"></div></body></html>
