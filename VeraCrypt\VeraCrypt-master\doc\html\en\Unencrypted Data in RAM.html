<!DOCTYPE html>
<html lang="en">
<head>
<meta http-equiv="content-type" content="text/html; charset=utf-8" />
<title>VeraCrypt - Free Open source disk encryption with strong security for the Paranoid</title>
<meta name="description" content="VeraCrypt is free open-source disk encryption software for Windows, Mac OS X and Linux. In case an attacker forces you to reveal the password, VeraCrypt provides plausible deniability. In contrast to file encryption, data encryption performed by VeraCrypt is real-time (on-the-fly), automatic, transparent, needs very little memory, and does not involve temporary unencrypted files."/>
<meta name="keywords" content="encryption, security"/>
<link href="styles.css" rel="stylesheet" type="text/css" />
</head>
<body>

<div>
<a href="Documentation.html"><img src="VeraCrypt128x128.png" alt="VeraCrypt"/></a>
</div>

<div id="menu">
	<ul>
	  <li><a href="Home.html">Home</a></li>
	  <li><a href="Code.html">Source Code</a></li>
	  <li><a href="Downloads.html">Downloads</a></li>
	  <li><a class="active" href="Documentation.html">Documentation</a></li>
	  <li><a href="Donation.html">Donate</a></li>
	  <li><a href="https://sourceforge.net/p/veracrypt/discussion/" target="_blank">Forums</a></li>
	</ul>
</div>

<div>
<p>
<a href="Documentation.html">Documentation</a>
<img src="arrow_right.gif" alt=">>" style="margin-top: 5px">
<a href="Security%20Requirements%20and%20Precautions.html">Security Requirements and Precautions</a>
<img src="arrow_right.gif" alt=">>" style="margin-top: 5px">
<a href="Unencrypted%20Data%20in%20RAM.html">Unencrypted Data in RAM</a>
</p></div>

<div class="wikidoc">
<h1>Unencrypted Data in RAM</h1>
<div style="text-align:left; margin-top:19px; margin-bottom:19px; padding-top:0px; padding-bottom:0px">
It is important to note that VeraCrypt is <em style="text-align:left">disk</em> encryption software, which encrypts only disks, not RAM (memory).</div>
<div style="text-align:left; margin-top:19px; margin-bottom:19px; padding-top:0px; padding-bottom:0px">
Keep in mind that most programs do not clear the memory area (buffers) in which they store unencrypted (portions of) files they load from a VeraCrypt volume. This means that after you exit such a program, unencrypted data it worked with may remain in memory
 (RAM) until the computer is turned off (and, according to some researchers, even for some time after the power is turned off*). Also note that if you open a file stored on a VeraCrypt volume, for example, in a text editor and then force unmount on the VeraCrypt
 volume, then the file will remain unencrypted in the area of memory (RAM) used by (allocated to) the text editor. This also applies to forced auto-unmount.</div>
<div style="text-align:left; margin-top:19px; margin-bottom:19px; padding-top:0px; padding-bottom:0px">
Inherently, unencrypted master keys have to be stored in RAM too. When a non-system VeraCrypt volume is unmounted, VeraCrypt erases its master keys (stored in RAM). When the computer is cleanly restarted (or cleanly shut down), all non-system VeraCrypt volumes
 are automatically unmounted and, thus, all master keys stored in RAM are erased by the VeraCrypt driver (except master keys for system partitions/drives &mdash; see below). However, when power supply is abruptly interrupted, when the computer is reset (not
 cleanly restarted), or when the system crashes, <strong style="text-align:left">
VeraCrypt naturally stops running and therefore cannot </strong>erase any keys or any other sensitive data. Furthermore, as Microsoft does not provide any appropriate API for handling hibernation and shutdown, master keys used for system encryption cannot be
 reliably (and are not) erased from RAM when the computer hibernates, is shut down or restarted.**</div>
<div style="text-align:left; margin-top:19px; margin-bottom:19px; padding-top:0px; padding-bottom:0px">
Starting from version 1.24, VeraCrypt introduces a mechanism to encrypt master keys and cached passwords in RAM. This RAM encryption mechanism must be activated manually in "Performance/Driver Configuration" dialog. RAM encryption comes with a performance overhead (between 5% and 15% depending on the CPU speed) and it disables Windows hibernate. <br>
Moreover, VeraCrypt 1.24 and above provide an additional security mechanism when system encryption is used that makes VeraCrypt erase master keys from RAM when a new device is connected to the PC. This additional mechanism can be activated using an option in System Settings dialog.<br/>
Even though both above mechanisms provides strong protection for masterskeys and cached password, users should still take usual precautions related for the safery of sensitive data in RAM.</div>
<table style="border-collapse:separate; border-spacing:0px; text-align:left; font-size:11px; line-height:13px; font-family:Verdana,Arial,Helvetica,sans-serif">
<tbody style="text-align:left">
<tr style="text-align:left">
<td style="text-align:left; font-size:11px; line-height:13px; font-family:Verdana,Arial,Helvetica,sans-serif; color:#ff0000; padding:15px; border:1px solid #000000">
To summarize, VeraCrypt <strong style="text-align:left">cannot</strong> and does <strong style="text-align:left">
not</strong> ensure that RAM contains no sensitive data (e.g. passwords, master keys, or decrypted data). Therefore, after each session in which you work with a VeraCrypt volume or in which an encrypted operating system is running, you must shut down (or, if
 the <a href="Hibernation%20File.html" style="text-align:left; color:#0080c0; text-decoration:none">
hibernation file</a> is <a href="System%20Encryption.html" style="text-align:left; color:#0080c0; text-decoration:none">
encrypted</a>, hibernate) the computer and then leave it powered off for at least several minutes (the longer, the better) before turning it on again. This is required to clear the RAM (also see the section
<a href="Hibernation%20File.html" style="text-align:left; color:#0080c0; text-decoration:none">
Hibernation File</a>).</td>
</tr>
</tbody>
</table>
<p>&nbsp;</p>
<hr align="left" size="1" width="189" style="text-align:left; height:0px; border-width:0px 1px 1px; border-style:solid; border-color:#000000">
<p><span style="text-align:left; font-size:10px; line-height:12px">* Allegedly, for 1.5-35 seconds under normal operating temperatures (26-44 &deg;C) and up to several hours when the memory modules are cooled (when the computer is running) to very low temperatures
 (e.g. -50&nbsp;&deg;C). New types of memory modules allegedly exhibit a much shorter decay time (e.g. 1.5-2.5 seconds) than older types (as of 2008).</span><br style="text-align:left">
<span style="text-align:left; font-size:10px; line-height:12px">** Before a key can be erased from RAM, the corresponding VeraCrypt volume must be unmounted. For non-system volumes, this does not cause any problems. However, as Microsoft currently does not
 provide any appropriate API for handling the final phase of the system shutdown process, paging files located on encrypted system volumes that are unmounted during the system shutdown process may still contain valid swapped-out memory pages (including portions
 of Windows system files). This could cause 'blue screen' errors. Therefore, to prevent 'blue screen' errors, VeraCrypt does not unmount encrypted system volumes and consequently cannot clear the master keys of the system volumes when the system is shut down
 or restarted.</span></p>
</div><div class="ClearBoth"></div></body></html>
