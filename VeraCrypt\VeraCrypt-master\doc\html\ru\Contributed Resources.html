﻿<!DOCTYPE html>
<html lang="ru">
<head>
<meta http-equiv="content-type" content="text/html; charset=utf-8" />
<title>VeraCrypt - Бесплатное надёжное шифрование дисков с открытым исходным кодом</title>
<meta name="description" content="VeraCrypt это бесплатное программное обеспечение для шифрования дисков с открытым исходным кодом для Windows, Mac OS X (macOS) и Linux. В случае, если злоумышленник вынуждает вас раскрыть пароль, VeraCrypt обеспечивает правдоподобное отрицание наличия шифрования. В отличие от пофайлового шифрования, VeraCrypt шифрует данные в реальном времени (на лету), автоматически, прозрачно, требует очень мало памяти и не использует временные незашифрованные файлы."/>
<meta name="keywords" content="encryption, security, шифрование, безопасность"/>
<link href="styles.css" rel="stylesheet" type="text/css" />
</head>
<body>

<div>
<a href="Documentation.html"><img src="VeraCrypt128x128.png" alt="VeraCrypt"/></a>
</div>

<div id="menu">
	<ul>
	  <li><a class="active" href="Home.html">Начало</a></li>
	  <li><a href="Code.html">Исходный код</a></li>
	  <li><a href="Downloads.html">Загрузить</a></li>
	  <li><a href="Documentation.html">Документация</a></li>
	  <li><a href="Donation.html">Поддержать разработку</a></li>
	  <li><a href="https://sourceforge.net/p/veracrypt/discussion/" target="_blank">Форум</a></li>
	</ul>
</div>

<div class="wikidoc">
<p>Полезные ресурсы, предоставленные пользователями VeraCrypt.</p>
<h3>Сторонние бинарные файлы:</h3>
<ul>
<li>Linux Ubuntu <strong>PPA</strong>, предоставлено пользователем&nbsp;<a href="https://unit193.net/" target="_blank">&quot;Unit 193&quot;</a> (автор сборки – Launchpad):
<ul>
<li><a href="https://launchpad.net/~unit193/&#43;archive/ubuntu/encryption" target="_blank">https://launchpad.net/~unit193/&#43;archive/ubuntu/encryption</a>
</li></ul>
</li><li>Linux <strong>Armv7</strong> GUI/консольная 32-разрядная сборка на ChromeBook, выполненная пользователем <a href="https://www.codeplex.com/site/users/view/haggster">
haggster</a>:
<ul>
<li><a href="http://sourceforge.net/projects/veracrypt/files/Contributions/ARM%20Linux/veracrypt-1.0f-1-setup-arm.tar.bz2/download" target="_blank">veracrypt-1.0f-1-setup-arm.tar.bz2</a>
</li></ul>
</li></ul>
<h3>Руководства:</h3>
<ul>
<li><a href="http://schneckchen.in/veracrypt-anleitung-zum-daten-verschluesseln/" target="_blank">http://schneckchen.in/veracrypt-anleitung-zum-daten-verschluesseln/</a>:
<ul>
<li>Немецкое руководство по VeraCrypt, автор Andreas Heinz. </li></ul>
</li><li><a href="http://howto.wared.fr/raspberry-pi-arch-linux-arm-installation-veracrypt/" target="_blank">http://howto.wared.fr/raspberry-pi-arch-linux-arm-installation-veracrypt/</a>:
<ul>
<li>Французское руководство по сборке VeraCrypt на Raspberry Pi Arch Linux, автор <a href="http://howto.wared.fr/author/wared/" target="_blank">
Edouard WATTECAMPS</a>. </li></ul>
</li><li><a href="http://sourceforge.net/projects/veracrypt/files/Contributions/clonezilla_using_veracrypt_ver_1.1.doc/download" target="_blank">clonezilla_using_veracrypt_ver_1.1.doc</a>:
<ul>
<li>Руководство по использованию VeraCrypt в CloneZilla для доступа к зашифрованным резервным копиям, автор
<a href="https://www.codeplex.com/site/users/view/pjc123" target="_blank">pjc123</a>.
</li></ul>
</li><li><a href="https://bohdan-danishevsky.blogspot.fr/2016/11/raspberry-pi-raspbian-installing.html" target="_blank">https://bohdan-danishevsky.blogspot.fr/2016/11/raspberry-pi-raspbian-installing.html</a>
<ul>
<li>Руководство по установке и использованию официальных бинарных файлов VeraCrypt на Raspberry Pi (Raspbian), автор Bohdan Danishevsky.
</li></ul>
</li></ul>
<h3>Разное:</h3>
<ul>
<li><a href="http://sourceforge.net/projects/veracrypt/files/Contributions/vcsteg2.py/download" target="_blank">vcsteg2.py</a>: скрипт на Python, пытающийся скрыть том VeraCrypt внутри видеофайла (стеганография).
</li></ul>
</div><div class="ClearBoth"></div></body></html>
