﻿<!DOCTYPE html>
<html lang="ru">
<head>
<meta http-equiv="content-type" content="text/html; charset=utf-8" />
<title>VeraCrypt - Бесплатное надёжное шифрование дисков с открытым исходным кодом</title>
<meta name="description" content="VeraCrypt это бесплатное программное обеспечение для шифрования дисков с открытым исходным кодом для Windows, Mac OS X (macOS) и Linux. В случае, если злоумышленник вынуждает вас раскрыть пароль, VeraCrypt обеспечивает правдоподобное отрицание наличия шифрования. В отличие от пофайлового шифрования, VeraCrypt шифрует данные в реальном времени (на лету), автоматически, прозрачно, требует очень мало памяти и не использует временные незашифрованные файлы."/>
<meta name="keywords" content="encryption, security, шифрование, безопасность"/>
<link href="styles.css" rel="stylesheet" type="text/css" />
</head>
<body>

<div>
<a href="Documentation.html"><img src="VeraCrypt128x128.png" alt="VeraCrypt"/></a>
</div>

<div id="menu">
	<ul>
	  <li><a href="Home.html">Начало</a></li>
	  <li><a href="Code.html">Исходный код</a></li>
	  <li><a href="Downloads.html">Загрузить</a></li>
	  <li><a class="active" href="Documentation.html">Документация</a></li>
	  <li><a href="Donation.html">Поддержать разработку</a></li>
	  <li><a href="https://sourceforge.net/p/veracrypt/discussion/" target="_blank">Форум</a></li>
	</ul>
</div>

<div>
<p>
<a href="Documentation.html">Документация</a>
<img src="arrow_right.gif" alt=">>" style="margin-top: 5px">
<a href="Plausible%20Deniability.html">Правдоподобное отрицание наличия шифрования</a>
<img src="arrow_right.gif" alt=">>" style="margin-top: 5px">
<a href="Hidden%20Volume.html">Скрытый том</a>
<img src="arrow_right.gif" alt=">>" style="margin-top: 5px">
<a href="Security%20Requirements%20for%20Hidden%20Volumes.html">Требования безопасности для скрытых томов</a>
</p></div>

<div class="wikidoc">
<h1>Требования безопасности и меры предосторожности, касающиеся скрытых томов</h1>
<div style="text-align:left; margin-top:19px; margin-bottom:19px; padding-top:0px; padding-bottom:0px">
Если вы используете <a href="Hidden%20Volume.html" style="text-align:left; color:#0080c0; text-decoration:none">
скрытый том VeraCrypt</a>, то обязаны соблюдать описанные здесь требования безопасности и меры предосторожности.<br>
Отказ от ответственности: мы не гарантируем, что эта глава содержит список
<em style="text-align:left">всех</em> проблем, связанных с безопасностью, и атак, которые может предпринять ваш
неприятель, чтобы получить доступ к данным, хранящимся в скрытом томе TrueCrypt, или ограничить возможность VeraCrypt
надёжно хранить такие данные и обеспечивать правдоподобное отрицание наличия шифрования.</div>
<ul style="text-align:left; margin-top:18px; margin-bottom:19px; padding-top:0px; padding-bottom:0px">
<li style="text-align:left; margin-top:0px; margin-bottom:0px; padding-top:0px; padding-bottom:0px">
Если неприятель имеет доступ к (размонтированному) тому VeraCrypt в нескольких точках в течение достаточно длительного
времени, он может определить, какие сектора тома изменяются. Если вы изменяете содержимое
<a href="Hidden%20Volume.html" style="text-align:left; color:#0080c0; text-decoration:none">
скрытого тома</a> (например, создаёте/копируете новые файлы в скрытый том или изменяете, удаляете, переименовываете,
перемещаете файлы в скрытом томе и т. п.), содержимое секторов (зашифрованный текст) в области, занимаемой скрытым томом,
изменяется. Узнав пароль от внешнего тома, неприятель может потребовать объяснений, почему изменились эти сектора.
Если вы не дадите правдоподобного объяснения, это может послужить поводом заподозрить наличие скрытого тома внутри
внешнего тома.<br style="text-align:left">
<br style="text-align:left">
Имейте в виду, что случаи, подобные вышеописанному, также могут возникать в следующих ситуациях:<br style="text-align:left">
<ul style="text-align:left; margin-top:18px; margin-bottom:19px; padding-top:0px; padding-bottom:0px">
<li style="text-align:left; margin-top:0px; margin-bottom:0px; padding-top:0px; padding-bottom:0px">
Файловая система, в которой у вас хранится контейнер VeraCrypt на основе файла, была дефрагментирована, и копия
контейнера VeraCrypt (или его фрагмента) остаётся в свободном пространстве хост-тома (в дефрагментированной
файловой системе). Чтобы это предотвратить, сделайте одно из следующего:
<ul style="text-align:left; margin-top:18px; margin-bottom:19px; padding-top:0px; padding-bottom:0px">
<li style="text-align:left; margin-top:0px; margin-bottom:0px; padding-top:0px; padding-bottom:0px">
вместо тома VeraCrypt на основе файла используйте том на основе раздела/устройства;</li>
<li style="text-align:left; margin-top:0px; margin-bottom:0px; padding-top:0px; padding-bottom:0px">
надёжно очищайте (затирайте) свободное пространство в хост-томе (в дефрагментированной файловой системе)
после дефрагментации. В Windows это можно делать с помощью бесплатной утилиты Microsoft
<a href="https://technet.microsoft.com/en-us/sysinternals/bb897443.aspx">SDelete</a>. В Linux для
той же цели подойдёт утилита <em>shred</em> из пакета GNU coreutils;</li>
<li style="text-align:left; margin-top:0px; margin-bottom:0px; padding-top:0px; padding-bottom:0px">
не дефрагментируйте файловые системы, в которых у вас хранятся тома VeraCrypt. </li></ul>
</li><li style="text-align:left; margin-top:0px; margin-bottom:0px; padding-top:0px; padding-bottom:0px">
Том VeraCrypt на основе файла-контейнера хранится в журналируемой файловой системе (например, в NTFS).
При этом копия контейнера VeraCrypt (или его фрагмента) может оставаться в хост-томе. Чтобы это предотвратить,
сделайте одно из следующего:
<ul style="text-align:left; margin-top:18px; margin-bottom:19px; padding-top:0px; padding-bottom:0px">
<li style="text-align:left; margin-top:0px; margin-bottom:0px; padding-top:0px; padding-bottom:0px">
вместо тома VeraCrypt на основе файла используйте том на основе раздела/устройства; </li>
<li style="text-align:left; margin-top:0px; margin-bottom:0px; padding-top:0px; padding-bottom:0px">
храните контейнер в нежурналируемой файловой системе (например, в FAT32). </li></ul>
</li><li style="text-align:left; margin-top:0px; margin-bottom:0px; padding-top:0px; padding-bottom:0px">
Том VeraCrypt находится на устройстве или в файловой системе, где применяется механизм wear-leveling –
равномерное распределение износа блоков (например, флеш-накопитель SSD или USB-флешка). В таком
устройстве может оставаться копия (или её фрагмент) тома VeraCrypt. Поэтому не храните скрытые тома
в таких устройствах/файловых системах. Подробнее о распределении износа см. в разделе
<a href="Wear-Leveling.html" style="text-align:left; color:#0080c0; text-decoration:none">
Wear-Leveling</a>, глава <a href="Security%20Requirements%20and%20Precautions.html" style="text-align:left; color:#0080c0; text-decoration:none">
Требования безопасности и меры предосторожности</a>. </li><li style="text-align:left; margin-top:0px; margin-bottom:0px; padding-top:0px; padding-bottom:0px">
Том VeraCrypt находится на устройстве или в файловой системе, где сохраняются данные (или на устройстве/в файловой
системе под управлением или мониторингом системы/устройства, сохраняющих данные) (например, значение
таймера или счётчика), которые можно использовать для того, чтобы определить, что один блок был записан
раньше, чем другой, и/или чтобы определить, сколько раз блок был записан/прочитан. Поэтому не храните
скрытые тома в таких устройствах/файловых системах. Выяснить, сохраняет ли устройство/система такие данные,
можно в прилагаемой к устройству/системе документации или связавшись с поставщиком/производителем.</li>
<li style="text-align:left; margin-top:0px; margin-bottom:0px; padding-top:0px; padding-bottom:0px">
Том VeraCrypt находится на устройстве, склонном к износу (где есть возможность определить, что один блок
был записан/считан больше раз, чем другой). Поэтому не храните скрытые тома в таких устройствах/файловых
системах. Выяснить, предрасположено ли устройство к износу, можно в документации на это устройство или
у его поставщика/производителя.</li>
<li style="text-align:left; margin-top:0px; margin-bottom:0px; padding-top:0px; padding-bottom:0px">
Вы делаете резервную копию содержимого скрытого тома, клонируя несущий его хост-том, или создаёте новый
скрытый том, клонируя его хост-том. Поэтому так поступать нельзя. Следуйте инструкциям в главе
<a href="How%20to%20Back%20Up%20Securely.html" style="text-align:left; color:#0080c0; text-decoration:none">
О безопасном резервном копировании</a> и в разделе <a href="Volume%20Clones.html" style="text-align:left; color:#0080c0; text-decoration:none">
Клонирование томов</a>.</li></ul>
</li></ul>
<ul style="text-align:left; margin-top:18px; margin-bottom:19px; padding-top:0px; padding-bottom:0px">
<li style="text-align:left; margin-top:0px; margin-bottom:0px; padding-top:0px; padding-bottom:0px">
При шифровании раздела/устройства, внутри которого вы намереваетесь создать скрытый том, убедитесь, что
выключено <em style="text-align:left">быстрое форматирование</em>.
</li><li style="text-align:left; margin-top:0px; margin-bottom:0px; padding-top:0px; padding-bottom:0px">
В среде Windows убедитесь, что вы не удаляли никаких файлов в томе, внутри которого собираетесь создать
скрытый том (при сканировании карты кластеров удалённые файлы не определяются).
</li><li style="text-align:left; margin-top:0px; margin-bottom:0px; padding-top:0px; padding-bottom:0px">
В среде Linux или Mac OS X (macOS), если вы собираетесь создать скрытый том внутри тома VeraCrypt на основе
файла, убедитесь, что этот том – не на основе разрежённого (sparse) файла (Windows-версия VeraCrypt это
проверяет самостоятельно, не позволяя создавать скрытые тома внутри разрежённых файлов).
</li><li style="text-align:left; margin-top:0px; margin-bottom:0px; padding-top:0px; padding-bottom:0px">
Когда скрытый том смонтирован, операционная система и сторонние приложения могут выполнять запись в нескрытые
тома (обычно в незашифрованный системный том) незашифрованной информации о данных, хранящихся в скрытом томе
(например, имена и расположение файлов, к которым недавно было обращение, базы данных, созданные утилитами
индексирования файлов, и др.), самих данных в незашифрованном виде (временные файлы и т. п.), незашифрованной
информации о файловой системе в скрытом томе (что может быть использовано, например, для идентификации файловой
системы и определения, является ли файловая система той, что во внешнем томе), пароля/ключа для скрытого тома
или других конфиденциальных данных. Поэтому необходимо соблюдать следующие требования и предостережения:
<br style="text-align:left">
<ul style="text-align:left; margin-top:18px; margin-bottom:19px; padding-top:0px; padding-bottom:0px">
<li style="text-align:left; margin-top:0px; margin-bottom:0px; padding-top:0px; padding-bottom:0px">
<em style="text-align:left">Windows</em>: Создайте скрытую операционную систему (о том, как это сделать, см. раздел
<a href="Hidden%20Operating%20System.html" style="text-align:left; color:#0080c0; text-decoration:none">
Скрытая операционная система</a>) и монтируйте скрытые тома только тогда, когда запущена скрытая операционная система.
<span style="text-align:left; font-size:10px; line-height:12px">Примечание. Когда работает скрытая операционная
система, VeraCrypt гарантирует, что все локальные незашифрованные файловые системы и нескрытые тома VeraCrypt
доступны только для чтения (то есть никакие файлы не могут быть записаны в такие файловые системы или тома
VeraCrypt).<a href="#hidden_os_exception">*</a> Запись данных в файловые системы разрешена внутри
<a href="Hidden%20Volume.html" style="text-align:left; color:#0080c0; text-decoration:none">
скрытых томов VeraCrypt</a>.</span> В качестве альтернативного варианта, если применение скрытой операционной
системы невозможно, используйте "live-CD" с системой Windows PE (целиком хранящейся на CD/DVD и оттуда же
загружающейся), гарантирующей, что все данные, записываемые в системный том, записываются в RAM-диск (диск в ОЗУ).
Монтируйте скрытые тома только тогда, когда работает система с такого "live-CD" (если нельзя использовать
скрытую операционную систему). Кроме того, в течение такого "live-CD"-сеанса в режиме чтения-записи можно
монтировать только файловые системы, расположенные в скрытых томах VeraCrypt (внешние или незашифрованные
тома/файловые системы необходимо монтировать в режиме только для чтения, либо они не должны монтироваться/быть
доступными вовсе). В противном случае вы должны удостовериться, что во время "live-CD"-сеанса приложения
и операционная система не выполняют запись никаких конфиденциальных данных (см. выше) в нескрытые тома/файловые системы.
</li><li style="text-align:left; margin-top:0px; margin-bottom:0px; padding-top:0px; padding-bottom:0px">
<em style="text-align:left">Linux</em>: Загрузите или создайте версию "live-CD" вашей операционной системы
(то есть live-систему Linux, целиком хранящуюся на CD/DVD и оттуда же загружающуюся), это гарантирует, что
любые записанные в системный том данные записаны в RAM-диск (диск в ОЗУ). Монтируйте скрытые тома только тогда,
когда запущена такая "live-CD"-система. В течение сеанса только файловые системы внутри скрытых томов VeraCrypt
могут быть смонтированы в режиме чтения-записи (внешние или незашифрованные тома/файловые системы должны
монтироваться как только для чтения или оставаться вовсе несмонтированными/недоступными). Если вы не можете
соблюсти это требование и не в состоянии гарантировать, что приложения и операционная система не выполняют
запись никаких конфиденциальных данных (см. выше) в нескрытые тома/файловые системы, вы не должны монтировать
или создавать скрытые тома VeraCrypt в среде Linux.
</li><li style="text-align:left; margin-top:0px; margin-bottom:0px; padding-top:0px; padding-bottom:0px">
<em style="text-align:left">Mac OS X</em>: Если вы не гарантируете, что приложения и операционная система
не выполняют запись никаких конфиденциальных данных данных перечисленных выше критических типов в нескрытые
тома (или файловые системы), монтировать или создавать скрытые тома VeraCrypt в среде Mac OS X нельзя.
</li></ul>
</li><li style="text-align:left; margin-top:0px; margin-bottom:0px; padding-top:0px; padding-bottom:0px">
Когда смонтирован внешний том с включённой <a href="Protection%20of%20Hidden%20Volumes.html" style="text-align:left; color:#0080c0; text-decoration:none">
защитой скрытого тома от повреждения</a> (см. раздел <a href="Protection%20of%20Hidden%20Volumes.html">
Защита скрытых томов от повреждения</a>), необходимо следовать тем же требованиям и предостережениям, что и
при монтировании скрытого тома (см. выше). Причина этого в том, что из операционной системы может "утечь"
пароль/ключ для скрытого тома в нескрытый или незашифрованный том.</li><li style="text-align:left; margin-top:0px; margin-bottom:0px; padding-top:0px; padding-bottom:0px">
Если вы используете <strong style="text-align:left">операционную систему, находящуюся внутри скрытого
тома</strong> (см. раздел
<a href="Hidden%20Operating%20System.html" style="text-align:left; color:#0080c0; text-decoration:none">
Скрытая операционная система</a>), то в дополнение к вышесказанному необходимо соблюдать следующие требования
безопасности и предостережения:
<br style="text-align:left">
<ul style="text-align:left; margin-top:18px; margin-bottom:19px; padding-top:0px; padding-bottom:0px">
<li style="text-align:left; margin-top:0px; margin-bottom:0px; padding-top:0px; padding-bottom:0px">
Следует использовать обманную операционную систему так часто, как вы пользуетесь своим компьютером.
В идеале её следует использовать всегда, когда не требуется задействовать засекреченные данные.
В противном случае может пострадать правдоподобность отрицания наличия скрытой операционной системы
(если вы сообщили неприятелю пароль от обманной операционной системы, он сможет выяснить, что эта
система использовалась не слишком часто, что может навести на мысль о существовании в компьютере
скрытой операционной системы). Обратите внимание, что вы можете сохранять данные в разделе с обманной
системой в любой момент и без какого-либо риска повредить скрытый том (так как обманная система
<i>не</i> установлена во внешнем томе).
</li><li style="text-align:left; margin-top:0px; margin-bottom:0px; padding-top:0px; padding-bottom:0px">
Если операционную систему требуется активировать, это нужно сделать до того, как она будет клонирована
(клонирование это часть процесса создания скрытой ОС &mdash; см. раздел
<a href="Hidden%20Operating%20System.html" style="text-align:left; color:#0080c0; text-decoration:none">
Скрытая операционная система</a>), а скрытая операционная система (то есть клон) никогда не должна быть
активирована повторно. Причина в том, что скрытая операционная система создана путём копирования содержимого
системного раздела в скрытый том (поэтому если операционная система не активирована, скрытая операционная
система также будет неактивированной). В случае активации или повторной активации скрытой операционной системы,
дата и время активации (и другая информация) могут быть зафиксированы на сервере Microsoft (и в скрытой
операционной системе), но не в <a href="Hidden%20Operating%20System.html" style="text-align:left; color:#0080c0; text-decoration:none">
обманной операционной системе</a>. Поэтому если неприятель получит доступ к сохранённым на сервере данным
или перехватит ваш запрос серверу (и если вы сообщили ему пароль от обманной операционной системы), он сможет
выяснить, что обманная операционная система была активирована (или повторно активирована) в другое время,
а это способно навести на мысль о существовании в компьютере скрытой операционной системы.
<br style="text-align:left">
По аналогичным причинам любое ПО, требующее активации, должно быть установлено и активировано до того,
как вы приступите к созданию скрытой операционной системы.
</li><li style="text-align:left; margin-top:0px; margin-bottom:0px; padding-top:0px; padding-bottom:0px">
Когда вам нужно завершить работу скрытой операционной системы и запустить обманную систему, <i>не</i>
перезагружайте компьютер. Вместо этого завершите работу системы или переведите её в состояние гибернации (сна),
после чего оставьте компьютер выключенным в течение хотя бы нескольких минут (чем дольше, тем лучше), и
только после этого включите его и загрузите обманную систему. Это требуется, чтобы очистить память, в которой
могут содержаться конфиденциальные данные. Подробности см. в разделе <a href="Unencrypted%20Data%20in%20RAM.html" style="text-align:left; color:#0080c0; text-decoration:none">
Незашифрованные данные в ОЗУ</a>, глава <a href="Security%20Requirements%20and%20Precautions.html" style="text-align:left; color:#0080c0; text-decoration:none">
Требования безопасности и меры предосторожности</a>. </li><li style="text-align:left; margin-top:0px; margin-bottom:0px; padding-top:0px; padding-bottom:0px">
Компьютер может быть подключён к сети (в том числе к Интернету), только когда запущена обманная операционная
система. Когда выполняется скрытая ОС, компьютер не следует подключать ни к какой сети, включая Интернет
(один из самых надёжных способов гарантировать это – отключить от ПК кабель сетевого адаптера, если таковой
имеется). Обратите внимание, что при загрузке данных с/на удалённый сервер, на сервере обычно фиксируются
дата и время соединения и другая информация. Разного сорта данные также протоколируются и в операционной
системе (например, данные автоматического обновления Windows, отчёты приложений, протоколы ошибок и т. п.).
Таким образом, если неприятель получил доступ к хранящимся на сервере данным или перехватил ваш запрос серверу
(и если вы сообщили ему пароль от обманной операционной системы), он сможет узнать, что соединение было
выполнено не из обманной ОС, и это способно навести его на мысль о существовании в вашем компьютере скрытой
операционной системы.
<br style="text-align:left">
<br style="text-align:left">
Также имейте в виду, что аналогичные проблемы возможны, если у вас в среде скрытой операционной системы
есть какие-либо файловые системы с общим доступом через сеть (вне зависимости от того, удалённая файловая
система или локальная). Поэтому во время работы скрытой операционной системы никаких файловых систем с общим
доступом по сети (в любом направлении) быть не должно.<br><br>
</li><li style="text-align:left; margin-top:0px; margin-bottom:0px; padding-top:0px; padding-bottom:0px">
Любые действия, которые могут быть обнаружены неприятелем (или любые действия, модифицирующие какие-либо
данные вне смонтированных скрытых томов), должны выполняться, только когда работает обманная операционная
система (если только у вас нет альтернативного правдоподобного объяснения, например, использование системы
на "live-CD" для выполнения таких действий). Скажем, параметр <i>Автоматический переход на летнее время и обратно</i>
можно включать только в обманной операционной системе.
</li><li style="text-align:left; margin-top:0px; margin-bottom:0px; padding-top:0px; padding-bottom:0px">
Если BIOS, EFI или любой другой компонент журналирует выключения питания или любые другие события, которые
могут свидетельствовать об использовании скрытого тома/системы (например, путём сравнения таких событий с
событиями в протоколе Windows), вы обязаны либо отключить подобное журналирование, либо обеспечить надёжное
удаление журнала после каждого сеанса (или иначе избежать подобной проблемы соответствующим образом).
</li></ul>
</li></ul>
<div style="text-align:left; margin-top:19px; margin-bottom:19px; padding-top:0px; padding-bottom:0px">
<br style="text-align:left">
В дополнение к вышесказанному необходимо соблюдать требования безопасности и меры предосторожности,
перечисленные в следующих главах:</div>
<ul style="text-align:left; margin-top:18px; margin-bottom:19px; padding-top:0px; padding-bottom:0px">
<li style="text-align:left; margin-top:0px; margin-bottom:0px; padding-top:0px; padding-bottom:0px">
<a href="Security%20Requirements%20and%20Precautions.html" style="text-align:left; color:#0080c0; text-decoration:none; font-weight:bold">Требования безопасности и меры предосторожности</a>
</li><li style="text-align:left; margin-top:0px; margin-bottom:0px; padding-top:0px; padding-bottom:0px">
<strong style="text-align:left"><a href="How%20to%20Back%20Up%20Securely.html" style="text-align:left; color:#0080c0; text-decoration:none">О безопасном резервном копировании</a></strong>
</li></ul>
<p><a href="VeraCrypt%20Hidden%20Operating%20System.html" style="text-align:left; color:#0080c0; text-decoration:none; font-weight:bold">Следующий раздел &gt;&gt;</a></p>
<hr align="left" size="1" width="189" style="text-align:left; height:0px; border-width:0px 1px 1px; border-style:solid; border-color:#000000">
<p id="hidden_os_exception"><span style="text-align:left; font-size:10px; line-height:12px">* Это не относится к файловым системам на CD/DVD-подобных носителях, а также к пользовательским, нетипичным или нестандартным устройствам/носителям.</span></p>
</div><div class="ClearBoth"></div></body></html>
