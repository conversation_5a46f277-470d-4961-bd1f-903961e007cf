﻿<!DOCTYPE html>
<html lang="ru">
<head>
<meta http-equiv="content-type" content="text/html; charset=utf-8" />
<title>VeraCrypt - Бесплатное надёжное шифрование дисков с открытым исходным кодом</title>
<meta name="description" content="VeraCrypt это бесплатное программное обеспечение для шифрования дисков с открытым исходным кодом для Windows, Mac OS X (macOS) и Linux. В случае, если злоумышленник вынуждает вас раскрыть пароль, VeraCrypt обеспечивает правдоподобное отрицание наличия шифрования. В отличие от пофайлового шифрования, VeraCrypt шифрует данные в реальном времени (на лету), автоматически, прозрачно, требует очень мало памяти и не использует временные незашифрованные файлы."/>
<meta name="keywords" content="encryption, security, шифрование, безопасность"/>
<link href="styles.css" rel="stylesheet" type="text/css" />
</head>
<body>

<div>
<a href="Documentation.html"><img src="VeraCrypt128x128.png" alt="VeraCrypt"/></a>
</div>

<div id="menu">
	<ul>
	  <li><a href="Home.html">Начало</a></li>
	  <li><a href="Code.html">Исходный код</a></li>
	  <li><a href="Downloads.html">Загрузить</a></li>
	  <li><a class="active" href="Documentation.html">Документация</a></li>
	  <li><a href="Donation.html">Поддержать разработку</a></li>
	  <li><a href="https://sourceforge.net/p/veracrypt/discussion/" target="_blank">Форум</a></li>
	</ul>
</div>

<div>
<p>
<a href="Documentation.html">Документация</a>
<img src="arrow_right.gif" alt=">>" style="margin-top: 5px">
<a href="Main%20Program%20Window.html">Главное окно программы</a>
</p></div>

<div class="wikidoc">
<h1>Главное окно программы</h1>
<h3>Выбрать файл</h3>
<p>Позволяет выбрать том VeraCrypt на основе файла. После выбора вы можете выполнить с томом различные операции (например,
смонтировать его, нажав кнопку <i>Смонтировать</i>). Выбрать том также можно перетаскиванием его значка на значок файла
&lsquo;VeraCrypt.exe&rsquo; (при этом VeraCrypt будет автоматически запущен) или в главное окно программы.</p>
<h3>Выбрать устройство</h3>
<p>Позволяет выбрать раздел VeraCrypt или устройство хранения данных (например, USB-флешку). После выбора вы можете
выполнить с томом различные операции (например, смонтировать его, нажав кнопку <i>Смонтировать</i>).<br>
<br>
Примечание. Монтировать разделы/устройства VeraCrypt можно и более удобным способом – см. подробности в разделе
<em>Автомонтирование</em>.</p>
<h3>Смонтировать</h3>
<p>После того, как вы нажмёте кнопку <i>Монтировать</i>, программа VeraCrypt попытается смонтировать выбранный том,
используя кэшированные (временно сохранённые в памяти) пароли (если таковые имеются), и если ни один из них не подойдёт,
попросит ввести пароль. Если вы введёте правильный пароль (и/или укажете корректные ключевые файлы), том будет смонтирован.</p>
<p>ВАЖНО: Обратите внимание, что когда вы закрываете программу VeraCrypt, её драйвер продолжает работать, и никакие тома
VeraCrypt не размонтируются.</p>
<h3 id="AutoMountDevices">Автомонтирование</h3>
<p>Эта функция позволяет монтировать разделы/устройства VeraCrypt без необходимости выбирать их вручную (кнопкой
<i>Выбрать устройство</i>). VeraCrypt поочерёдно сканирует заголовки всех доступных разделов/устройств в системе
(за исключением накопителей DVD и аналогичных устройств) и пытается смонтировать каждый из них как том VeraCrypt.
Обратите внимание, что ни том/устройство VeraCrypt, ни шифр, применявшийся при их шифровании, невозможно идентифицировать.
По этой причине программа не может просто "найти" разделы VeraCrypt. Вместо этого она пытается выполнить монтирование
каждого (даже незашифрованного) раздела/устройства с помощью всех алгоритмов шифрования и всех сохранённых в кэше
паролей (если таковые имеются). Поэтому будьте готовы к тому, что на медленных компьютерах данный процесс может
занять много времени.<br>
<br>
Если введён неправильный пароль, выполняется попытка монтирования, используя кэшированные пароли (если они есть).
Если вы указали пустой пароль и не выбрали опцию <em>Ключевые файлы</em>, то при попытке автомонтирования
разделов/устройств будут использоваться только кэшированные пароли. Если вам не нужно указывать параметры монтирования,
то можно избежать появления запроса пароля: для этого при нажатии кнопки <em>Автомонтирование</em> удерживайте нажатой
клавишу <em>Shift</em> (при этом будут использоваться только кэшированные пароли, если они есть).<br>
<br>
Буквы дисков будут назначены начиная с той, которая была выбрана в списке дисков в главном окне.</p>
<h3>Размонтировать</h3>
<p>Эта функция позволяет размонтировать том VeraCrypt, выбранный в списке дисков на главном окне программы.
Размонтировать – значит закрыть этот том и сделать для него недоступными операции чтения/записи.</p>
<h3>Размонтировать все</h3>
<p>Примечание. Информация в этом разделе применима ко всем элементам меню и кнопкам с таким же или похожим названием
(например, она также относится к пункту <em>Размонтировать все</em> в системной области уведомлений).<br>
<br>
Эта функция позволяет размонтировать сразу несколько томов VeraCrypt. Размонтировать – значит закрыть этот том
и сделать для него недоступными операции чтения/записи. Данная функция размонтирует все смонтированные тома VeraCrypt,
за исключением следующих:</p>
<ul>
<li>разделы/диски внутри области действия ключа шифрования активной системы (например, системный раздел, зашифрованный
VeraCrypt, или несистемный раздел на системном диске, зашифрованном VeraCrypt, смонтированный во время работы
зашифрованной операционной системы);</li>
<li>тома VeraCrypt, не полностью доступные из-под учётной записи пользователя (например, том, смонтированный из-под
другой учётной записи);</li>
<li>тома VeraCrypt, не отображаемые в окне программы VeraCrypt. Например, системные избранные тома, которые пытались
размонтировать с помощью экземпляра VeraCrypt без прав администратора при включённом параметре
<em>Просматривать/размонтировать системные избранные тома могут лишь администраторы</em>. </li></ul>
<h3>Очистить кэш</h3>
<p>Удаляет из памяти драйвера все кэшированные пароли (где также может находиться содержимое обработанных ключевых файлов).
Если в кэше нет паролей, эта кнопка неактивна. О кэшировании паролей см. в разделе 
<a href="Mounting%20VeraCrypt%20Volumes.html"><em>Кэшировать пароли в памяти драйвера</em></a>.</p>
<h3>Не сохранять историю</h3>
<p>Если эта опция <i>не включена</i>, имена файлов и/или пути последних двадцати файлов/устройств, которые вы пытались
смонтировать как тома VeraCrypt, будут запоминаться в файле истории (его содержимое отображается при щелчке по стрелке
у выпадающего списка <i>Том</i> в главном окне программы).<br>
<br>
Если эта опция <i>включена</i>, TrueCrypt очищает записи в реестре, созданные диалоговыми окнами выбора файлов Windows для
VeraCrypt и делает "текущей папкой" домашнюю папку пользователя (в переносном режиме – папку, из которой был запущен
VeraCrypt) вне зависимости от того, что выбиралось в диалоговом окне выбора – контейнер или ключевой файл. Поэтому
Windows-диалог выбора файлов не будет запоминать путь последнего смонтированного контейнера (или последнего выбранного
ключевого файла). Учтите, однако, что описанные в этом разделе операции <i>не</i> гарантируют надёжность и безопасность
(например, см. <a href="Security%20Requirements%20and%20Precautions.html">
<em>Требования безопасности и меры предосторожности</em></a>), поэтому настоятельно рекомендуется на них не полагаться,
а шифровать системный раздел/диск (см.
<a href="System%20Encryption.html"><em>Шифрование системы</em></a>).<br>
<br>
Кроме того, если эта опция включена, поле ввода пути к тому в главном окне программы очищается свякий раз, когда вы
скрываете VeraCrypt.<br>
<br>
Примечание. Чтобы очистить историю томов, выберите в меню <em>Сервис</em> команду <em>Очистить историю томов</em>.</p>
<h3>Выход</h3>
<p>Завершает работу программы VeraCrypt. При этом драйвер продолжает работать, и никакие тома VeraCrypt не размонтируются.
При работе в переносном (&lsquo;portable&rsquo;) режиме драйвер VeraCrypt выгружается, если он больше не требуется
(например, когда все копии главного приложения и/или мастера создания томов закрыты и нет смонтированных томов VeraCrypt).
Однако если вы принудительно размонтируете том VeraCrypt, когда программа работает в переносном режиме, или смонтируете
доступный для записи отформатированный как NTFS том в среде Windows Vista или более новых версиях Windows, в этом случае
драйвер VeraCrypt может <em>не</em> быть выгруженным при выходе из VeraCrypt (он будет выгружен только при завершении
работы системы или её перезагрузке). Таким образом предотвращаются различные проблемы, обусловленные ошибкой в Windows
(например, был бы невозможен повторный запуск VeraCrypt, пока есть какие-либо приложения, использующие размонтированный том).</p>
<h3>Операции с томами</h3>
<h4>Изменить пароль тома</h4>
<p>См. раздел <a href="Program%20Menu.html">
<em>Тома &gt; Изменить пароль тома</em></a>.</p>
<h4>Установить алгоритм формирования ключа заголовка</h4>
<p>См. раздел <a href="Program%20Menu.html">
<em>Тома &gt; Установить алгоритм формирования ключа заголовка</em></a>.</p>
<h4>Создать резервную копию заголовка тома</h4>
<p>См. раздел <a href="Program%20Menu.html#tools-backup-volume-header">
<em>Сервис &gt; Создать резервную копию заголовка тома</em></a>.</p>
<h4>Восстановить заголовок тома</h4>
<p>См. раздел <a href="Program%20Menu.html#tools-restore-volume-header">
<em>Сервис &gt; Восстановить заголовок тома</em></a>.</p>
<p>&nbsp;</p>
<p><a href="Program%20Menu.html" style="text-align:left; color:#0080c0; text-decoration:none; font-weight:bold">Следующий раздел &gt;&gt;</a></p>
</div><div class="ClearBoth"></div></body></html>
