﻿<!DOCTYPE html>
<html lang="ru">
<head>
<meta http-equiv="content-type" content="text/html; charset=utf-8" />
<title>VeraCrypt - Бесплатное надёжное шифрование дисков с открытым исходным кодом</title>
<meta name="description" content="VeraCrypt это бесплатное программное обеспечение для шифрования дисков с открытым исходным кодом для Windows, Mac OS X (macOS) и Linux. В случае, если злоумышленник вынуждает вас раскрыть пароль, VeraCrypt обеспечивает правдоподобное отрицание наличия шифрования. В отличие от пофайлового шифрования, VeraCrypt шифрует данные в реальном времени (на лету), автоматически, прозрачно, требует очень мало памяти и не использует временные незашифрованные файлы."/>
<meta name="keywords" content="encryption, security, шифрование, безопасность"/>
<link href="styles.css" rel="stylesheet" type="text/css" />
</head>
<body>

<div>
<a href="Documentation.html"><img src="VeraCrypt128x128.png" alt="VeraCrypt"/></a>
</div>

<div id="menu">
	<ul>
	  <li><a href="Home.html">Начало</a></li>
	  <li><a href="Code.html">Исходный код</a></li>
	  <li><a href="Downloads.html">Загрузить</a></li>
	  <li><a class="active" href="Documentation.html">Документация</a></li>
	  <li><a href="Donation.html">Поддержать разработку</a></li>
	  <li><a href="https://sourceforge.net/p/veracrypt/discussion/" target="_blank">Форум</a></li>
	</ul>
</div>

<div>
<p>
<a href="Documentation.html">Документация</a>
<img src="arrow_right.gif" alt=">>" style="margin-top: 5px">
<a href="Command%20Line%20Usage.html">Использование в режиме командной строки</a>
</p></div>

<div class="wikidoc">
<div>
<h1>Использование в режиме командной строки</h1>
<p>Информация в этом разделе относится к версии VeraCrypt для Windows. Чтобы получить сведения об использовании в режиме командной строки
<strong>версий для Linux и Mac OS X</strong>, выполните следующую команду: <code>veracrypt -h</code></p>
<table border="1" cellspacing="0" cellpadding="1">
<tbody>
<tr>
<td>&nbsp;<em>/help</em> или <em>/?</em></td>
<td>Показать справку по использованию в командной строке.</td>
</tr>
<tr>
<td>&nbsp;<em>/truecrypt</em> или <em>/tc</em></td>
<td>Активировать режим совместимости с TrueCrypt, который позволяет монтировать тома, созданные в TrueCrypt версий 6.x и 7.x.</td>
</tr>
<tr>
<td>&nbsp;<em>/hash</em></td>
<td>После этого ключа указывается хеш-алгоритм PRF, используемый при монтировании тома. Возможные значения ключа /hash: <code>sha256, sha-256, sha512, sha-512, whirlpool, blake2s</code> и <code>blake2s-256</code>. Если ключ <code>/hash</code> не указан, VeraCrypt будет пробовать
 все доступные PRF-алгоритмы, тем самым увеличивая время монтирования.</td>
</tr>
<tr>
<td id="volume">&nbsp;<em>/volume</em> или <em>/v</em></td>
<td>
<p>После этого ключа указывается полное имя (с путём) файла с томом VeraCrypt для монтирования (не используйте при размонтировании) или идентификатор тома (Volume ID) диска/раздела для монтирования.<br>
Синтаксис идентификатора тома следующий: <code>ID:XXXXXX...XX</code>, где часть с XX это строка из 64 шестнадцатеричных символов, которая представляет собой 32-байтовый идентификатор тома для монтирования.<br>
<br>
Чтобы смонтировать том на основе раздела/устройства, используйте, например, параметры <code>/v \Device\Harddisk1\Partition3</code> (узнать путь к разделу/устройству можно, запустив VeraCrypt и нажав кнопку
<em>Выбрать устройство</em>). Монтировать раздел или динамический том также можно используя его имя тома (например, <code>/v \\?\Volume{5cceb196-48bf-46ab-ad00-70965512253a}\</code>). Узнать имя тома можно, например, с помощью mountvol.exe. Также помните, что в путях устройств учитывается регистр букв.<br>
<br>
Для монтирования также можно указывать идентификатор монтируемого тома (Volume ID) на основе раздела/устройства, например: <code>/v ID:53B9A8D59CC84264004DA8728FC8F3E2EE6C130145ABD3835695C29FD601EDCA</code>. Значение идентификатора тома можно получить с помощью диалогового окна свойств тома.</p>
</td>
</tr>
<tr>
<td>&nbsp;<em>/letter</em> или <em>/l</em></td>
<td>После этого ключа указывается буква диска, присваиваемая монтируемому тому. Если ключ <code>/l</code> не указан и используется ключ <code>/a</code>, тогда тому присваивается первая незанятая буква диска.</td>
</tr>
<tr>
<td>&nbsp;<em>/explore</em> или <em>/e</em></td>
<td>Открыть окно Проводника после монтирования тома.</td>
</tr>
<tr>
<td>&nbsp;<em>/beep</em> или <em>/b</em></td>
<td>Звуковой сигнал после успешного монтирования или размонтирования.</td>
</tr>
<tr>
<td>&nbsp;<em>/auto</em> или <em>/a</em></td>
<td>Если этот ключ указан без параметров, то выполняется автоматическое монтирование тома. Если указан параметр <code>devices</code> (например, <code>/a devices</code>), то выполняется автомонтирование всех доступных в данный момент томов VeraCrypt на основе устройств/разделов. Если указан параметр <code>favorites</code>, то выполняется автомонтирование
 избранных томов. Обратите внимание, что ключ <code>/auto</code> подразумевается, если указаны ключи <code>/quit</code> и <code>/volume</code>. Если требуется подавить вывод на экран окна программы, используйте ключ <code>/quit</code>.</td>
</tr>
<tr>
<td>&nbsp;<em>/unmount</em> или <em>/u</em></td>
<td>Размонтировать том с указанной буквой диска (пример: <code>/u x</code>). Если буква диска не указана, то будут размонтированы все смонтированные на данный момент тома VeraCrypt.</td>
</tr>
<tr>
<td>&nbsp;<em>/force</em> или <em>/f</em></td>
<td>Принудительно размонтировать (если размонтируемый том содержит файлы, используемые системой или какой-либо программой) и принудительно смонтировать в совместно используемом (shared) режиме (то есть без эксклюзивного доступа).</td>
</tr>
<tr>
<td>&nbsp;<em>/keyfile</em> или <em>/k</em></td>
<td>После этого ключа указывается ключевой файл или путь поиска ключевых файлов. Если ключевых файлов несколько, то они указываются, например, так: <code>/k c:\keyfile1.dat /k d:\KeyfileFolder /k c:\kf2</code>. Чтобы указать ключевой файл, находящийся на токене безопасности или смарт-карте, используйте следующий синтаксис:
<code>token://slot/SLOT_NUMBER/file/FILE_NAME</code></td>
</tr>
<tr id="tryemptypass">
<td>&nbsp;<em>/tryemptypass&nbsp;&nbsp; </em></td>
<td>Этот ключ применяется, <em>только</em> если сконфигурирован ключевой файл по умолчанию или ключевой файл указан в командной строке.<br>
Если после этого ключа указан параметр <strong>y</strong> или <strong>yes</strong>, либо параметр не указан: попытаться смонтировать, используя пустой пароль и ключевой файл, прежде чем показать запрос пароля.<br>
Если после этого ключа указан параметр <strong>n</strong> или <strong>no</strong>: не пытаться смонтировать, используя пустой пароль и ключевой файл, и сразу показать запрос пароля.</td>
</tr>
<tr>
<td>&nbsp;<em>/nowaitdlg</em></td>
<td>Если после этого ключа указан параметр <strong>y</strong> или <strong>yes</strong>, либо параметр не указан: не показывать окно ожидания при выполнении таких операций, как, например, монтирование томов.<br>
Если после этого ключа указан параметр <strong>n</strong> или <strong>no</strong>: принудительно показывать окно ожидания при выполнении операций.</td>
</tr>
<tr>
<td>&nbsp;<em>/secureDesktop</em></td>
<td>Если после этого ключа указан параметр <strong>y</strong> или <strong>yes</strong>, либо параметр не указан: показывать окно пароля и окно пин-кода токена на выделенном безопасном рабочем столе для защиты от определённых типов атак.<br>
Если после этого ключа указан параметр <strong>n</strong> или <strong>no</strong>: окно пароля и окно PIN-кода токена отображаются на обычном рабочем столе.</td>
</tr>
<tr>
<td>&nbsp;<em>/tokenlib</em></td>
<td>После этого ключа указывается библиотека PKCS #11 для токенов безопасности и смарт-карт (пример: <code>/tokenlib c:\pkcs11lib.dll</code>).</td>
</tr>
<tr>
<td>&nbsp;<em>/tokenpin</em></td>
<td>После этого ключа указывается пин-код для аутентификации с помощью токена безопасности или смарт-карты (пример: <code>/tokenpin 0000</code>).
 ВНИМАНИЕ: Этот метод ввода пин-кода смарт-карты может быть небезопасным, например, когда незашифрованный журнал истории командной строки сохраняется на незашифрованном диске.</td>
</tr>
<tr>
<td>&nbsp;<em>/cache</em> или <em>/c</em></td>
<td>Если после этого ключа указан параметр <strong>y</strong> или <strong>yes</strong>, либо параметр не указан: включить кэш паролей.
<br>
Если после этого ключа указан параметр <strong>p</strong> или <strong>pim</strong>: включить кэш паролей и PIM (пример: <code>/c p</code>).<br>
Если после этого ключа указан параметр <strong>n</strong> или <strong>no</strong>: отключить кэш паролей (пример: <code>/c n</code>).<br>
Если после этого ключа указан параметр <strong>f</strong> или <strong>favorites</strong>: временно кэшировать пароль при монтировании нескольких избранных томов (пример: <code>/c f</code>).<br>
Обратите внимание, что отключение кэша паролей не очищает его (чтобы очистить кэш паролей, используйте ключ <code>/w</code>).</td>
</tr>
<tr>
<td>&nbsp;<em>/history</em> или <em>/h</em></td>
<td>Если после этого ключа указан параметр <strong>y</strong> или параметр не указан: включить сохранение истории смонтированных томов.<br>
Если после этого ключа указан параметр <strong>n</strong>: отключить сохранение истории смонтированных томов (пример: <code>/h n</code>).</td>
</tr>
<tr>
<td>&nbsp;<em>/wipecache</em> или <em>/w</em></td>
<td>Удалить все пароли, кэшированные (сохранённые) в памяти драйвера.</td>
</tr>
<tr>
<td>&nbsp;<em>/password</em> или <em>/p</em></td>
<td>После этого ключа указывается пароль тома. Если в пароле есть пробелы, его необходимо заключить в двойные кавычки (пример: <code>/p &rdquo;My Password&rdquo;</code>). Чтобы указать пустой пароль, используйте ключ <code>/p &rdquo;&rdquo;</code>.
<em>ВНИМАНИЕ: Такой метод ввода пароля может быть небезопасен, например, если на незашифрованном диске записывается незашифрованная история операций в командной строке.</em></td>
</tr>
<tr>
<td>&nbsp;<em>/pim</em></td>
<td>После этого ключа указывается положительное целое число, определяющее PIM (Персональный множитель итераций) для использования с этим томом.</td>
</tr>
<tr>
<td>&nbsp;<em>/quit</em> или <em>/q</em></td>
<td>Автоматически выполнить запрошенные действия и выйти (без отображения главного окна VeraCrypt). Если в качестве параметра указано <code>preferences</code> (пример: <code>/q preferences</code>), то будут загружены/сохранены настройки программы, и они переопределят параметры, указанные в командной строке.
Ключ <code>/q background</code> запускает VeraCrypt в фоновом режиме (значок в области уведомлений), если только это не запрещено в настройках.</td>
</tr>
<tr>
<td>&nbsp;<em>/silent</em> или <em>/s</em></td>
<td>При указании вместе с ключом <code>/q</code> подавляет взаимодействие с пользователем (запросы, сообщения об ошибках, предупреждения и т. д.). Если ключ <code>/q</code> не указан, этот параметр никакого действия не оказывает.</td>
</tr>
<tr>
<td>&nbsp;<em>/mountoption</em> или <em>/m</em></td>
<td>
<p>После этого ключа указывается один из перечисленных ниже параметров.</p>
<p><strong>ro</strong> или <strong>readonly</strong>: смонтировать том как доступный только для чтения.</p>
<p><strong>rm</strong> или <strong>removable</strong>: смонтировать том как сменный носитель (см. раздел
<a href="Removable%20Medium%20Volume.html">
<em>Том, смонтированный как сменный носитель</em></a>).</p>
<p><strong>ts</strong> или <strong>timestamp</strong>: не сохранять дату и время изменения контейнера.</p>
<p><strong>sm</strong> или <strong>system</strong>: смонтировать без предзагрузочной аутентификации раздел, входящий в область действия шифрования системы (например, раздел на зашифрованном системном диске с другой операционной системой, которая в данный момент не выполняется).
 Полезно, например, для операций резервного копирования или починки. Примечание: если вы указываете пароль как параметр ключа /p, убедитесь, что пароль набран с использованием стандартной американской раскладки клавиатуры (при использовании графического интерфейса программы это делается автоматически). Это необходимо потому,
 что пароль требуется вводить на этапе до загрузки операционной системы (до запуска Windows), когда раскладки клавиатуры, отличные от американской, ещё недоступны.</p>
<p><strong>bk</strong> или <strong>headerbak</strong>: смонтировать том, используя встроенную резервную копию заголовка. Примечание: встроенная резервная копия заголовка содержится во всех томах, созданных VeraCrypt (эта копия располагается в конце тома).</p>
<p><strong>recovery</strong>: не проверять контрольные суммы, хранящиеся в заголовке тома. Этот параметр следует использовать только при повреждении заголовка тома и когда такой том невозможно смонтировать даже с параметром headerbak. Пример: /m ro</p>
<p><strong>label=LabelValue</strong>: использовать указанную строку <strong>LabelValue</strong> как метку смонтированного тома в Проводнике Windows. Максимальная длина
<strong>LabelValue&nbsp;</strong> – 32 символа для томов NTFS и 11 символов для томов FAT. Например,
<code>/m label=MyDrive</code> установит для диска в Проводнике метку тома <em>MyDrive</em>.</p>
<p><strong>noattach</strong>: создать только виртуальное устройство без фактического присоединения смонтированного тома к выбранной букве диска.</p>
<p>Обратите внимание, что этот ключ может присутствовать в командной строке несколько раз, чтобы указать несколько вариантов монтирования (пример: /m rm /m ts)</p>
</td>
</tr>
<tr>
<td>&nbsp;<em>/DisableDeviceUpdate</em>&nbsp;</td>
<td>Отключить периодическую внутреннюю проверку устройств, подключённых к системе, которая используется для обработки избранного, идентифицированного с помощью идентификаторов томов (Volume ID), и заменить её проверками по требованию.</td>
</tr>
<tr>
<td>&nbsp;<em>/protectMemory</em>&nbsp;</td>
<td>Активировать механизм защиты памяти процесса VeraCrypt от доступа со стороны других процессов без прав администратора.</td>
</tr>
<tr>
<td>&nbsp;<em>/signalExit</em>&nbsp;</td>
<td>После этого ключа должен следовать параметр, определяющий имя отправляемого сигнала для разблокирования ожидания команды <a href="https://docs.microsoft.com/en-us/windows-server/administration/windows-commands/waitfor" target="_blank">WAITFOR.EXE</a> при завершении работы VeraCrypt.<br>
Имя сигнала должно совпадать с указанным в команде WAITFOR.EXE (пример: <code>veracrypt.exe /q /v test.hc /l Z /signal SigName</code> с последующим <code>waitfor.exe SigName</code><br>
Если <code>/q</code> не указан, этот ключ игнорируется.</td>
</tr>
</tbody>
</table>
<h4>VeraCrypt Format.exe (Мастер создания томов VeraCrypt):</h4>
<table border="1" cellspacing="0" cellpadding="0">
<tbody>
<tr>
<td>&nbsp;<em>/create</em></td>
<td>Создать том на основе файла-контейнера в режиме командной строки. За этим ключом должно следовать имя файла создаваемого контейнера.</td>
</tr>
<tr>
<td>&nbsp;<em>/size</em></td>
<td>
<p>(Только с ключом /create)<br>
После этого ключа указывается размер создаваемого файла-контейнера. Если указано просто число без суффикса, то оно обозначает размер в байтах. Если после числа добавить суффикс "K", "M", "G" или "T", то это будет означать размер, соответственно, в килобайтах, мегабайтах, гигабайтах или терабайтах.
Примеры:</p>
<ul>
<li><code>/size 5000000</code>: размер контейнера – 5 000 000 байт</li><li><code>/size 25K</code>: размер контейнера – 25 килобайт</li><li><code>/size 100M</code>: размер контейнера – 100 мегабайт </li><li><code>/size 2G</code>: размер контейнера – 2 гигабайта</li><li><code>/size 1T</code>: размер контейнера – 1 терабайт</li></ul>
</td>
</tr>
<tr>
<td>&nbsp;<em>/password</em></td>
<td>(Только с ключом <code>/create</code>)<br>
После этого ключа указывается пароль к создаваемому тому.</td>
</tr>
<tr>
<td>&nbsp;<em>/keyfile</em> или <em>/k</em></td>
<td>(Только с ключом <code>/create</code>)<br>
После этого ключа указывается ключевой файл или путь поиска ключевых файлов к создаваемому тому. Чтобы использовать несколько ключевых файлов, укажите, например, такие параметры: <code>/k c:\keyfile1.dat /k d:\KeyfileFolder /k c:\kf2</code>. Чтобы указать ключевой файл, расположенный на токене безопасности или смарт-карте, используйте следующий синтаксис:
<code>token://slot/SLOT_NUMBER/file/FILE_NAME</code></td>
</tr>
<tr>
<td>&nbsp;<em>/tokenlib</em></td>
<td>(Только с ключом <code>/create</code>)<br>
После этого ключа указывается библиотека PKCS #11 для использования с токенами безопасности и смарт-картами (пример: <code>/tokenlib c:\pkcs11lib.dll</code>).</td>
</tr>
<tr>
<td>&nbsp;<em>/tokenpin</em></td>
<td>(Только с ключом <code>/create</code>)<br>
После этого ключа указывается пин-код для аутентификации с помощью токена безопасности или смарт-карты (пример: <code>/tokenpin 0000</code>). ВНИМАНИЕ: Этот метод
ввода пин-кода смарт-карты может быть небезопасным, например, когда незашифрованный журнал истории командной строки сохраняется на незашифрованном диске.</td>
</tr>
<tr>
<td>&nbsp;<em>/hash</em></td>
<td>(Только с ключом <code>/create</code>)<br>
После этого ключа указывается хеш-алгоритм PRF, используемый при создании тома. Синтаксис такой же, как у VeraCrypt.exe.</td>
</tr>
<tr>
<td>&nbsp;<em>/encryption</em></td>
<td>(Только с ключом <code>/create</code>)<br>
После этого ключа указывается используемый алгоритм шифрования. По умолчанию это AES, если данный ключ не указан. Возможны следующие значения (регистр букв не учитывается):
<ul>
<li><code>AES </code></li><li><code>Serpent </code></li><li><code>Twofish </code></li><li><code>Camellia </code></li><li><code>Kuznyechik </code></li><li><code>AES(Twofish) </code></li><li><code>AES(Twofish(Serpent)) </code></li><li><code>Serpent(AES) </code></li><li><code>Serpent(Twofish(AES)) </code></li><li><code>Twofish(Serpent) </code></li>
<li><code>Camellia(Kuznyechik) </code></li>
<li><code>Kuznyechik(Twofish) </code></li>
<li><code>Camellia(Serpent) </code></li>
<li><code>Kuznyechik(AES) </code></li>
<li><code>Kuznyechik(Serpent(Camellia))</code></li>
</ul>
</td>
</tr>
<tr>
<td>&nbsp;<em>/filesystem</em></td>
<td>(Только с ключом <code>/create</code>)<br>
После этого ключа указывается файловая система для использования в томе. Возможны следующие значения:
<ul>
<li>Значение не указано: не использовать никакую файловую систему</li><li>FAT: форматировать в FAT/FAT32 </li><li>NTFS: форматировать в NTFS (в этом случае появится окно с запросом UAC, если только процесс не выполняется с полными правами администратора)
</li>
<li>ExFAT: форматировать в ExFAT (этот ключ доступен начиная с Windows Vista SP1)</li>
<li>ReFS: форматировать в ReFS (этот ключ доступен начиная с Windows 10)</li>
</ul>
</td>
</tr>
<tr>
<td>&nbsp;<em>/dynamic</em></td>
<td>(Только с ключом <code>/create</code>)<br>
Создать динамический том.</td>
</tr>
<tr>
<td>&nbsp;<em>/force</em></td>
<td>(Только с ключом <code>/create</code>)<br>
Принудительная перезапись без запроса.</td>
</tr>
<tr>
<td>&nbsp;<em>/silent</em></td>
<td>(Только с ключом <code>/create</code>)<br>
Не показывать окна с сообщениями. Если произойдёт какая-либо ошибка, операция завершится без сообщений.</td>
</tr>
<tr>
<td>&nbsp;<em>/noisocheck</em> или <em>/n</em></td>
<td>Не проверять правильность записи на носители дисков восстановления VeraCrypt (Rescue Disk). <strong>ВНИМАНИЕ</strong>: Никогда не пытайтесь применять этот ключ, чтобы облегчить повторное использование ранее созданного Диска восстановления VeraCrypt. Помните, что при каждом шифровании системного раздела/диска
 нужно создавать новый Диск восстановления VeraCrypt, даже если используете тот же пароль. Ранее созданный Диск восстановления нельзя использовать повторно, так как он был создан для другого мастер-ключа.</td>
</tr>
<tr>
<td>&nbsp;<em>/nosizecheck</em></td>
<td>Не проверять Не проверять, что заданный размер файлового контейнера меньше, чем доступно на места на диске. Это относится как к пользовательскому интерфейсу, так и к командной строке.</td>
</tr>
<tr>
<td>&nbsp;<em>/quick</em></td>
<td>Выполнять быстрое форматирование томов вместо полного форматирования. Это относится как к пользовательскому интерфейсу, так и к командной строке.</td>
</tr>
<tr>
<td>&nbsp;<em>/FastCreateFile</em></td>
<td>Использовать более быстрый, хотя и потенциально небезопасный метод создания файловых контейнеров. Эта опция сопряжена с риском для безопасности, поскольку способна встроить существующее содержимое диска в файловый контейнер, что может привести к раскрытию конфиденциальных данных, если злоумышленник получит к нему доступ. Обратите внимание, что этот ключ влияет на все методы создания файловых контейнеров, независимо от того, запущены ли они из командной строки с параметром <em>/create</em> или с помощью мастера из интерфейса пользователя.</td>
</tr>
<tr>
<td>&nbsp;<em>/protectMemory</em>&nbsp;</td>
<td>Активировать механизм защиты памяти процесса VeraCrypt Format от доступа со стороны других процессов без прав администратора.</td>
</tr>
<tr>
<td>&nbsp;<em>/secureDesktop</em></td>
<td>Если после этого ключа указан параметр <strong>y</strong> или <strong>yes</strong>, либо параметр не указан: показывать окно пароля и окно PIN-кода токена на выделенном безопасном рабочем столе для защиты от определённых типов атак.<br>
Если после этого ключа указан параметр <strong>n</strong> или <strong>no</strong>: окно пароля и окно PIN-кода токена отображаются на обычном рабочем столе.</td>
</tr>
</tbody>
</table>
<h4>Синтаксис</h4>
<p><code>VeraCrypt.exe [/tc] [/hash {sha256|sha-256|sha512|sha-512|whirlpool |blake2s|blake2s-256}][/a [devices|favorites]] [/b] [/c [y|n|f]] [/d [буква диска]] [/e] [/f] [/h [y|n]] [/k ключевой файл или путь поиска] [tryemptypass [y|n]] [/l буква диска] [/m {bk|rm|recovery|ro|sm|ts|noattach}]
 [/p пароль] [/pim значение pim] [/q [background|preferences]] [/s] [/tokenlib путь] [/v том] [/w]</code></p>
<p><code>&quot;VeraCrypt Format.exe&quot; [/n] [/create] [/size число[{K|M|G|T}]] [/p пароль]&nbsp; [/encryption {AES | Serpent | Twofish | Camellia | Kuznyechik | AES(Twofish) | AES(Twofish(Serpent)) | Serpent(AES) | Serpent(Twofish(AES)) | Twofish(Serpent) | Camellia(Kuznyechik) | Kuznyechik(Twofish) | Camellia(Serpent) | Kuznyechik(AES) | Kuznyechik(Serpent(Camellia))}] [/hash {sha256|sha-256|sha512|sha-512|whirlpool|blake2s|blake2s-256}]
 [/filesystem {пусто|FAT|NTFS|ExFAT|ReFS}] [/dynamic] [/force] [/silent] [/noisocheck] [FastCreateFile] [/quick]</code></p>
<p>Порядок, в котором указаны параметры, не имеет значения.</p>
<h4>Примеры</h4>
<p>Смонтировать том <em>d:\myvolume</em> на первую свободную букву диска, используя запрос пароля (основное окно программы не отображается):</p>
<p><code>veracrypt /q /v d:\myvolume</code></p>
<p>Размонтировать том, смонтированный как диск <em>X</em> (основное окно программы не отображается):</p>
<p><code>veracrypt /q /d x</code></p>
<p>Смонтировать том с именем <em>myvolume.tc</em>, используя пароль <em>MyPassword</em>, как диск
<em>X</em>. VeraCrypt откроет окно Проводника и подаст звуковой сигнал; монтирование будет автоматическим:</p>
<p><code>veracrypt /v myvolume.tc /l x /a /p MyPassword /e /b</code></p>
<p>Создать файловый контейнер размером 10 МБ, используя пароль <em>test</em>, шифрование Serpent, хеш SHA-512, и отформатировав том в FAT:</p>
<p><code>&quot;C:\Program Files\VeraCrypt\VeraCrypt Format.exe&quot; /create c:\Data\test.hc /password test /hash sha512 /encryption serpent /filesystem FAT /size 10M /force</code></p>
</div>
</div><div class="ClearBoth"></div></body></html>
