﻿<!DOCTYPE html>
<html lang="ru">
<head>
<meta http-equiv="content-type" content="text/html; charset=utf-8" />
<title>VeraCrypt - Бесплатное надёжное шифрование дисков с открытым исходным кодом</title>
<meta name="description" content="VeraCrypt это бесплатное программное обеспечение для шифрования дисков с открытым исходным кодом для Windows, Mac OS X (macOS) и Linux. В случае, если злоумышленник вынуждает вас раскрыть пароль, VeraCrypt обеспечивает правдоподобное отрицание наличия шифрования. В отличие от пофайлового шифрования, VeraCrypt шифрует данные в реальном времени (на лету), автоматически, прозрачно, требует очень мало памяти и не использует временные незашифрованные файлы."/>
<meta name="keywords" content="encryption, security, шифрование, безопасность"/>
<link href="styles.css" rel="stylesheet" type="text/css" />
</head>
<body>

<div>
<a href="Documentation.html"><img src="VeraCrypt128x128.png" alt="VeraCrypt"/></a>
</div>

<div id="menu">
	<ul>
	  <li><a href="Home.html">Начало</a></li>
	  <li><a href="Code.html">Исходный код</a></li>
	  <li><a href="Downloads.html">Загрузить</a></li>
	  <li><a class="active" href="Documentation.html">Документация</a></li>
	  <li><a href="Donation.html">Поддержать разработку</a></li>
	  <li><a href="https://sourceforge.net/p/veracrypt/discussion/" target="_blank">Форум</a></li>
	</ul>
</div>

<div>
<p>
<a href="Documentation.html">Документация</a>
<img src="arrow_right.gif" alt=">>" style="margin-top: 5px">
<a href="Issues%20and%20Limitations.html">Замеченные проблемы и ограничения</a>
</p></div>

<div class="wikidoc">
<h1>Замеченные проблемы и ограничения</h1>
<h3>Замеченные проблемы</h3>
<ul>
<li>В Windows возможна ситуация, когда смонтированному тому будут назначены две буквы диска вместо одной. Это вызвано
проблемой с кэшем диспетчера монтирования Windows, и её можно решить, вводя в командной строке с повышенными правами
(от имени администратора) команду &quot;<strong>mountvol.exe /r</strong>&quot; перед монтированием любого тома.
Если проблема не исчезнет после перезагрузки, для её решения можно использовать следующую процедуру:
<ul>
<li>С помощью редактора реестра откройте в реестре ключ &quot;HKEY_LOCAL_MACHINE\SYSTEM\MountedDevices&quot;.
Прокрутите содержимое окна вниз, пока не обнаружите записи, начинающиеся с &quot;\DosDevices\&quot; или
&quot;\Global??\&quot;, которые указывают буквы дисков, используемые системой. Перед монтированием любого тома дважды
щёлкните по каждой из них и удалите те, которые содержат имя &quot;VeraCrypt&quot; и &quot;TrueCrypt&quot;.
<br>
Кроме того, есть и другие записи, имя которых начинается с &quot;#{&quot; и &quot;\??\Volume{&quot;: дважды щёлкните
по каждой из них и удалите те, значение данных которых содержит имя &quot;VeraCrypt&quot; и &quot;TrueCrypt&quot;.
</li></ul>
</li>
<li>На некоторых компьютерах с Windows программа VeraCrypt может периодически зависать при монтировании и
размонтировании тома. Подобные зависания могут влиять на другие запущенные приложения во время операций монтирования
или демонтирования VeraCrypt.
Эта проблема вызвана конфликтом между диалоговым окном ожидания VeraCrypt, отображаемым во время
монтирования/демонтирования, и другим ПО, установленным в ПК (например, Outpost Firewall Pro).
В таких ситуациях проблему можно решить, отключив окно ожидания VeraCrypt в настройках программы: выберите меню
"Настройки -> Параметры" и включите опцию "Не показывать окно ожидания во время операций".
</li>
</ul>
<h3 id="limitations">Ограничения</h3>
<ul>
<li>[<em>Данное ограничение не относится к пользователям Windows Vista и более новых версий Windows.</em>]
В Windows XP/2003 VeraCrypt не поддерживает шифрование всего системного диска, если тот содержит расширенные
(логические) разделы. Весь системный диск можно зашифровать при условии, что он содержит только первичные разделы.
На любом системном диске, который частично или полностью зашифрован, создавать расширенные (логические) разделы
нельзя (можно только первичные).<br>
<em>Примечание:</em> если требуется зашифровать весь диск, содержащий расширенные разделы, можно зашифровать
системный раздел и в дополнение создать тома VeraCrypt на основе раздела внутри любых несистемных разделов на
этом диске. Либо, как альтернативный вариант, обновить систему до Windows Vista или более новой версии Windows.</li>
<li>В настоящее время VeraCrypt не поддерживает шифрование системного диска, преобразованного в динамический диск.</li>
<li>Чтобы обойти проблему в Windows XP, загрузчик VeraCrypt всегда автоматически настраивается под версию
операционной системы, в которой он установлен. При изменении версии системы (например, загрузчик VeraCrypt
устанавливается во время работы Windows Vista, но позже используется для загрузки Windows XP) вы можете столкнуться
с различными известными и неизвестными проблемами (например, на некоторых ноутбуках с Windows XP может не отображаться
экран входа в систему). Обратите внимание, что это влияет на мультизагрузочные конфигурации, Диски восстановления
VeraCrypt и обманные/скрытые операционные системы (поэтому если, к примеру, скрытая система – Windows XP, то
обманной системой тоже должна быть Windows XP).</li>
<li>Возможность монтировать раздел, находящийся в области действия ключа шифрования системы без предзагрузочной
аутентификации, что делается командой <em>Смонтировать без предзагрузочной аутентификации</em> в меню <em>Система</em>,
(например, раздел, расположенный на зашифрованном системном диске с другой, не работающей в данный момент операционной
системой), ограничена первичными разделами (расширенные/логические разделы таким способом монтировать нельзя).</li>
<li>Из-за проблемы с Windows 2000, Диспетчер монтирования Windows в Windows 2000 не поддерживается VeraCrypt.
Поэтому некоторые встроенные средства Windows 2000, такие как дефрагментация дисков, не работают с томами VeraCrypt.
Кроме того, невозможно использовать службы Диспетчера монтирования в Windows 2000, например, назначить точку
монтирования тому VeraCrypt (то есть прикрепить том VeraCrypt к папке).</li>
<li>VeraCrypt не поддерживает предзагрузочную аутентификацию для операционных систем, установленных в файлах VHD,
за исключением случаев загрузки с использованием соответствующего ПО виртуализации, такого как Microsoft Virtual PC.</li>
<li>Служба теневого копирования томов Windows в настоящее время поддерживается только для разделов в пределах
области действия ключа шифрования системы (например, системный раздел, зашифрованный VeraCrypt, или несистемный
раздел, расположенный на системном диске, зашифрованном VeraCrypt, смонтированный во время работы зашифрованной
операционной системы). Примечание: для других типов томов служба теневого копирования томов не поддерживается,
поскольку отсутствует документация по необходимому API.</li>
<li>Параметры загрузки Windows нельзя изменить из скрытой операционной системы, если система загружается не с
раздела, на котором она установлена. Это связано с тем, что в целях безопасности загрузочный раздел монтируется
как доступный только для чтения при работающей скрытой системе. Чтобы изменить параметры загрузки, запустите
обманную операционную систему.</li>
<li>Размер зашифрованных разделов нельзя изменять, за исключением разделов на полностью зашифрованном системном
диске, размер которых изменяется во время работы зашифрованной ОС.</li>
<li id="SysEncUpgrade">Если системный раздел/диск зашифрован, система не может быть обновлена до более новой
версии ​​(например, с Windows XP до Windows Vista) или восстановлена ​​в предзагрузочной среде (с помощью
установочного CD/DVD Windows или предзагрузочного компонента Windows). В таких случаях сначала необходимо
расшифровать системный раздел/диск. Примечание: работающую в данный момент операционную систему можно
без проблем <em>обновлять</em> (устанавливать патчи безопасности, пакеты обновлений и т. д.), даже если
системный раздел/диск зашифрован.</li>
<li>Шифрование системы поддерживается только на дисках, подключённых локально через интерфейс ATA/SCSI (обратите
внимание, что термин ATA также относится к SATA и eSATA).</li>
<li>При использовании шифрования системы (это относится и к скрытым операционным системам) VeraCrypt не поддерживает
изменение многозагрузочных конфигураций (например, изменение количества операционных систем и их расположения).
В частности, конфигурация должна оставаться такой же, какой она была при запуске мастера создания томов VeraCrypt
для подготовки процесса шифрования системного раздела/диска (или создания скрытой операционной системы).<br>
Примечание. Единственное исключение – многозагрузочная конфигурация, в которой работающая операционная система
с шифрованием VeraCrypt всегда находится на диске №0, и это единственная операционная система на диске
(или на диске есть одна обманная система, зашифрованная VeraCrypt, и одна скрытая ОС, зашифрованная VeraCrypt,
и никакой другой ОС), и диск подключается или отключается до включения компьютера (например, с помощью выключателя
питания на корпусе внешнего диска eSATA). На других дисках, подключённых к компьютеру, могут быть установлены
любые дополнительные операционные системы (зашифрованные или незашифрованные) (когда диск №0 отключён, диск №1
становится диском №0, и т. д.)</li>
<li>Если у ноутбука низкий заряд батареи, Windows может не отправлять соответствующие сообщения запущенным
приложениям, когда компьютер переходит в режим энергосбережения. Поэтому в таких случаях VeraCrypt может не
выполнить автоматическое размонтирование томов.</li>
<li>Сохранение любой временной метки любого файла (например, контейнера или ключевого файла) не гарантируется
надёжно и безопасно (например, из-за журналов файловой системы, временных меток атрибутов файла или того, что
операционная система не может выполнить это по различным документированным и недокументированным причинам).
Примечание. При записи на скрытый том на основе файла, временн<i>а</i>я метка контейнера может измениться.
Это можно правдоподобно объяснить изменением пароля (внешнего) тома. Также обратите внимание, что VeraCrypt
никогда не сохраняет временн<i>ы</i>е метки избранных томов системы (независимо от настроек).</li>
<li>Специальное программное обеспечение (например, низкоуровневый редактор дисков), которое записывает данные
на диск в обход драйверов в стеке драйверов класса <i>DiskDrive</i> (GUID класса – 4D36E967-E325-11CE-BFC1-08002BE10318),
может записывать незашифрованные данные на несистемный диск, на котором размещается смонтированный том VeraCrypt
(<i>Partition0</i>), а также на зашифрованные разделы/диски, которые находятся в пределах области действия
ключа активного шифрования системы (VeraCrypt не шифрует такие данные, записанные этим способом).
Точно так же программное обеспечение, которое записывает данные на диск в обход драйверов в стеке драйверов
класса <i>Storage Volume</i> (GUID класса – 71A27CDD-812A-11D0-BEC7-08002BE2092F), может записывать
незашифрованные данные в тома VeraCrypt на основе раздела (даже если они смонтированы).</li>
<li>Из соображений безопасности, когда работает скрытая операционная система, VeraCrypt обеспечивает, что
все локальные незашифрованные файловые системы и нескрытые тома VeraCrypt доступны только для чтения.
Однако это не относится к файловым системам на CD/DVD-подобных носителях, а также к пользовательским,
нетипичным или нестандартным устройствам/носителям (например, к любым устройствам/носителям, класс которых
отличается от Windows-класса устройств <i>Storage Volume</i> или не отвечающие требованиям этого класса
(GUID класса – 71A27CDD-812A-11D0-BEC7-08002BE2092F)).</li>
<li>Тома VeraCrypt на основе устройств, расположенные на дискетах, не поддерживаются. Но вы по-прежнему
можете создавать на гибких дисках тома VeraCrypt на основе файлов-контейнеров.</li>
<li>Редакции Windows Server не позволяют использовать смонтированные тома VeraCrypt в качестве пути для
резервного копирования сервера. Это можно решить, активировав общий доступ к тому VeraCrypt через интерфейс
Проводника (конечно, вы должны установить правильные права, чтобы избежать несанкционированного доступа),
а затем выбрать опцию <i>Удалённая общая папка</i> (она, разумеется, не удалённая, но Windows нужен сетевой путь).
Там можно указать путь к общему диску (например, \\ServerName\sharename) – и резервное копирование будет
настроено правильно.</li>
<li>Из-за недостатков дизайна Microsoft в обработке разрежённых файлов NTFS вы можете столкнуться с системными
ошибками при записи данных в большие динамические тома (более нескольких сотен гигабайт). Чтобы этого избежать,
рекомендуем, чтобы размер файла-контейнера динамического тома для максимальной совместимости составлял 300 ГБ.
Более подробную информацию об этом ограничении см. здесь: <a href="http://www.flexhex.com/docs/articles/sparse-files.phtml#msdn" target="_blank">
http://www.flexhex.com/docs/articles/sparse-files.phtml#msdn</a> </li>
<li>В Windows 8 и Windows 10 для ускорения загрузки системы появилась функция <i>Гибридная загрузка и завершение
работы</i> и <i>Быстрый запуск</i>. Эта функция включена по умолчанию и имеет побочные эффекты при использовании
томов VeraCrypt. Рекомендуется отключить эту функцию (например, <a href="https://www.maketecheasier.com/disable-hybrid-boot-and-shutdown-in-windows-8/" target="_blank">
здесь</a> объясняется, как отключить её в Windows 8, а <a href="https://www.tenforums.com/tutorials/4189-turn-off-fast-startup-windows-10-a.html" target="_blank">здесь</a>
даны эквивалентные инструкции для Windows 10).
<br>Некоторые примеры проблем:
<ul>
<li>после выключения и перезагрузки смонтированный том продолжит монтироваться без ввода пароля: это связано
с тем, что новое завершение работы Windows 8 – не настоящее, а замаскированный режим гибернации/сна.
</li>
<li>если используется шифрование системы и есть системные избранные тома, настроенные на монтирование во время
загрузки, то после завершения работы и перезапуска эти системные избранные тома не будут смонтированы.
</li>
</ul>
</li>
<li>Диск исправления/восстановления Windows невозможно создать, если том VeraCrypt смонтирован как несъёмный
диск (что происходит по умолчанию). Чтобы решить эту проблему, либо размонтируйте все тома, либо смонтируйте
тома на съёмных носителях.</li>
<li>Дополнительные ограничения перечислены в разделе <a href="Security%20Model.html">
<em>Модель безопасности</em></a>. </li></ul>
</div><div class="ClearBoth"></div></body></html>
