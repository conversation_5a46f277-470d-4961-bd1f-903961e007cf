<!DOCTYPE html>
<html lang="en">
<head>
<meta http-equiv="content-type" content="text/html; charset=utf-8" />
<title>VeraCrypt - Free Open source disk encryption with strong security for the Paranoid</title>
<meta name="description" content="VeraCrypt is free open-source disk encryption software for Windows, Mac OS X and Linux. In case an attacker forces you to reveal the password, VeraCrypt provides plausible deniability. In contrast to file encryption, data encryption performed by VeraCrypt is real-time (on-the-fly), automatic, transparent, needs very little memory, and does not involve temporary unencrypted files."/>
<meta name="keywords" content="encryption, security"/>
<link href="styles.css" rel="stylesheet" type="text/css" />
</head>
<body>

<div>
<a href="Documentation.html"><img src="VeraCrypt128x128.png" alt="VeraCrypt"/></a>
</div>

<div id="menu">
	<ul>
	  <li><a href="Home.html">Home</a></li>
	  <li><a href="Code.html">Source Code</a></li>
	  <li><a href="Downloads.html">Downloads</a></li>
	  <li><a class="active" href="Documentation.html">Documentation</a></li>
	  <li><a href="Donation.html">Donate</a></li>
	  <li><a href="https://sourceforge.net/p/veracrypt/discussion/" target="_blank">Forums</a></li>
	</ul>
</div>

<div>
<p>
<a href="Documentation.html">Documentation</a>
<img src="arrow_right.gif" alt=">>" style="margin-top: 5px">
<a href="Security%20Requirements%20and%20Precautions.html">Security Requirements and Precautions</a>
<img src="arrow_right.gif" alt=">>" style="margin-top: 5px">
<a href="Choosing%20Passwords%20and%20Keyfiles.html">Choosing Passwords and Keyfiles</a>
</p></div>

<div class="wikidoc">
<div>
<h1>Choosing Passwords and Keyfiles</h1>
<p>It is very important that you choose a good password. You must avoid choosing one that contains only a single word that can be found in a dictionary (or a combination of such words). It must not contain any names, dates of birth, account numbers, or any
 other items that could be easy to guess. A good password is a random combination of upper and lower case letters, numbers, and special characters, such as @ ^ = $ * &#43; etc. We strongly recommend choosing a password consisting of more than 20 characters (the
 longer, the better). Short passwords are easy to crack using brute-force techniques.<br>
<br>
To make brute-force attacks on a keyfile infeasible, the size of the keyfile must be at least 30 bytes. If a volume uses multiple keyfiles, then at least one of the keyfiles must be 30 bytes in size or larger. Note that the 30-byte limit assumes a large amount
 of entropy in the keyfile. If the first 1024 kilobytes of a file contain only a small amount of entropy, it must not be used as a keyfile (regardless of the file size). If you are not sure what entropy means, we recommend that you let VeraCrypt generate a
 file with random content and that you use it as a keyfile (select <em>Tools -&gt; Keyfile Generator</em>).</p>
<p>When creating a volume, encrypting a system partition/drive, or changing passwords/keyfiles, you must not allow any third party to choose or modify the password/keyfile(s) before/while the volume is created or the password/keyfiles(s) changed. For example,
 you must not use any password generators (whether website applications or locally run programs) where you are not sure that they are high-quality and uncontrolled by an attacker, and keyfiles must not be files that you download from the internet or that are
 accessible to other users of the computer (whether they are administrators or not).</p>
</div>
</div><div class="ClearBoth"></div></body></html>
