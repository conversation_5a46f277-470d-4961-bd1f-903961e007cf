<!DOCTYPE html>
<html lang="en">
<head>
<meta http-equiv="content-type" content="text/html; charset=utf-8" />
<title>VeraCrypt - Free Open source disk encryption with strong security for the Paranoid</title>
<meta name="description" content="VeraCrypt is free open-source disk encryption software for Windows, Mac OS X and Linux. In case an attacker forces you to reveal the password, VeraCrypt provides plausible deniability. In contrast to file encryption, data encryption performed by VeraCrypt is real-time (on-the-fly), automatic, transparent, needs very little memory, and does not involve temporary unencrypted files."/>
<meta name="keywords" content="encryption, security"/>
<link href="styles.css" rel="stylesheet" type="text/css" />
</head>
<body>

<div>
<a href="Documentation.html"><img src="VeraCrypt128x128.png" alt="VeraCrypt"/></a>
</div>

<div id="menu">
	<ul>
	  <li><a href="Home.html">Home</a></li>
	  <li><a href="Code.html">Source Code</a></li>
	  <li><a href="Downloads.html">Downloads</a></li>
	  <li><a class="active" href="Documentation.html">Documentation</a></li>
	  <li><a href="Donation.html">Donate</a></li>
	  <li><a href="https://sourceforge.net/p/veracrypt/discussion/" target="_blank">Forums</a></li>
	</ul>
</div>

<div>
<p>
<a href="Documentation.html">Documentation</a>
<img src="arrow_right.gif" alt=">>" style="margin-top: 5px">
<a href="TrueCrypt%20Support.html">TrueCrypt Support</a>
</p></div>

<div class="wikidoc">
<h1>TrueCrypt Support</h1>
<p>
<strong>Note: <span style="color:#ff0000;">TrueCrypt support was dropped starting from version 1.26</span></strong>
</p>
<p>Starting from version 1.0f, VeraCrypt supports loading TrueCrypt volumes and partitions, both normal and hidden. In order to activate this, you have to check &ldquo;TrueCrypt Mode&rdquo; in the password prompt dialog as shown below.</p>
<p><img src="TrueCrypt Support_truecrypt_mode_gui.jpg" alt="TrueCrypt mode"></p>
<p><strong>Note:</strong> Only volumes and partitions created using TrueCrypt versions
<strong>6.x</strong> and <strong>7.x</strong> are supported.</p>
</div><div class="ClearBoth"></div></body></html>
