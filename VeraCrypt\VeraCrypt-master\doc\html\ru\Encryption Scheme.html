﻿<!DOCTYPE html>
<html lang="ru">
<head>
<meta http-equiv="content-type" content="text/html; charset=utf-8" />
<title>VeraCrypt - Бесплатное надёжное шифрование дисков с открытым исходным кодом</title>
<meta name="description" content="VeraCrypt это бесплатное программное обеспечение для шифрования дисков с открытым исходным кодом для Windows, Mac OS X (macOS) и Linux. В случае, если злоумышленник вынуждает вас раскрыть пароль, VeraCrypt обеспечивает правдоподобное отрицание наличия шифрования. В отличие от пофайлового шифрования, VeraCrypt шифрует данные в реальном времени (на лету), автоматически, прозрачно, требует очень мало памяти и не использует временные незашифрованные файлы."/>
<meta name="keywords" content="encryption, security, шифрование, безопасность"/>
<link href="styles.css" rel="stylesheet" type="text/css" />
</head>
<body>

<div>
<a href="Documentation.html"><img src="VeraCrypt128x128.png" alt="VeraCrypt"/></a>
</div>

<div id="menu">
	<ul>
	  <li><a href="Home.html">Начало</a></li>
	  <li><a href="Code.html">Исходный код</a></li>
	  <li><a href="Downloads.html">Загрузить</a></li>
	  <li><a class="active" href="Documentation.html">Документация</a></li>
	  <li><a href="Donation.html">Поддержать разработку</a></li>
	  <li><a href="https://sourceforge.net/p/veracrypt/discussion/" target="_blank">Форум</a></li>
	</ul>
</div>

<div>
<p>
<a href="Documentation.html">Документация</a>
<img src="arrow_right.gif" alt=">>" style="margin-top: 5px">
<a href="Technical%20Details.html">Технические подробности</a>
<img src="arrow_right.gif" alt=">>" style="margin-top: 5px">
<a href="Encryption%20Scheme.html">Схема шифрования</a>
</p></div>

<div class="wikidoc">
<h1>Схема шифрования</h1>
<p>При монтировании тома VeraCrypt (предполагаем, что нет кэшированных паролей/ключевых файлов) или при предзагрузочной аутентификации выполняются следующие операции:</p>
<ol>
<li>Считываются (помещаются) в ОЗУ первые 512 байт тома (то есть заголовок обычного тома), из которых первые 64 байта это соль (см.
<a href="VeraCrypt%20Volume%20Format%20Specification.html">
<em>Спецификация формата томов VeraCrypt</em></a>). Для шифрования системы (см. <a href="System%20Encryption.html"><em>Шифрование системы</em></a>)
в ОЗУ считываются последние 512 байт первой дорожки логического диска (загрузчик VeraCrypt располагается в первой дорожке
системного диска и/или Диска восстановления VeraCrypt). </li><li>Считываются (помещаются) в ОЗУ байты 65 536&ndash;66 047 тома (см.
<a href="VeraCrypt%20Volume%20Format%20Specification.html">
<em>Спецификация формата томов VeraCrypt</em></a>). Для шифрования системы считываются байты 65 536&ndash;66 047 раздела, расположенного сразу за активным разделом* (см.
<a href="Hidden%20Operating%20System.html">
Скрытая операционная система</a>). Если внутри этого тома имеется скрытый том (или внутри раздела, следующего за загрузочным разделом),
то в этой точке мы прочитали его заголовок; в противном случае мы просто прочитали случайные данные (есть скрытый том внутри или его нет,
определяется только попыткой расшифровать эти данные; подробности см. в разделе
<a href="Hidden%20Volume.html"><em>Скрытый том</em></a>).
</li><li>Сейчас VeraCrypt пытается расшифровать заголовок обычного тома, считанный на этапе 1. Все данные, использованные
и сгенерированные в ходе дешифрования, хранятся в ОЗУ (VeraCrypt никогда не сохраняет их на диске). Указанные ниже параметры
неизвестны и определяются методом проб и ошибок (то есть проверкой всех возможных комбинаций следующего):
<ol type="a">
<li>PRF (псевдослучайная функция), применяемая при формировании (деривации) ключа заголовка (как определено в PKCS #5 v2.0; см.
<a href="Header%20Key%20Derivation.html">
<em>Формирование ключа заголовка, соль и количество итераций</em></a>), которая может быть одной из следующих:
<p>HMAC-SHA-512, HMAC-SHA-256, HMAC-BLAKE2S-256, HMAC-Whirlpool.</p>
<p>Если PRF указана пользователем явно, используется непосредственно она, без опробования других функций.</p>
<p>Введённый пользователем пароль (который может сопровождаться одним или несколькими ключевыми файлами – см. раздел
<a href="Keyfiles%20in%20VeraCrypt.html">
<em>Ключевые файлы</em></a>), значение PIM (если указано) и соль, считанные на этапе 1, передаются в функцию формирования
ключа заголовка, которая производит последовательность значений (см. <a href="Header%20Key%20Derivation.html">
<em>Формирование ключа заголовка, соль и количество итераций</em></a>), из которых формируются ключ шифрования
заголовка и вторичный ключ заголовка (режим XTS). (Эти ключи используются для дешифрования заголовка тома.)</p>
</li><li>Алгоритм шифрования: AES-256, Serpent, Twofish, AES-Serpent, AES-Twofish-Serpent и т. д.
</li><li>Режим работы: поддерживается только XTS</li><li>Размеры ключей</li></ol>
</li><li>Дешифрование считается успешным, если первые четыре байта расшифрованных данных содержат ASCII-строку &ldquo;VERA&rdquo;
и если контрольная сумма CRC-32 последних 256 байт расшифрованных данных (заголовок тома) совпадает со значением, находящимся в байте №8
расшифрованных данных (неприятелю это значение неизвестно, поскольку оно зашифровано – см. раздел
<a href="VeraCrypt%20Volume%20Format%20Specification.html">
<em>Спецификация формата томов VeraCrypt</em></a>). Если эти условия не выполнены, процесс продолжается с этапа 3 снова,
но на этот раз вместо данных, считанных на этапе 1, используются данные, считанные на этапе 2 (то есть возможный заголовок
скрытого тома). Если условия снова не выполнены, монтирование прекращается (неверный пароль, повреждённый том, не том
VeraCrypt).
</li><li>Теперь мы знаем (или предполагаем с очень высокой вероятностью), что у нас правильный пароль, правильный алгоритм
шифрования, режим, размер ключа и правильный алгоритм формирования ключа заголовка. Если мы успешно расшифровали данные,
считанные на этапе 2, мы также знаем, что монтируется скрытый том, и знаем его размер, полученный из данных, считанных
на этапе 2 и расшифрованных на этапе 3.
</li><li>Подпрограмма шифрования переинициализируется с первичным мастер-ключом** и вторичным мастер-ключом (режим
XTS &ndash; см. раздел <a href="Modes%20of%20Operation.html"><em>Режимы работы</em></a>), которые получены из расшифрованного заголовка
тома (см. раздел <a href="VeraCrypt%20Volume%20Format%20Specification.html">
<em>Спецификация формата томов VeraCrypt</em></a>). Эти ключи могут быть использованы для дешифрования любого сектора тома,
за исключением области заголовка тома (или, в случае шифрования системы, области ключевых данных), зашифрованного с помощью
ключей заголовка. Том смонтирован.
</li></ol>
<p>См. также разделы <a href="Modes%20of%20Operation.html">
<em>Режимы работы</em></a> и <a href="Header%20Key%20Derivation.html">
<em>Формирование ключа заголовка, соль и количество итераций</em></a>, а также главу
<a href="Security%20Model.html"><em>Модель безопасности</em></a>.</p>
<p>* Если размер активного раздела меньше 256 МБ, то данные считываются из <em>второго</em> раздела, идущего следом за активным
(Windows 7 и более поздние версии по умолчанию не загружаются с раздела, на котором они установлены).</p>
<p>&dagger; Эти параметры держатся в секрете <em>не</em> для того, чтобы усложнить атаку, а в первую очередь для того, чтобы
сделать тома VeraCrypt не идентифицируемыми (неотличимыми от случайных данных), чего было бы трудно добиться, если бы эти
параметры хранились в незашифрованном виде в заголовке тома. Также обратите внимание, что в случае устаревшего режима
загрузки MBR, если для шифрования системы используется некаскадный алгоритм шифрования, алгоритм <em>известен</em>
(его можно определить, проанализировав содержимое незашифрованного загрузчика VeraCrypt, хранящегося на первой дорожке
логического диска или на Диске восстановления VeraCrypt).</p>
<p>** Мастер-ключи генерируются во время создания тома и не могут быть изменены позже. Изменение пароля тома выполняется
путём повторного шифрования заголовка тома с использованием нового ключа заголовка (сформированным из нового пароля).</p>
<p>&nbsp;</p>
<p><a href="Modes%20of%20Operation.html" style="text-align:left; color:#0080c0; text-decoration:none; font-weight:bold">Следующий раздел &gt;&gt;</a></p>
</div><div class="ClearBoth"></div></body></html>
