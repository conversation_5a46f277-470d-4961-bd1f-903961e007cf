﻿<!DOCTYPE html>
<html lang="ru">
<head>
<meta http-equiv="content-type" content="text/html; charset=utf-8" />
<title>VeraCrypt - Бесплатное надёжное шифрование дисков с открытым исходным кодом</title>
<meta name="description" content="VeraCrypt это бесплатное программное обеспечение для шифрования дисков с открытым исходным кодом для Windows, Mac OS X (macOS) и Linux. В случае, если злоумышленник вынуждает вас раскрыть пароль, VeraCrypt обеспечивает правдоподобное отрицание наличия шифрования. В отличие от пофайлового шифрования, VeraCrypt шифрует данные в реальном времени (на лету), автоматически, прозрачно, требует очень мало памяти и не использует временные незашифрованные файлы."/>
<meta name="keywords" content="encryption, security, шифрование, безопасность"/>
<link href="styles.css" rel="stylesheet" type="text/css" />
</head>
<body>

<div>
<a href="Documentation.html"><img src="VeraCrypt128x128.png" alt="VeraCrypt"/></a>
</div>

<div id="menu">
	<ul>
	  <li><a href="Home.html">Начало</a></li>
	  <li><a href="Code.html">Исходный код</a></li>
	  <li><a href="Downloads.html">Загрузить</a></li>
	  <li><a class="active" href="Documentation.html">Документация</a></li>
	  <li><a href="Donation.html">Поддержать разработку</a></li>
	  <li><a href="https://sourceforge.net/p/veracrypt/discussion/" target="_blank">Форум</a></li>
	</ul>
</div>

<div>
<p>
<a href="Documentation.html">Документация</a>
<img src="arrow_right.gif" alt=">>" style="margin-top: 5px">
<a href="Security%20Requirements%20and%20Precautions.html">Требования безопасности и меры предосторожности</a>
<img src="arrow_right.gif" alt=">>" style="margin-top: 5px">
<a href="Authenticity%20and%20Integrity.html">Подлинность и целостность данных</a>
</p></div>

<div class="wikidoc">
<div>
<h1>Подлинность и целостность данных</h1>
<p>VeraCrypt применяет шифрование для сохранения <em>конфиденциальности</em> шифруемых данных. VeraCrypt
не сохраняет и не проверяет целостность или подлинность данных, подвергающихся шифрованию и дешифрованию.
Следовательно, если вы позволите неприятелю изменить зашифрованные с помощью VeraCrypt данные, он сможет
установить у любого 16-байтового блока данных случайное или предыдущее значение, которое ему удалось
получить в прошлом. Обратите внимание, что неприятель не может выбрать значение, которое вы получите,
когда VeraCrypt расшифровывает изменённый блок – значение будет случайным, если только противник не восстановит
старую версию зашифрованного блока, которую ему удалось получить в прошлом. Ответственность за проверку
целостности и подлинности данных, зашифрованных или расшифрованных VeraCrypt, лежит только на вас (например,
это можно сделать с помощью соответствующего стороннего ПО).<br>
<br>
См. также: <a href="Physical%20Security.html">
<em>Физическая безопасность</em></a>, <a href="Security%20Model.html">
<em>Модель безопасности</em></a></p>
</div>
</div><div class="ClearBoth"></div></body></html>
