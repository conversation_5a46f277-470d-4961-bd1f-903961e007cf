<!DOCTYPE html>
<html lang="en">
<head>
<meta http-equiv="content-type" content="text/html; charset=utf-8" />
<title>VeraCrypt - Free Open source disk encryption with strong security for the Paranoid</title>
<meta name="description" content="VeraCrypt is free open-source disk encryption software for Windows, Mac OS X and Linux. In case an attacker forces you to reveal the password, VeraCrypt provides plausible deniability. In contrast to file encryption, data encryption performed by VeraCrypt is real-time (on-the-fly), automatic, transparent, needs very little memory, and does not involve temporary unencrypted files."/>
<meta name="keywords" content="encryption, security"/>
<link href="styles.css" rel="stylesheet" type="text/css" />
</head>
<body>

<div>
<a href="Documentation.html"><img src="VeraCrypt128x128.png" alt="VeraCrypt"/></a>
</div>

<div id="menu">
	<ul>
	  <li><a href="Home.html">Home</a></li>
	  <li><a href="Code.html">Source Code</a></li>
	  <li><a href="Downloads.html">Downloads</a></li>
	  <li><a class="active" href="Documentation.html">Documentation</a></li>
	  <li><a href="Donation.html">Donate</a></li>
	  <li><a href="https://sourceforge.net/p/veracrypt/discussion/" target="_blank">Forums</a></li>
	</ul>
</div>

<div>
<p>
<a href="Documentation.html">Documentation</a>
<img src="arrow_right.gif" alt=">>" style="margin-top: 5px">
<a href="Security%20Requirements%20and%20Precautions.html">Security Requirements and Precautions</a>
<img src="arrow_right.gif" alt=">>" style="margin-top: 5px">
<a href="Data%20Leaks.html">Data Leaks</a>
<img src="arrow_right.gif" alt=">>" style="margin-top: 5px">
<a href="Memory%20Dump%20Files.html">Memory Dump Files</a>
</p></div>

<div class="wikidoc">
<h1>Memory Dump Files</h1>
<div style="text-align:left; margin-top:19px; margin-bottom:19px; padding-top:0px; padding-bottom:0px">
<em style="text-align:left">Note: The issue described below does <strong style="text-align:left">
not</strong> affect you if the system partition or system drive is encrypted (for more information, see the chapter
<a href="System%20Encryption.html" style="text-align:left; color:#0080c0; text-decoration:none">
System Encryption</a>) and if the system is configured to write memory dump files to the system drive (which it typically is, by default).</em></div>
<div style="text-align:left; margin-top:19px; margin-bottom:19px; padding-top:0px; padding-bottom:0px">
Most operating systems, including Windows, can be configured to write debugging information and contents of the system memory to so-called memory dump files (also called crash dump files) when an error occurs (system crash, &quot;blue screen,&quot; bug check). Therefore,
 memory dump files may contain sensitive data. VeraCrypt <em style="text-align:left">
cannot</em> prevent cached passwords, encryption keys, and the contents of sensitive files opened in RAM from being saved
<em style="text-align:left">unencrypted</em> to memory dump files. Note that when you open a file stored on a VeraCrypt volume, for example, in a text editor, then the content of the file is stored
<em style="text-align:left">unencrypted</em> in RAM (and it may remain <em style="text-align:left">
unencrypted </em>in RAM until the computer is turned off). Also note that when a VeraCrypt volume is mounted, its master key is stored
<em style="text-align:left">unencrypted</em> in RAM. Therefore, you must disable memory dump file generation on your computer at least for each session during which you work with any sensitive data and during which you mount a VeraCrypt volume. To do so in
 Windows XP or later, right-click the '<em style="text-align:left">Computer</em>' (or '<em style="text-align:left">My Computer</em>') icon on the desktop or in the
<em style="text-align:left">Start Menu</em>, and then select <em style="text-align:left">
Properties</em> &gt; (on Windows Vista or later: &gt; <em style="text-align:left">
Advanced System Settings</em> &gt;) <em style="text-align:left">Advanced </em>tab &gt; section
<em style="text-align:left">Startup and Recovery </em>&gt; <em style="text-align:left">
Settings &gt; </em>section <em style="text-align:left">Write debugging information
</em>&gt; select <em style="text-align:left">(none)</em> &gt; <em style="text-align:left">
OK</em>.</div>
<div style="text-align:left; margin-top:19px; margin-bottom:19px; padding-top:0px; padding-bottom:0px">
<em style="text-align:left">Note for users of Windows XP/2003</em>: As Windows XP and Windows 2003 do not provide any API for encryption of memory dump files, if the system partition/drive is encrypted by VeraCrypt and your Windows XP system is configured to
 write memory dump files to the system drive, the VeraCrypt driver automatically prevents Windows from writing any data to memory dump files<em style="text-align:left">.</em></div>
</div><div class="ClearBoth"></div></body></html>
