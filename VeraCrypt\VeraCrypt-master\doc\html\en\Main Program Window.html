<!DOCTYPE html>
<html lang="en">
<head>
<meta http-equiv="content-type" content="text/html; charset=utf-8" />
<title>VeraCrypt - Free Open source disk encryption with strong security for the Paranoid</title>
<meta name="description" content="VeraCrypt is free open-source disk encryption software for Windows, Mac OS X and Linux. In case an attacker forces you to reveal the password, VeraCrypt provides plausible deniability. In contrast to file encryption, data encryption performed by VeraCrypt is real-time (on-the-fly), automatic, transparent, needs very little memory, and does not involve temporary unencrypted files."/>
<meta name="keywords" content="encryption, security"/>
<link href="styles.css" rel="stylesheet" type="text/css" />
</head>
<body>

<div>
<a href="Documentation.html"><img src="VeraCrypt128x128.png" alt="VeraCrypt"/></a>
</div>

<div id="menu">
	<ul>
	  <li><a href="Home.html">Home</a></li>
	  <li><a href="Code.html">Source Code</a></li>
	  <li><a href="Downloads.html">Downloads</a></li>
	  <li><a class="active" href="Documentation.html">Documentation</a></li>
	  <li><a href="Donation.html">Donate</a></li>
	  <li><a href="https://sourceforge.net/p/veracrypt/discussion/" target="_blank">Forums</a></li>
	</ul>
</div>

<div>
<p>
<a href="Documentation.html">Documentation</a>
<img src="arrow_right.gif" alt=">>" style="margin-top: 5px">
<a href="Main%20Program%20Window.html">Main Program Window</a>
</p></div>

<div class="wikidoc">
<h1>Main Program Window</h1>
<h3>Select File</h3>
<p>Allows you to select a file-hosted VeraCrypt volume. After you select it, you can perform various operations on it (e.g., mount it by clicking &lsquo;Mount&rsquo;). It is also possible to select a volume by dragging its icon to the &lsquo;VeraCrypt.exe&rsquo;
 icon (VeraCrypt will be automatically launched then) or to the main program window.</p>
<h3>Select Device</h3>
<p>Allows you to select a VeraCrypt partition or a storage device (such as a USB memory stick). After it is selected, you can perform various operations with it (e.g., mount it by clicking &lsquo;Mount&rsquo;).<br>
<br>
Note: There is a more comfortable way of mounting VeraCrypt partitions/devices &ndash; see the section
<em>Auto-Mount Devices</em> for more information.</p>
<h3>Mount</h3>
<p>After you click &lsquo;Mount&rsquo;, VeraCrypt will try to mount the selected volume using cached passwords (if there are any) and if none of them works, it prompts you for a password. If you enter the correct password (and/or provide correct keyfiles),
 the volume will be mounted.</p>
<p>Important: Note that when you exit the VeraCrypt application, the VeraCrypt driver continues working and no VeraCrypt volume is unmounted.</p>
<h3 id="AutoMountDevices">Auto-Mount Devices</h3>
<p>This function allows you to mount VeraCrypt partitions/devices without having to select them manually (by clicking &lsquo;Select Device&rsquo;). VeraCrypt scans headers of all available partitions/devices on your system (except DVD drives and similar devices)
 one by one and tries to mount each of them as a VeraCrypt volume. Note that a VeraCrypt partition/device cannot be identified, nor the cipher it has been encrypted with. Therefore, the program cannot directly &ldquo;find&rdquo; VeraCrypt partitions. Instead,
 it has to try mounting each (even unencrypted) partition/device using all encryption algorithms and all cached passwords (if there are any). Therefore, be prepared that this process may take a long time on slow computers.<br>
<br>
If the password you enter is wrong, mounting is attempted using cached passwords (if there are any). If you enter an empty password and if
<em>Use keyfiles</em> is unchecked, only the cached passwords will be used when attempting to auto-mount partitions/devices. If you do not need to set mount options, you can bypass the password prompt by holding down the
<em>Shift</em> key when clicking <em>Auto- Mount Devices</em> (only cached passwords will be used, if there are any).<br>
<br>
Drive letters will be assigned starting from the one that is selected in the drive list in the main window.</p>
<h3>Unmount</h3>
<p>This function allows you to unmount the VeraCrypt volume selected in the drive list in the main window. To unmount a VeraCrypt volume means to close it and make it impossible to read/write from/to the volume.</p>
<h3>Unmount All</h3>
<p>Note: The information in this section applies to all menu items and buttons with the same or similar caption (for example, it also applies to the system tray menu item
<em>Unmount All</em>).<br>
<br>
This function allows you to unmount multiple VeraCrypt volumes. To unmount a VeraCrypt volume means to close it and make it impossible to read/write from/to the volume. This function unmounts all mounted VeraCrypt volumes except the following:</p>
<ul>
<li>Partitions/drives within the key scope of active system encryption (e.g., a system partition encrypted by VeraCrypt, or a non-system partition located on a system drive encrypted by VeraCrypt, mounted when the encrypted operating system is running).
</li><li>VeraCrypt volumes that are not fully accessible to the user account (e.g. a volume mounted from within another user account).
</li><li>VeraCrypt volumes that are not displayed in the VeraCrypt application window. For example, system favorite volumes attempted to be unmounted by an instance of VeraCrypt without administrator privileges when the option '<em>Allow only administrators to
 view and unmount system favorite volumes in VeraCrypt</em>' is enabled. </li></ul>
<h3>Wipe Cache</h3>
<p>Clears all passwords (which may also contain processed keyfile contents) cached in driver memory. When there are no passwords in the cache, this button is disabled. For information on password cache, see the section
<a href="Mounting%20VeraCrypt%20Volumes.html">
<em>Cache Password in Driver Memory</em></a>.</p>
<h3>Never Save History</h3>
<p>If this option disabled, the file names and/or paths of the last twenty files/devices that were attempted to be mounted as VeraCrypt volumes will be saved in the History file (whose content can be displayed by clicking on the Volume combo-box in the main
 window).<br>
<br>
When this option is enabled, VeraCrypt clears the registry entries created by the Windows file selector for VeraCrypt, and sets the &ldquo;current directory&rdquo; to the user&rsquo;s home directory (in portable mode, to the directory from which VeraCrypt was
 launched) whenever a container or keyfile is selected via the Windows file selector. Therefore, the Windows file selector will not remember the path of the last mounted container (or the last selected keyfile). However, note that the operations described in
 this paragraph are <em>not</em> guaranteed to be performed reliably and securely (see e.g.
<a href="Security%20Requirements%20and%20Precautions.html">
<em>Security Requirements and Precautions</em></a>) so we strongly recommend that you encrypt the system partition/drive instead of relying on them (see
<a href="System%20Encryption.html"><em>System Encryption</em></a>).<br>
<br>
Furthermore, if this option is enabled, the volume path input field in the main VeraCrypt window is cleared whenever you hide VeraCrypt.<br>
<br>
Note: You can clear the volume history by selecting <em>Tools</em> -&gt; <em>Clear Volume History</em>.</p>
<h3>Exit</h3>
<p>Terminates the VeraCrypt application. The driver continues working and no VeraCrypt volumes are unmounted. When running in &lsquo;portable&rsquo; mode, the VeraCrypt driver is unloaded when it is no longer needed (e.g., when all instances of the main application
 and/or of the Volume Creation Wizard are closed and no VeraCrypt volumes are mounted). However, if you force unmount on a</p>
<p>VeraCrypt volume when VeraCrypt runs in portable mode, or mount a writable NTFS-formatted volume on Windows Vista or later, the VeraCrypt driver may
<em>not</em> be unloaded when you exit VeraCrypt (it will be unloaded only when you shut down or restart the system). This prevents various problems caused by a bug in Windows (for instance, it would be impossible to start VeraCrypt again as long as there are
 applications using the unmounted volume).</p>
<h3>Volume Tools</h3>
<h4>Change Volume Password</h4>
<p>See the section <a href="Program%20Menu.html">
<em>Volumes -&gt; Change Volume Password</em></a>.</p>
<h4>Set Header Key Derivation Algorithm</h4>
<p>See the section <a href="Program%20Menu.html">
<em>Volumes -&gt; Set Header Key Derivation Algorithm</em></a>.</p>
<h4>Backup Volume Header</h4>
<p>See the section <a href="Program%20Menu.html#tools-backup-volume-header">
<em>Tools -&gt; Backup Volume Header</em></a>.</p>
<h4>Restore Volume Header</h4>
<p>See the section <a href="Program%20Menu.html#tools-restore-volume-header">
<em>Tools -&gt; Restore Volume Header</em></a>.</p>
<p>&nbsp;</p>
<p><a href="Program%20Menu.html" style="text-align:left; color:#0080c0; text-decoration:none; font-weight:bold">Next Section &gt;&gt;</a></p>
</div><div class="ClearBoth"></div></body></html>
