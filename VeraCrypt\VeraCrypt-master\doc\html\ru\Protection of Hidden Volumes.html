﻿<!DOCTYPE html>
<html lang="ru">
<head>
<meta http-equiv="content-type" content="text/html; charset=utf-8" />
<title>VeraCrypt - Бесплатное надёжное шифрование дисков с открытым исходным кодом</title>
<meta name="description" content="VeraCrypt это бесплатное программное обеспечение для шифрования дисков с открытым исходным кодом для Windows, Mac OS X (macOS) и Linux. В случае, если злоумышленник вынуждает вас раскрыть пароль, VeraCrypt обеспечивает правдоподобное отрицание наличия шифрования. В отличие от пофайлового шифрования, VeraCrypt шифрует данные в реальном времени (на лету), автоматически, прозрачно, требует очень мало памяти и не использует временные незашифрованные файлы."/>
<meta name="keywords" content="encryption, security, шифрование, безопасность"/>
<link href="styles.css" rel="stylesheet" type="text/css" />
</head>
<body>

<div>
<a href="Documentation.html"><img src="VeraCrypt128x128.png" alt="VeraCrypt"/></a>
</div>

<div id="menu">
	<ul>
	  <li><a href="Home.html">Начало</a></li>
	  <li><a href="Code.html">Исходный код</a></li>
	  <li><a href="Downloads.html">Загрузить</a></li>
	  <li><a class="active" href="Documentation.html">Документация</a></li>
	  <li><a href="Donation.html">Поддержать разработку</a></li>
	  <li><a href="https://sourceforge.net/p/veracrypt/discussion/" target="_blank">Форум</a></li>
	</ul>
</div>

<div>
<p>
<a href="Documentation.html">Документация</a>
<img src="arrow_right.gif" alt=">>" style="margin-top: 5px">
<a href="Plausible%20Deniability.html">Правдоподобное отрицание наличия шифрования</a>
<img src="arrow_right.gif" alt=">>" style="margin-top: 5px">
<a href="Hidden%20Volume.html">Скрытый том</a>
<img src="arrow_right.gif" alt=">>" style="margin-top: 5px">
<a href="Protection%20of%20Hidden%20Volumes.html">Защита скрытых томов</a>
</p></div>

<div class="wikidoc">
<h1>Защита скрытых томов от повреждений</h1>
<div style="text-align:left; margin-top:19px; margin-bottom:19px; padding-top:0px; padding-bottom:0px">
Если вы монтируете том VeraCrypt, внутри которого находится
<a href="Hidden%20Volume.html" style="text-align:left; color:#0080c0; text-decoration:none">
скрытый том</a>, то можете <i>считывать</i> данные из (внешнего) тома без всякого риска. Однако если вам
(или операционной системе) потребуется <i>записать</i> данные во внешний том, есть риск повредить
(перезаписать) скрытый том. Чтобы избежать этого, скрытый том следует защитить, о чём и пойдёт здесь речь.</div>
<div style="text-align:left; margin-top:19px; margin-bottom:19px; padding-top:0px; padding-bottom:0px">
При монтировании внешнего тома введите его пароль, но прежде чем нажать <em style="text-align:left">
OK,</em> нажмите кнопку <em style="text-align:left">Параметры</em>:</div>
<div style="text-align:left; margin-top:19px; margin-bottom:19px; padding-top:0px; padding-bottom:0px">
<img src="Protection of Hidden Volumes_Image_027.png" alt="Графический интерфейс VeraCrypt"></div>
<div style="text-align:left; margin-top:19px; margin-bottom:19px; padding-top:0px; padding-bottom:0px">
&nbsp;</div>
<div style="text-align:left; margin-top:19px; margin-bottom:19px; padding-top:0px; padding-bottom:0px">
В появившемся окне <em style="text-align:left">Параметры монтирования</em> включите опцию
<em style="text-align:left">Защитить скрытый том от повреждения при записи во внешний том</em>. В поле
<em style="text-align:left">Пароль скрытого тома</em> введите пароль для скрытого тома. Нажмите
<em style="text-align:left">OK</em>, а затем нажмите <em style="text-align:left">
OK</em> в окне ввода основного пароля.</div>
<div style="text-align:left; margin-top:19px; margin-bottom:19px; padding-top:0px; padding-bottom:0px">
<img src="Protection of Hidden Volumes_Image_028.png" alt="Монтирование с защитой скрытого тома"></div>
<div style="text-align:left; margin-top:19px; margin-bottom:19px; padding-top:0px; padding-bottom:0px">
<br style="text-align:left">
<br style="text-align:left">
Оба пароля должны быть правильными; в противном случае внешний том не будет смонтирован. Когда включена
защита скрытого тома, VeraCrypt на самом деле <em style="text-align:left">не</em> монтирует скрытый том.
Он только расшифровывает его заголовок (в ОЗУ) и получает информацию о размере скрытого тома (из
расшифрованного заголовка). Затем монтируется внешний том, а любые попытки записи данных в область скрытого
тома отклоняются (пока внешний том не будет размонтирован).
<strong style="text-align:left">Обратите внимание, что VeraCrypt <i>никогда</i> и <i>никак</i> не модифицирует
файловую систему (например, сведения о распределённых кластерах, объём свободного пространства и т. д.) внутри
внешнего тома. Как только том будет размонтирован, защита отключается. При повторном монтировании тома определить,
что применялась защита скрытого тома, невозможно. Защиту скрытого тома может включать только пользователь,
который укажет правильный пароль (и/или ключевые файлы) для скрытого тома (при каждом монтировании внешнего тома).
<br style="text-align:left"></strong><br style="text-align:left">
Как только операция записи в область скрытого тома отклонена/предотвращена (для защиты скрытого тома), весь
хост-том (как внешний, так и скрытый том) становится защищённым от записи до тех пор, пока не будет размонтирован
(при каждой попытке записи в этот том драйвер VeraCrypt передаёт системе ошибку "неверный параметр").
Таким образом, сохраняется возможность правдоподобного отрицания наличия шифрования (иначе некоторые
несоответствия внутри файловой системы могли бы свидетельствовать, что для этого тома применялась защита
скрытого тома). Когда предотвращается повреждение скрытого тома, об этом выдаётся предупреждающее сообщение
(при условии, что включена работа VeraCrypt в фоновом режиме, см. главу
<a href="VeraCrypt%20Background%20Task.html" style="text-align:left; color:#0080c0; text-decoration:none">
Работа VeraCrypt в фоновом режиме</a>). Кроме того, отображаемый в главном окне тип смонтированного внешнего
тома изменяется на "<em style="text-align:left">Внешний(!)</em>":</div>
<div style="text-align:left; margin-top:19px; margin-bottom:19px; padding-top:0px; padding-bottom:0px">
<img src="Protection of Hidden Volumes_Image_029.png" alt="Графический интерфейс VeraCrypt"></div>
<div style="text-align:left; margin-top:19px; margin-bottom:19px; padding-top:0px; padding-bottom:0px">
<br style="text-align:left">
<br style="text-align:left">
Также, в окне <i>Свойства тома</i> в поле <em style="text-align:left">Скрытый том защищён</em> выводится:
<br style="text-align:left">
"<em style="text-align:left">Да (защита от повреждений!)</em>"<em style="text-align:left">.</em><br style="text-align:left">
<br style="text-align:left">
Обратите внимание, что если было предотвращено повреждение скрытого тома, <i>никакая</i> информация об этом событии
в том не записывается. После размонтирования и повторного монтирования внешнего тома в свойствах тома <i>не будет</i>
строки "<i>защита от повреждений</i>".<em style="text-align:left"><br style="text-align:left">
</em></div>
<div style="text-align:left; margin-top:19px; margin-bottom:19px; padding-top:0px; padding-bottom:0px">
<br style="text-align:left">
Проверить, защищён ли скрытый том от повреждений, можно несколькими способами:</div>
<ol style="text-align:left; margin-top:18px; margin-bottom:19px; padding-top:0px; padding-bottom:0px">
<li style="text-align:left; margin-top:0px; margin-bottom:0px; padding-top:0px; padding-bottom:0px">
После монтирования внешнего тома появляется окно с подтверждающим сообщением о том, что скрытый том защищён
(если этого сообщения не появилось, скрытый том не защищён!).</li>
<li style="text-align:left; margin-top:0px; margin-bottom:0px; padding-top:0px; padding-bottom:0px">
В окне <em style="text-align:left">Свойства тома</em>, в поле <em style="text-align:left">
Скрытый том защищён</em> выводится значение <em style="text-align:left">Да</em>.</li>
<li style="text-align:left; margin-top:0px; margin-bottom:0px; padding-top:0px; padding-bottom:0px">
Тип смонтированного внешнего тома – <em style="text-align:left">Внешний</em>.</li></ol>
<p><img src="Protection of Hidden Volumes_Image_030.png" alt="Графический интерфейс VeraCrypt"></p>
<div style="text-align:left; margin-top:19px; margin-bottom:19px; padding-top:0px; padding-bottom:0px">
<em style="text-align:left"><br style="text-align:left">
<strong style="text-align:left">ВАЖНО: Когда неприятель вынуждает вас смонтировать внешний том, вы, разумеется,
<u>НЕ должны</u> монтировать внешний том с включённой защитой скрытого тома. Вы должны монтировать его как обычный том
(после чего VeraCrypt будет показывать тип тома не "Внешний", а "Обычный"). Обратите внимание, что пока внешний
том остаётся смонтированным с включённой защитой скрытого тома, неприятель может обнаружить наличие скрытого тома
во внешнем томе (его можно будет найти до того момента, пока том не размонтирован, и даже некоторое время
после выключения компьютера – см. <a href="Unencrypted%20Data%20in%20RAM.html" style="text-align:left; color:#0080c0; text-decoration:none">
Незашифрованные данные в ОЗУ</a>).</strong></em> <br style="text-align:left">
<br style="text-align:left">
<br style="text-align:left">
<br style="text-align:left">
<em style="text-align:left">ВНИМАНИЕ</em>: Опция <em style="text-align:left">Защитить скрытый том от повреждения
при записи во внешний том</em> в окне <em style="text-align:left">Параметры монтирования</em> автоматически
сбрасывается в выключенное состояние после завершённой попытки монтирования, неважно, успешной или нет (все
уже защищённые скрытые тома, разумеется, остаются защищёнными). Поэтому эту опцию нужно включать при <i>каждом</i>
монтировании внешнего тома (если хотите, чтобы скрытый том был защищён от повреждений):<br style="text-align:left">
<br style="text-align:left">
<img src="Protection of Hidden Volumes_Image_031.png" alt="Графический интерфейс VeraCrypt"></div>
<div style="text-align:left; margin-top:19px; margin-bottom:19px; padding-top:0px; padding-bottom:0px">
<br style="text-align:left">
Если вы хотите смонтировать внешний том и защитить находящийся внутри него скрытый том, используя кэшированные пароли,
выполните следующие шаги. Удерживая нажатой клавишу <i>Control</i> (<i>Ctrl</i>), нажмите кнопку <i>Смонтировать</i>
(или выберите в меню <i>Тома</i> команду <i>Смонтировать том с параметрами</i>). Откроется диалоговое окно
<i>Параметры монтирования</i>. Включите в нём опцию <i>Защитить скрытый том от повреждения при записи во внешний том</i>
и оставьте поле ввода пароля пустым. Затем нажмите <i>OK</i>.</div>
<p>Если вам нужно смонтировать внешний том, и вы знаете, что в нём не потребуется сохранять никаких данных, тогда
наиболее удобным способом защиты скрытого тома от повреждений будет монтирование внешнего тома как доступного только
для чтения (см.
<a href="Mounting%20VeraCrypt%20Volumes.html" style="text-align:left; color:#0080c0; text-decoration:none">
Параметры монтирования</a>).</p>
<p>&nbsp;</p>
<p><a href="Security%20Requirements%20for%20Hidden%20Volumes.html" style="text-align:left; color:#0080c0; text-decoration:none; font-weight:bold">Следующий раздел &gt;&gt;</a></p>
</div><div class="ClearBoth"></div></body></html>
