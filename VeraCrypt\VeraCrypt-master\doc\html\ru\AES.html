﻿<!DOCTYPE html>
<html lang="ru">
<head>
<meta http-equiv="content-type" content="text/html; charset=utf-8" />
<title>VeraCrypt - Бесплатное надёжное шифрование дисков с открытым исходным кодом</title>
<meta name="description" content="VeraCrypt это бесплатное программное обеспечение для шифрования дисков с открытым исходным кодом для Windows, Mac OS X (macOS) и Linux. В случае, если злоумышленник вынуждает вас раскрыть пароль, VeraCrypt обеспечивает правдоподобное отрицание наличия шифрования. В отличие от пофайлового шифрования, VeraCrypt шифрует данные в реальном времени (на лету), автоматически, прозрачно, требует очень мало памяти и не использует временные незашифрованные файлы."/>
<meta name="keywords" content="encryption, security, шифрование, безопасность"/>
<link href="styles.css" rel="stylesheet" type="text/css" />
</head>
<body>

<div>
<a href="Documentation.html"><img src="VeraCrypt128x128.png" alt="VeraCrypt"/></a>
</div>

<div id="menu">
	<ul>
	  <li><a href="Home.html">Начало</a></li>
	  <li><a href="Code.html">Исходный код</a></li>
	  <li><a href="Downloads.html">Загрузить</a></li>
	  <li><a class="active" href="Documentation.html">Документация</a></li>
	  <li><a href="Donation.html">Поддержать разработку</a></li>
	  <li><a href="https://sourceforge.net/p/veracrypt/discussion/" target="_blank">Форум</a></li>
	</ul>
</div>

<div>
<p>
<a href="Documentation.html">Документация</a>
<img src="arrow_right.gif" alt=">>" style="margin-top: 5px">
<a href="Encryption%20Algorithms.html">Алгоритмы шифрования</a>
<img src="arrow_right.gif" alt=">>" style="margin-top: 5px">
<a href="AES.html">AES</a>
</p></div>

<div class="wikidoc">
<h1>Алгоритм шифрования AES</h1>
<div style="text-align:left; margin-top:19px; margin-bottom:19px; padding-top:0px; padding-bottom:0px">
Advanced Encryption Standard (AES) – это одобренный FIPS (Федеральные стандарты обработки информации) криптографический
алгоритм (также известен как Rijndael, авторы: Joan Daemen и Vincent Rijmen, опубликован в 1998 году), разрешённый
к применению федеральными ведомствами и учреждениями США для криптостойкой защиты секретной информации [3].
VeraCrypt использует AES с 14 раундами и 256-битовым ключом (то есть стандарт AES-256, опубликованный в 2001 году), работающий
<a href="Modes%20of%20Operation.html" style="text-align:left; color:#0080c0; text-decoration:none">
в режиме XTS</a> (см. раздел <a href="Modes%20of%20Operation.html" style="text-align:left; color:#0080c0; text-decoration:none">
Режимы работы</a>).</div>
<div style="text-align:left; margin-top:19px; margin-bottom:19px; padding-top:0px; padding-bottom:0px">
В июне 2003 года, после того как Агентство национальной безопасности США (NSA, US National Security Agency)
провело исследование и анализ AES, американский комитет CNSS (Committee on National Security Systems) объявил
в [1], что реализация и надёжность AES-256 (и AES-192) достаточны для защиты секретной информации вплоть до
уровня Top Secret («Совершенно секретно»). Это относится ко всем правительственным ведомствам и
учреждениям США, намеревающимся приобрести или использовать продукты, включающие Advanced Encryption Standard (AES),
для обеспечения требований информационной безопасности, относящейся к защите национальных систем безопасности
и/или информации, связанной с госбезопасностью [1].
</div>
<div style="text-align:left; margin-top:19px; margin-bottom:19px; padding-top:0px; padding-bottom:0px">
<a href="Camellia.html" style="text-align:left; color:#0080c0; text-decoration:none; font-weight:bold">Следующий раздел &gt;&gt;</a></div>
</div><div class="ClearBoth"></div></body></html>
