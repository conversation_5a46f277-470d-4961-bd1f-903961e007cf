﻿<!DOCTYPE html>
<html lang="ru">
<head>
<meta http-equiv="content-type" content="text/html; charset=utf-8" />
<title>VeraCrypt - Бесплатное надёжное шифрование дисков с открытым исходным кодом</title>
<meta name="description" content="VeraCrypt это бесплатное программное обеспечение для шифрования дисков с открытым исходным кодом для Windows, Mac OS X (macOS) и Linux. В случае, если злоумышленник вынуждает вас раскрыть пароль, VeraCrypt обеспечивает правдоподобное отрицание наличия шифрования. В отличие от пофайлового шифрования, VeraCrypt шифрует данные в реальном времени (на лету), автоматически, прозрачно, требует очень мало памяти и не использует временные незашифрованные файлы."/>
<meta name="keywords" content="encryption, security, шифрование, безопасность"/>
<link href="styles.css" rel="stylesheet" type="text/css" />
</head>
<body>

<div>
<a href="Documentation.html"><img src="VeraCrypt128x128.png" alt="VeraCrypt"/></a>
</div>

<div id="menu">
	<ul>
	  <li><a href="Home.html">Начало</a></li>
	  <li><a href="Code.html">Исходный код</a></li>
	  <li><a href="Downloads.html">Загрузить</a></li>
	  <li><a class="active" href="Documentation.html">Документация</a></li>
	  <li><a href="Donation.html">Поддержать разработку</a></li>
	  <li><a href="https://sourceforge.net/p/veracrypt/discussion/" target="_blank">Форум</a></li>
	</ul>
</div>

<div>
<p>
<a href="Documentation.html">Документация</a>
<img src="arrow_right.gif" alt=">>" style="margin-top: 5px">
<a href="Security%20Requirements%20and%20Precautions.html">Требования безопасности и меры предосторожности</a>
<img src="arrow_right.gif" alt=">>" style="margin-top: 5px">
<a href="VeraCrypt%20Memory%20Protection.html">Защита памяти в VeraCrypt</a>
</p></div>

<div class="wikidoc">
<h1>Защита памяти в VeraCrypt</h1>
<h2>Введение</h2>
<p>VeraCrypt всегда стремится улучшить взаимодействие с пользователем, сохраняя при этом наивысший уровень безопасности. Одна из таких функций безопасности – механизм защиты памяти. Однако для тех пользователей, которым требуются специальные возможности взаимодействия с ОС, мы предоставили возможность отключить этот механизм.</p>
<h2>Общие сведения</h2>
<p>
Защита памяти гарантирует, что процессам, не являющимся административными, запрещён доступ к памяти процесса VeraCrypt. Это служит двум основным целям:
<ul>
	<li>ЗАЩИТА ОТ ВРЕДОНОСНЫХ ДЕЙСТВИЙ: Этот механизм предотвращает внедрение вредоносных данных или кода в процесс VeraCrypt неадминистративными процессами.</li>
	<li>ЗАЩИТА КОНФИДЕНЦИАЛЬНЫХ ДАННЫХ: Хотя VeraCrypt разработан так, чтобы не оставлять конфиденциальных данных в памяти, эта функция обеспечивает дополнительный уровень безопасности, гарантируя, что другие процессы, не являющиеся административными, не смогут получить доступ к потенциально конфиденциальной информации или извлечь её.</li>
</ul>
</p>
<h2>Зачем нужна опция отключения защиты памяти?</h2>
<p>
	Некоторым инструментам специальных возможностей, таким как экранный диктор, требуется доступ к памяти процессов ПО для эффективной интерпретации пользовательского интерфейса (UI) и взаимодействия с ним. Защита памяти VeraCrypt непреднамеренно препятствовала работе таких инструментов. Чтобы гарантировать, что пользователи смогут беспрепятственно применять инструменты специальных возможностей в VeraCrypt, мы и ввели эту опцию отключения защиты.
</p>
<h2>Как включить/отключить защиту памяти?</h2>
<p>
	По умолчанию защита памяти включена. Её можно отключить через главное окно VeraCrypt или во время установки.
	<ol>
		<li>Во время установки:
			<ul>
				<li>В мастере установки найдите опцию <em>Отключить защиту памяти для совместимости со специальными возможностями</em>.</li>
				<li>Установите флажок, если хотите отключить защиту памяти. Оставьте этот флажок снятым, чтобы продолжать использовать защиту памяти.</li>
				<li>Приступайте к остальной части установки.</li>
			</ul>
		</li>
		<li>После установки:
			<ul>
				<li>Откройте VeraCrypt и в меню <em>Настройки</em> выберите пункт <em>Производительность и драйвер</em>.</li>
				<li>Найдите и установите/снимите флажок с опции <em>Отключить защиту памяти для совместимости со специальными возможностями</em> согласно вашим потребностям. Появится уведомление, что для вступления изменений в силу необходима перезагрузка ОС.</li>
				<li>Нажмите <em>OK</em>.</li>
			</ul>
		</li>
		<li>При обновлении или исправлении/переустановке:
			<ul>
				<li>В мастере установки найдите опцию <em>Отключить защиту памяти для совместимости со специальными возможностями</em>.</li>
				<li>Найдите и установите/снимите флажок с опции <em>Отключить защиту памяти для совместимости со специальными возможностями</em> согласно вашим потребностям.</li>
				<li>Приступайте к остальной части обновления или исправления/переустановки.</li>
				<li>Если вы изменили настройку защиты памяти, появится уведомление, что необходима перезагрузка ОС.</li>
			</ul>

		</li>
	</ol>
<h2>О потенциальных рисках</h2>
<p>
Хотя отключение защиты памяти может быть важным для некоторых пользователей, необходимо понимать связанные с этим риски:
<ul>
	<li><b>ПОТЕНЦИАЛЬНОЕ ВОЗДЕЙСТВИЕ:</b> Отключение защиты может подвергнуть память процесса VeraCrypt воздействию вредоносных процессов.</li>
	<li><b>НАИЛУЧШЕЕ РЕШЕНИЕ:</b> Если вам не требуются специальные средства для использования VeraCrypt, рекомендуется оставить защиту памяти включённой.</li>
</ul>
</p>
<h2>Вопросы и ответы</h2>
<p>
	<b>По умолчанию защита памяти включена или выключена?</b><br>
	Защита памяти по умолчанию включена.
</p>
<p>
	<b>Как узнать, включена или отключена защита памяти?</b><br>
	Проверить состояние защиты памяти можно из главного окна VeraCrypt. Перейдите в меню <em>Настройки</em> и выберите пункт <em>Производительность и драйвер</em>. Если опция <em>Отключить защиту памяти для совместимости со специальными возможностями</em> включена, то защита памяти отключена. Если эта опция отключена, то защита памяти включена.
</p>
<p>
	<b>Снизит ли отключение защиты памяти надёжность шифрования VeraCrypt?</b><br>
	Нет, алгоритмы шифрования и их надёжность остаются прежними. Затрагивается только защита от потенциального перехвата памяти и внедрения в неё процессов, не являющимися административными.
</p>
<p>
	<b>Я не пользуюсь инструментами специальных возможностей. Нужно ли мне отключить эту функцию?</b><br>
	Нет, лучше оставить защиту памяти включённой для дополнительной безопасности.
</p>
</div><div class="ClearBoth"></div></body></html>
