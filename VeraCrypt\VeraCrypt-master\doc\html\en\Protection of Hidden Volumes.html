<!DOCTYPE html>
<html lang="en">
<head>
<meta http-equiv="content-type" content="text/html; charset=utf-8" />
<title>VeraCrypt - Free Open source disk encryption with strong security for the Paranoid</title>
<meta name="description" content="VeraCrypt is free open-source disk encryption software for Windows, Mac OS X and Linux. In case an attacker forces you to reveal the password, VeraCrypt provides plausible deniability. In contrast to file encryption, data encryption performed by VeraCrypt is real-time (on-the-fly), automatic, transparent, needs very little memory, and does not involve temporary unencrypted files."/>
<meta name="keywords" content="encryption, security"/>
<link href="styles.css" rel="stylesheet" type="text/css" />
</head>
<body>

<div>
<a href="Documentation.html"><img src="VeraCrypt128x128.png" alt="VeraCrypt"/></a>
</div>

<div id="menu">
	<ul>
	  <li><a href="Home.html">Home</a></li>
	  <li><a href="Code.html">Source Code</a></li>
	  <li><a href="Downloads.html">Downloads</a></li>
	  <li><a class="active" href="Documentation.html">Documentation</a></li>
	  <li><a href="Donation.html">Donate</a></li>
	  <li><a href="https://sourceforge.net/p/veracrypt/discussion/" target="_blank">Forums</a></li>
	</ul>
</div>

<div>
<p>
<a href="Documentation.html">Documentation</a>
<img src="arrow_right.gif" alt=">>" style="margin-top: 5px">
<a href="Plausible%20Deniability.html">Plausible Deniability</a>
<img src="arrow_right.gif" alt=">>" style="margin-top: 5px">
<a href="Hidden%20Volume.html">Hidden Volume</a>
<img src="arrow_right.gif" alt=">>" style="margin-top: 5px">
<a href="Protection%20of%20Hidden%20Volumes.html">Protection of Hidden Volumes</a>
</p></div>

<div class="wikidoc">
<h1>Protection of Hidden Volumes Against Damage</h1>
<div style="text-align:left; margin-top:19px; margin-bottom:19px; padding-top:0px; padding-bottom:0px">
If you mount a VeraCrypt volume within which there is a <a href="Hidden%20Volume.html" style="text-align:left; color:#0080c0; text-decoration:none">
hidden volume</a>, you may <em style="text-align:left">read</em> data stored on the (outer) volume without any risk. However, if you (or the operating system) need to
<em style="text-align:left">save</em> data to the outer volume, there is a risk that the hidden volume will get damaged (overwritten). To prevent this, you should protect the hidden volume in a way described in this section.</div>
<div style="text-align:left; margin-top:19px; margin-bottom:19px; padding-top:0px; padding-bottom:0px">
When mounting an outer volume, type in its password and before clicking <em style="text-align:left">
OK, </em>click <em style="text-align:left">Mount Options</em>:</div>
<div style="text-align:left; margin-top:19px; margin-bottom:19px; padding-top:0px; padding-bottom:0px">
<img src="Protection of Hidden Volumes_Image_027.jpg" alt="VeraCrypt GUI"></div>
<div style="text-align:left; margin-top:19px; margin-bottom:19px; padding-top:0px; padding-bottom:0px">
&nbsp;</div>
<div style="text-align:left; margin-top:19px; margin-bottom:19px; padding-top:0px; padding-bottom:0px">
In the <em style="text-align:left">Mount Options </em>dialog window, enable the option '<em style="text-align:left">Protect hidden volume against damage caused by writing to outer volume</em> '. In the '<em style="text-align:left">Password to hidden volume</em>'
 input field, type the password for the hidden volume. Click <em style="text-align:left">
OK </em>and, in the main password entry dialog, click <em style="text-align:left">
OK</em>.</div>
<div style="text-align:left; margin-top:19px; margin-bottom:19px; padding-top:0px; padding-bottom:0px">
<img src="Protection of Hidden Volumes_Image_028.jpg" alt="Mounting with hidden protection"></div>
<div style="text-align:left; margin-top:19px; margin-bottom:19px; padding-top:0px; padding-bottom:0px">
<br style="text-align:left">
<br style="text-align:left">
Both passwords must be correct; otherwise, the outer volume will not be mounted. When hidden volume protection is enabled, VeraCrypt does
<em style="text-align:left">not</em> actually mount the hidden volume. It only decrypts its header (in RAM) and retrieves information about the size of the hidden volume (from the decrypted header). Then, the outer volume is mounted and any attempt to save
 data to the area of the hidden volume will be rejected (until the outer volume is unmounted).
<strong style="text-align:left">Note that VeraCrypt never modifies the filesystem (e.g., information about allocated clusters, amount of free space, etc.) within the outer volume in any way. As soon as the volume is unmounted, the protection is lost. When
 the volume is mounted again, it is not possible to determine whether the volume has used hidden volume protection or not. The hidden volume protection can be activated only by users who supply the correct password (and/or keyfiles) for the hidden volume (each
 time they mount the outer volume). <br style="text-align:left">
</strong><br style="text-align:left">
As soon as a write operation to the hidden volume area is denied/prevented (to protect the hidden volume), the entire host volume (both the outer and the hidden volume) becomes write-protected until unmounted (the VeraCrypt driver reports the 'invalid parameter'
 error to the system upon each attempt to write data to the volume). This preserves plausible deniability (otherwise certain kinds of inconsistency within the file system could indicate that this volume has used hidden volume protection). When damage to hidden
 volume is prevented, a warning is displayed (provided that the VeraCrypt Background Task is enabled &ndash; see the chapter
<a href="VeraCrypt%20Background%20Task.html" style="text-align:left; color:#0080c0; text-decoration:none">
VeraCrypt Background Task</a>). Furthermore, the type of the mounted outer volume displayed in the main window changes to '<em style="text-align:left">Outer(!)</em> ':</div>
<div style="text-align:left; margin-top:19px; margin-bottom:19px; padding-top:0px; padding-bottom:0px">
<img src="Protection of Hidden Volumes_Image_029.jpg" alt="VeraCrypt GUI"></div>
<div style="text-align:left; margin-top:19px; margin-bottom:19px; padding-top:0px; padding-bottom:0px">
<br style="text-align:left">
<br style="text-align:left">
Moreover, the field <em style="text-align:left">Hidden Volume Protected </em>in the
<em style="text-align:left">Volume Properties </em>dialog window says:<br style="text-align:left">
'<em style="text-align:left">Yes (damage prevented!)</em>'<em style="text-align:left">.</em><br style="text-align:left">
<br style="text-align:left">
Note that when damage to hidden volume is prevented, <em style="text-align:left">
no</em> information about the event is written to the volume. When the outer volume is unmounted and mounted again, the volume properties will
<em style="text-align:left">not </em>display the string &quot;<em style="text-align:left">damage prevented</em>&quot;.<em style="text-align:left"><br style="text-align:left">
</em></div>
<div style="text-align:left; margin-top:19px; margin-bottom:19px; padding-top:0px; padding-bottom:0px">
<br style="text-align:left">
There are several ways to check that a hidden volume is being protected against damage:</div>
<ol style="text-align:left; margin-top:18px; margin-bottom:19px; padding-top:0px; padding-bottom:0px">
<li style="text-align:left; margin-top:0px; margin-bottom:0px; padding-top:0px; padding-bottom:0px">
A confirmation message box saying that hidden volume is being protected is displayed after the outer volume is mounted (if it is not displayed, the hidden volume is not protected!).
</li><li style="text-align:left; margin-top:0px; margin-bottom:0px; padding-top:0px; padding-bottom:0px">
In the <em style="text-align:left">Volume Properties </em>dialog, the field <em style="text-align:left">
Hidden Volume Protected </em>says '<em style="text-align:left">Yes</em>': </li><li style="text-align:left; margin-top:0px; margin-bottom:0px; padding-top:0px; padding-bottom:0px">
The type of the mounted outer volume is <em style="text-align:left">Outer</em>: </li></ol>
<p><img src="Protection of Hidden Volumes_Image_030.jpg" alt="VeraCrypt GUI"></p>
<div style="text-align:left; margin-top:19px; margin-bottom:19px; padding-top:0px; padding-bottom:0px">
<em style="text-align:left"><br style="text-align:left">
<strong style="text-align:left">Important: You are the only person who can mount your outer volume with the hidden volume protection enabled (since nobody else knows your hidden volume password). When an adversary asks you to mount an outer volume, you of course
 must </strong></em><strong style="text-align:left">not</strong><em style="text-align:left"><strong style="text-align:left"> mount it with the hidden volume protection enabled. You must mount it as a normal volume (and then VeraCrypt will not show the volume
 type &quot;Outer&quot; but &quot;Normal&quot;). The reason is that, during the time when an outer volume is mounted with the hidden volume protection enabled, the adversary
</strong></em><strong style="text-align:left">can</strong><em style="text-align:left"><strong style="text-align:left"> find out that a hidden volume exists within the outer volume (he/she will be able to find it out until the volume is unmounted and possibly
 even some time after the computer has been powered off - see <a href="Unencrypted%20Data%20in%20RAM.html" style="text-align:left; color:#0080c0; text-decoration:none">
Unencrypted Data in RAM</a>).</strong></em> <br style="text-align:left">
<br style="text-align:left">
<br style="text-align:left">
<br style="text-align:left">
<em style="text-align:left">Warning</em>: Note that the option '<em style="text-align:left">Protect hidden volume against damage caused by writing to outer volume</em>' in the
<em style="text-align:left">Mount Options </em>dialog window is automatically disabled after a mount attempt is completed, no matter whether it is successful or not (all hidden volumes that are already being protected will, of course, continue to be protected).
 Therefore, you need to check that option <em style="text-align:left">each </em>time you attempt to mount the outer volume (if you wish the hidden volume to be protected):<br style="text-align:left">
<br style="text-align:left">
<img src="Protection of Hidden Volumes_Image_031.jpg" alt="VeraCrypt GUI"></div>
<div style="text-align:left; margin-top:19px; margin-bottom:19px; padding-top:0px; padding-bottom:0px">
<br style="text-align:left">
If you want to mount an outer volume and protect a hidden volume within using cached passwords, then follow these steps: Hold down the
<em style="text-align:left">Control </em>(<em style="text-align:left">Ctrl</em>) key when clicking
<em style="text-align:left">Mount </em>(or select <em style="text-align:left">Mount with Options
</em>from the <em style="text-align:left">Volumes </em>menu). This will open the <em style="text-align:left">
Mount Options </em>dialog. Enable the option '<em style="text-align:left">Protect hidden volume against damage caused by writing to outer volume</em>' and leave the password box empty. Then click
<em style="text-align:left">OK</em>.</div>
<p>If you need to mount an outer volume and you know that you will not need to save any data to it, then the most comfortable way of protecting the hidden volume against damage is mounting the outer volume as read-only (see the section
<a href="Mounting%20VeraCrypt%20Volumes.html" style="text-align:left; color:#0080c0; text-decoration:none">
Mount Options</a>).</p>
<p>&nbsp;</p>
<p><a href="Security%20Requirements%20for%20Hidden%20Volumes.html" style="text-align:left; color:#0080c0; text-decoration:none; font-weight:bold">Next Section &gt;&gt;</a></p>
</div><div class="ClearBoth"></div></body></html>
