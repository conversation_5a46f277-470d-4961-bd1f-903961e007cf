﻿<!DOCTYPE html>
<html lang="ru">
<head>
<meta http-equiv="content-type" content="text/html; charset=utf-8" />
<title>VeraCrypt - Бесплатное надёжное шифрование дисков с открытым исходным кодом</title>
<meta name="description" content="VeraCrypt это бесплатное программное обеспечение для шифрования дисков с открытым исходным кодом для Windows, Mac OS X (macOS) и Linux. В случае, если злоумышленник вынуждает вас раскрыть пароль, VeraCrypt обеспечивает правдоподобное отрицание наличия шифрования. В отличие от пофайлового шифрования, VeraCrypt шифрует данные в реальном времени (на лету), автоматически, прозрачно, требует очень мало памяти и не использует временные незашифрованные файлы."/>
<meta name="keywords" content="encryption, security, шифрование, безопасность"/>
<link href="styles.css" rel="stylesheet" type="text/css" />
</head>
<body>

<div>
<a href="Documentation.html"><img src="VeraCrypt128x128.png" alt="VeraCrypt"/></a>
</div>

<div id="menu">
	<ul>
	  <li><a href="Home.html">Начало</a></li>
	  <li><a href="Code.html">Исходный код</a></li>
	  <li><a href="Downloads.html">Загрузить</a></li>
	  <li><a class="active" href="Documentation.html">Документация</a></li>
	  <li><a href="Donation.html">Поддержать разработку</a></li>
	  <li><a href="https://sourceforge.net/p/veracrypt/discussion/" target="_blank">Форум</a></li>
	</ul>
</div>

<div>
<p>
<a href="Documentation.html">Документация</a>
<img src="arrow_right.gif" alt=">>" style="margin-top: 5px">
<a href="Security%20Requirements%20and%20Precautions.html">Требования безопасности и меры предосторожности</a>
<img src="arrow_right.gif" alt=">>" style="margin-top: 5px">
<a href="Reallocated%20Sectors.html">Перераспределённые сектора</a>
</p></div>

<div class="wikidoc">
<div>
<h1>Перераспределённые сектора</h1>
<p>Некоторые устройства хранения информации (например, жёсткие диски) перераспределяют/переназначают плохие сектора
внутренними методами. Как только устройство обнаруживает сектор, в который невозможно записать данные, оно помечает
такой сектор как плохой и переназначает его на другой сектор, расположенный в скрытой зарезервированной области диска.
Все последующие операции чтения/записи с этим плохим сектором перенаправляются на сектор в зарезервированной области.
Это означает, что любые содержащиеся в плохом секторе данные остаются на диске и их нельзя стереть (перезаписать
другими данными), что может повлечь за собой различные проблемы с безопасностью. Например, в плохом секторе могут
оставаться незашифрованные данные, которые должны были быть зашифрованы "на месте". Аналогично, в плохом секторе
могут сохраниться данные, которые должны быть удалены (например, в процессе создания скрытой операционной системы).
Если сектор перераспределён, это может неблагоприятно сказаться на правдоподобности отрицания наличия шифрования
(см. раздел
<a href="Plausible%20Deniability.html"><em>Правдоподобное отрицание наличия шифрования</em></a>). Другие
примеры возможных проблем с безопасностью приведены в разделе
<a href="Security%20Requirements%20and%20Precautions.html">
<em>Требования безопасности и меры предосторожности</em></a>. Имейте в виду, однако, что этот список неполный
(просто примеры). Также учтите, что VeraCrypt <em>не может</em> предотвратить никаких проблем с безопасностью,
связанных с перераспределёнными секторами или вызванных ими. Выяснить количество перераспределённых секторов на
жёстком диске можно, например, с помощью сторонних программ для чтения так называемой информации S.M.A.R.T.</p>
</div>
</div><div class="ClearBoth"></div></body></html>
