﻿<!DOCTYPE html>
<html lang="ru">
<head>
<meta http-equiv="content-type" content="text/html; charset=utf-8" />
<title>VeraCrypt - Бесплатное надёжное шифрование дисков с открытым исходным кодом</title>
<meta name="description" content="VeraCrypt это бесплатное программное обеспечение для шифрования дисков с открытым исходным кодом для Windows, Mac OS X (macOS) и Linux. В случае, если злоумышленник вынуждает вас раскрыть пароль, VeraCrypt обеспечивает правдоподобное отрицание наличия шифрования. В отличие от пофайлового шифрования, VeraCrypt шифрует данные в реальном времени (на лету), автоматически, прозрачно, требует очень мало памяти и не использует временные незашифрованные файлы."/>
<meta name="keywords" content="encryption, security, шифрование, безопасность"/>
<link href="styles.css" rel="stylesheet" type="text/css" />
</head>
<body>

<div>
<a href="Documentation.html"><img src="VeraCrypt128x128.png" alt="VeraCrypt"/></a>
</div>

<div id="menu">
	<ul>
	  <li><a href="Home.html">Начало</a></li>
	  <li><a href="Code.html">Исходный код</a></li>
	  <li><a href="Downloads.html">Загрузить</a></li>
	  <li><a class="active" href="Documentation.html">Документация</a></li>
	  <li><a href="Donation.html">Поддержать разработку</a></li>
	  <li><a href="https://sourceforge.net/p/veracrypt/discussion/" target="_blank">Форум</a></li>
	</ul>
</div>

<div>
<p>
<a href="Documentation.html">Документация</a>
<img src="arrow_right.gif" alt=">>" style="margin-top: 5px">
<a href="Encryption%20Algorithms.html">Алгоритмы шифрования</a>
<img src="arrow_right.gif" alt=">>" style="margin-top: 5px">
<a href="Twofish.html">Twofish</a>
</p></div>

<div class="wikidoc">
<h1>Алгоритм шифрования Twofish</h1>
<div style="text-align:left; margin-top:19px; margin-bottom:19px; padding-top:0px; padding-bottom:0px">
<p>Авторы – Bruce Schneier, John Kelsey, Doug Whiting, David Wagner, Chris Hall и Niels Ferguson; алгоритм
опубликован в 1998 году. Использует ключ длиной 256 бит и блок размером 128 бит, работает в режиме XTS (см. раздел
<a href="Modes%20of%20Operation.html"><em>Режимы работы</em></a>). Twofish был одним из финалистов конкурса AES.
Отличительная особенность – применение зависящих от ключа S-блоков. Twofish можно рассматривать как совокупность
2<sup>128</sup> различных криптосистем, где выбором криптосистемы управляют 128 бит, формируемые из 256-битового
ключа [4]. В документе [13] команда Twofish утверждает, что зависимые от ключа S-блоки представляют собой форму
запаса безопасности против неизвестных атак [4].</p>
<p>&nbsp;</p>
<p><a href="Cascades.html" style="text-align:left; color:#0080c0; text-decoration:none; font-weight:bold">Следующий раздел &gt;&gt;</a></p>
</div>
</div><div class="ClearBoth"></div></body></html>
