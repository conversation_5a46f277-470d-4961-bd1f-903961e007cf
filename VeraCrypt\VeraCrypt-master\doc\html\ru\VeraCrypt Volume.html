﻿<!DOCTYPE html>
<html lang="ru">
<head>
<meta http-equiv="content-type" content="text/html; charset=utf-8" />
<title>VeraCrypt - Бесплатное надёжное шифрование дисков с открытым исходным кодом</title>
<meta name="description" content="VeraCrypt это бесплатное программное обеспечение для шифрования дисков с открытым исходным кодом для Windows, Mac OS X (macOS) и Linux. В случае, если злоумышленник вынуждает вас раскрыть пароль, VeraCrypt обеспечивает правдоподобное отрицание наличия шифрования. В отличие от пофайлового шифрования, VeraCrypt шифрует данные в реальном времени (на лету), автоматически, прозрачно, требует очень мало памяти и не использует временные незашифрованные файлы."/>
<meta name="keywords" content="encryption, security, шифрование, безопасность"/>
<link href="styles.css" rel="stylesheet" type="text/css" />
</head>
<body>

<div>
<a href="Documentation.html"><img src="VeraCrypt128x128.png" alt="VeraCrypt"/></a>
</div>

<div id="menu">
	<ul>
	  <li><a href="Home.html">Начало</a></li>
	  <li><a href="Code.html">Исходный код</a></li>
	  <li><a href="Downloads.html">Загрузить</a></li>
	  <li><a class="active" href="Documentation.html">Документация</a></li>
	  <li><a href="Donation.html">Поддержать разработку</a></li>
	  <li><a href="https://sourceforge.net/p/veracrypt/discussion/" target="_blank">Форум</a></li>
	</ul>
</div>

<div>
<p>
<a href="Documentation.html">Документация</a>
<img src="arrow_right.gif" alt=">>" style="margin-top: 5px">
<a href="VeraCrypt%20Volume.html">Том VeraCrypt</a>
</p></div>

<div class="wikidoc">
<h1>Том VeraCrypt</h1>
<p>Существует два типа томов VeraCrypt:</p>
<ul>
<li>на основе файла (контейнер) </li>
<li>на основе раздела/устройства (несистемного)</li></ul>
<p>Примечание. Помимо создания виртуальных томов указанных выше типов, VeraCrypt также может шифровать физический
раздел/диск, на котором установлена Windows (подробности см. в главе
<a href="System%20Encryption.html"><em>Шифрование системы</em></a>).<br>
<br>
Том VeraCrypt на основе файла – это обычный файл, который может находиться на любом устройстве хранения данных.
Он содержит (является хостом) полностью независимое зашифрованное виртуальное дисковое устройство.<br>
<br>
Раздел VeraCrypt – это раздел на жёстком диске, зашифрованный с помощью VeraCrypt. Также можно шифровать целиком
жёсткие диски (в том числе подключаемые по USB), флеш-накопители USB ("флешки") и устройства хранения данных других типов.</p>
<p><a href="Creating%20New%20Volumes.html" style="text-align:left; color:#0080c0; text-decoration:none; font-weight:bold">Следующий раздел &gt;&gt;</a></p>
</div>
</body></html>
