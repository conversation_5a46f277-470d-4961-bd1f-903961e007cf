<!DOCTYPE html>
<html lang="en">
<head>
<meta http-equiv="content-type" content="text/html; charset=utf-8" />
<title>VeraCrypt - Free Open source disk encryption with strong security for the Paranoid</title>
<meta name="description" content="VeraCrypt is free open-source disk encryption software for Windows, Mac OS X and Linux. In case an attacker forces you to reveal the password, VeraCrypt provides plausible deniability. In contrast to file encryption, data encryption performed by VeraCrypt is real-time (on-the-fly), automatic, transparent, needs very little memory, and does not involve temporary unencrypted files."/>
<meta name="keywords" content="encryption, security"/>
<link href="styles.css" rel="stylesheet" type="text/css" />
</head>
<body>

<div>
<a href="Documentation.html"><img src="VeraCrypt128x128.png" alt="VeraCrypt"/></a>
</div>

<div id="menu">
	<ul>
	  <li><a href="Home.html">Home</a></li>
	  <li><a href="Code.html">Source Code</a></li>
	  <li><a href="Downloads.html">Downloads</a></li>
	  <li><a class="active" href="Documentation.html">Documentation</a></li>
	  <li><a href="Donation.html">Donate</a></li>
	  <li><a href="https://sourceforge.net/p/veracrypt/discussion/" target="_blank">Forums</a></li>
	</ul>
</div>

<div>
<p>
<a href="Documentation.html">Documentation</a>
<img src="arrow_right.gif" alt=">>" style="margin-top: 5px">
<a href="System%20Encryption.html">System Encryption</a>
</p></div>

<div class="wikidoc">
<h1>System Encryption</h1>
<div style="text-align:left; margin-top:19px; margin-bottom:19px; padding-top:0px; padding-bottom:0px">
VeraCrypt can on-the-fly encrypt a system partition or entire system drive, i.e. a partition or drive where Windows is installed and from which it boots.</div>
<div style="text-align:left; margin-top:19px; margin-bottom:19px; padding-top:0px; padding-bottom:0px">
System encryption provides the highest level of security and privacy, because all files, including any temporary files that Windows and applications create on the system partition (typically, without your knowledge or consent), hibernation files, swap files,
 etc., are always permanently encrypted (even when power supply is suddenly interrupted). Windows also records large amounts of potentially sensitive data, such as the names and locations of files you open, applications you run, etc. All such log files and
 registry entries are always permanently encrypted as well.</div>

<div style="text-align:left; margin-top:19px; margin-bottom:19px; padding-top:0px; padding-bottom:0px">
<strong>Note on SSDs and TRIM:</strong>
When using system encryption on SSDs, it's important to consider the implications of the TRIM operation, which can potentially reveal information about which sectors on the drive are not in use. For detailed guidance on how TRIM operates with VeraCrypt and how to manage its settings for enhanced security, please refer to the <a href="Trim%20Operation.html">TRIM Operation</a> documentation.
</div>

<div style="text-align:left; margin-top:19px; margin-bottom:19px; padding-top:0px; padding-bottom:0px">
System encryption involves pre-boot authentication, which means that anyone who wants to gain access and use the encrypted system, read and write files stored on the system drive, etc., will need to enter the correct password each time before Windows boots
 (starts). Pre-boot authentication is handled by the VeraCrypt Boot Loader, which resides in the first track of the boot drive and on the
<a href="VeraCrypt%20Rescue%20Disk.html" style="text-align:left; color:#0080c0; text-decoration:none">
VeraCrypt Rescue Disk (see below)</a>.</div>
<div style="text-align:left; margin-top:19px; margin-bottom:19px; padding-top:0px; padding-bottom:0px">
Note that VeraCrypt can encrypt an existing unencrypted system partition/drive in-place while the operating system is running (while the system is being encrypted, you can use your computer as usual without any restrictions). Likewise, a VeraCrypt-encrypted
 system partition/drive can be decrypted in-place while the operating system is running. You can interrupt the process of encryption or decryption anytime, leave the partition/drive partially unencrypted, restart or shut down the computer, and then resume the
 process, which will continue from the point it was stopped.</div>
<div style="text-align:left; margin-top:19px; margin-bottom:19px; padding-top:0px; padding-bottom:0px">
The mode of operation used for system encryption is <a href="Modes%20of%20Operation.html" style="text-align:left; color:#0080c0; text-decoration:none">
XTS</a> (see the section <a href="Modes%20of%20Operation.html" style="text-align:left; color:#0080c0; text-decoration:none">
Modes of Operation</a>). For further technical details of system encryption, see the section
<a href="Encryption%20Scheme.html" style="text-align:left; color:#0080c0; text-decoration:none">
Encryption Scheme</a> in the chapter <a href="Technical%20Details.html" style="text-align:left; color:#0080c0; text-decoration:none">
Technical Details</a>.</div>
<div style="text-align:left; margin-top:19px; margin-bottom:19px; padding-top:0px; padding-bottom:0px">
To encrypt a system partition or entire system drive, select <em style="text-align:left">
System</em> &gt; <em style="text-align:left">Encrypt System Partition/Drive</em> and then follow the instructions in the wizard. To decrypt a system partition/drive, select
<em style="text-align:left">System</em> &gt; <em style="text-align:left">Permanently Decrypt System Partition/Drive</em>.</div>
<div style="text-align:left; margin-top:19px; margin-bottom:19px; padding-top:0px; padding-bottom:0px">
Because of BIOS requirement, the pre-boot password is typed using <strong>US keyboard layout.
</strong>During the system encryption process, VeraCrypt automatically and transparently switches the keyboard to US layout in order to ensure that the password value typed will match the one typed in pre-boot mode. 
However, pasting the password from the clipboard can override this protective measure. To prevent any issues arising from this discrepancy, VeraCrypt disables the option to paste passwords from the clipboard in the system encryption wizard. 
Thus, when setting or entering your password, it's crucial to type it manually using the same keys as when creating the system encryption, ensuring consistent access to your encrypted system.</div>
<p>Note: By default, Windows 7 and later boot from a special small partition. The partition contains files that are required to boot the system. Windows allows only applications that have administrator privileges to write to the partition (when the system is
 running). In EFI boot mode, which is the default on modern PCs, VeraCrypt can not encrypt this partition since it must remain unencrypted so that the BIOS can load the EFI bootloader from it. This in turn implies that in EFI boot mode, VeraCrypt offers only to encrypt the system partition where Windows is installed (the user can later manually encrypt other data partitions using VeraCrypt).
 In MBR legacy boot mode, VeraCrypt encrypts the partition only if you choose to encrypt the whole system drive (as opposed to choosing to encrypt only the partition where Windows is installed).</p>
<p>&nbsp;</p>
<p><a href="Hidden%20Operating%20System.html" style="text-align:left; color:#0080c0; text-decoration:none; font-weight:bold">Next Section &gt;&gt;</a></p>
</div>
</body></html>
