<!DOCTYPE html>
<html lang="ru">
<head>
<meta http-equiv="content-type" content="text/html; charset=utf-8" />
<title>VeraCrypt - Бесплатное надёжное шифрование дисков с открытым исходным кодом</title>
<meta name="description" content="VeraCrypt это бесплатное программное обеспечение для шифрования дисков с открытым исходным кодом для Windows, Mac OS X (macOS) и Linux. В случае, если злоумышленник вынуждает вас раскрыть пароль, VeraCrypt обеспечивает правдоподобное отрицание наличия шифрования. В отличие от пофайлового шифрования, VeraCrypt шифрует данные в реальном времени (на лету), автоматически, прозрачно, требует очень мало памяти и не использует временные незашифрованные файлы."/>
<meta name="keywords" content="encryption, security, шифрование, безопасность"/>
<link href="styles.css" rel="stylesheet" type="text/css" />
</head>
<body>

<div>
<a href="Documentation.html"><img src="VeraCrypt128x128.png" alt="VeraCrypt"/></a>
</div>

<div id="menu">
	<ul>
	  <li><a href="Home.html">Начало</a></li>
	  <li><a href="Code.html">Исходный код</a></li>
	  <li><a href="Downloads.html">Загрузить</a></li>
	  <li><a class="active" href="Documentation.html">Документация</a></li>
	  <li><a href="Donation.html">Поддержать разработку</a></li>
	  <li><a href="https://sourceforge.net/p/veracrypt/discussion/" target="_blank">Форум</a></li>
	</ul>
</div>

<div>
<p>
<a href="Documentation.html">Документация</a>
<img src="arrow_right.gif" alt=">>" style="margin-top: 5px">
<a href="Release%20Notes.html">История версий</a>
</p></div>

<div class="wikidoc">
<h1>История версий</h1>

<p>
<strong>Примечание для тех, кто создавал тома с помощью VeraCrypt версии 1.17 или более ранней:</strong><br/>
<span style="color:#ff0000;">Чтобы избежать намёков на то, что ваши тома (не) содержат скрытый том, или если вам необходимо
правдоподобно отрицать наличие шифрования при использовании скрытых томов/ОС, вы должны <em>создать заново</em> как
внешние, так и скрытые тома, включая шифрование системы и скрытую ОС, и удалить существующие тома, созданные версией
VeraCrypt старее, чем 1.18a.</span></li>
</p>

<p><strong style="text-align:left">1.26.26</strong> (29 июня 2025 года):</p>
<ul>
<li><strong>Все ОС:</strong>
    <ul>
        <li>Обновлены иконки: теперь используются упрощённые варианты без надписей.</li>
        <li>Обновлена документация.</li>
        <li>Обновлены переводы.</li>
    </ul>
</li>
<li><strong>Windows:</strong>
    <ul>
        <li>Добавлена поддержка алгоритма Argon2id для хеширования паролей.</li>
		<li>Ускорено монтирование при выборе автодетекции PRF.</li>
        <li>Добавлен параметр командной строки <code>/protectScreen</code> для отключения защиты экрана в портативном режиме (см. документацию).</li>
        <li>Добавлен дополнительный аргумент для параметра <code>/protectMemory</code> — теперь можно отключать защиту памяти в портативном режиме (см. документацию).</li>
        <li>Предоставлен SDK VeraCrypt на C/C++ для создания томов: <a href="https://github.com/veracrypt/VeraCrypt-SDK">https://github.com/veracrypt/VeraCrypt-SDK</a>.</li>
    </ul>
</li>
<li><strong>Linux:</strong>
    <ul>
        <li>Для Ubuntu 25.04 теперь требуется пакет <code>libwxgtk3.2-1t64</code>.</li>
        <li>Файл AppImage теперь можно запускать, если его имя начинается с «veracrypt» (регистр букв не имеет значения).</li>
        <li>Исправлена начальная ширина столбцов в главном интерфейсе.</li>
    </ul>
</li>
<li><strong>macOS:</strong>
    <ul>
        <li>Исправлена начальная ширина столбцов в главном интерфейсе.</li>
    </ul>
</li>
</ul>

<p><strong style="text-align:left">1.26.24</strong> (30 мая 2025 года):</p>
<ul>
<li><strong>Все ОС:</strong>
    <ul>
        <li>Исправлена реализация хеш-функции Whirlpool для платформ с порядком байтов от старшего к младшему (big-endian) (GH #1529).</li>
        <li>В английском интерфейсе команда "Dismount All" переименована в "Unmount All". Новая комбинация клавиш: Alt+U вместо Alt+S.</li>
        <li>Добавлена документация в формате CHM на китайском и русском языках (предоставлена волонтёрами).</li>
        <li>Обновлены переводы.</li>
    </ul>
</li>
<li><strong>Windows:</strong>
    <ul>
        <li>Реализована защита от снятия скриншотов и записи экрана, по умолчанию она включена.
            <ul>
                <li>Эту функцию можно отключить во время установки или в настройках программы (меню "Настройки->Производительность и драйвер").</li>
            </ul>
        </li>
        <li>В MSI-установщик добавлены флажки для управления функциями защиты памяти и экрана.
            <ul>
                <li>Добавлены параметры командной строки <code>DISABLEMEMORYPROTECTION</code> и <code>DISABLESCREENPROTECTION</code> для управления этими функциями в MSI-установщике.</li>
                <li>Оба параметра принимают значения <code>0</code> (включено) или <code>1</code> (отключено).</li>
                <li>Пример использования:<br>
                    <code>msiexec /i VeraCrypt_Setup_x64_1.26.24.msi DISABLESCREENPROTECTION=1 /qn REBOOT=ReallySuppress MSIRESTARTMANAGERCONTROL=Disable ACCEPTLICENSE=YES</code>
                </li>
            </ul>
        </li>
		<li>Исправлены состояния гонки при одновременном запуске нескольких экземпляров veracrypt.exe.</li>
        <li>Библиотека <code>libzip</code> обновлена до версии 1.11.3.</li>
    </ul>
</li>
<li><strong>Linux:</strong>
    <ul>
        <li>Добавлена поддержка формата распространения и использования AppImage.</li>
        <li>Исправлено использование абсолютного пути к команде 'true' при проверке активности sudo-сессии.</li>
        <li>Исправлена ошибка, препятствующая использованию венгерских переводов.</li>
        <li>Улучшены универсальные установочные скрипты (GH #1514).</li>
        <li>Добавлена поддержка /run/media/veracrypt в качестве префикса монтирования по умолчанию, если /media недоступен (GH #1524).</li>
        <li>Удалена зависимость от pcsclite в пакетах .deb и .rpm, так как библиотека теперь обнаруживается и загружается динамически во время выполнения.</li>
    </ul>
</li>
<li><strong>macOS:</strong>
    <ul>
        <li>Исправлено использование абсолютного пути к команде 'true' при проверке активности sudo-сессии.</li>
		<li>Создана символическая ссылка VeraCrypt в <code>/usr/local/bin</code> для использования из командной строки.</li>
        <li>Исправлена ошибка, препятствующая использованию венгерских переводов.</li>
    </ul>
</li>
</ul>

<p><strong style="text-align:left">1.26.20</strong> (3 февраля 2025 года):</p>
<ul>
<li><strong>Все ОС:</strong>
	<ul>
		<li>Ускорено вычисление SHA-256 на платформах ARM64 с помощью инструкций процессора.</li>
		<li>Обновлены переводы.</li>
		<li>Во всём интерфейсе и документации (на английском языке) термин "Dismount" заменён на "Unmount" для соответствия ИТ-стандартам.</li>
	</ul>
</li>
<li><strong>Windows:</strong>
	<ul>
		<li>Устранена регрессия в драйвере, приводившая к постоянному разрешению дефрагментации и вызывавшая другие побочные эффекты.</li>
		<li>Восстановлен предыдущий метод сбора системной энтропии из-за сообщений пользователей о проблемах со стабильностью.</li>
	</ul>
</li>
<li><strong>Linux:</strong>
	<ul>
		<li>Исправлена регрессия в Linux Mint, влияющая на аутентификацию администратора по паролю (GH #1473).
			<ul>
				<li>/usr/bin/true теперь используется вместо /usr/bin/uptime для определения активных sudo-сессий.</li>
				<li>Если в файле sudoers для veracrypt используется правило NOPASSWD с uptime, его следует заменить на /usr/bin/true.</li>
			</ul>
		</li>
	</ul>
</li>
<li><strong>macOS:</strong>
	<ul>
		<li>Исправлена регрессия, из-за которой было невозможно размонтировать тома (GH #1467).</li>
		<li>Исправлена ошибка утверждения wxWidgets 3.2.6, связанная с неопределённым параметром <code>use-dummy-sudo-password</code> (GH #1470).</li>
	</ul>
</li>
</ul>

<p><strong style="text-align:left">1.26.18</strong> (20 января 2025 года):</p>
<ul>
<li><strong>Все ОС:</strong>
	<ul>
		<li>Добавлена поддержка встроенного интерфейса SHA-256 x86 для ускорения PBKDF2-HMAC-SHA256.</li>
		<li>Добавлена поддержка аппаратного шифрования AES на платформах ARM64 (например, Windows ARM64, macOS на компьютерах с процессорами Apple Silicon Mx).</li>
		<li>Обновлены переводы.</li>
</ul>
</li>
<li><strong>Windows:</strong>
	<ul>
		<li>Прекращена поддержка 32-разрядных версий Windows.</li>
		<li>Минимально поддерживаемая версия Windows 10 – обновление от октября 2018 года (версия 1809).</li>
		<li>Уменьшена вероятность взаимоблокировок драйверов при нехватке памяти из-за повторных завершений IRP.</li>
		<li>Исправлено определение EFI на некоторых компьютерах, где не определена переменная BootOrder (предложено @kriegste, GH #360).</li>
		<li>Исправлена ошибка отказа в доступе при обновлении VeraCrypt с помощью EXE-установщика после обновления Windows.</li>
		<li>Исправлены различные проблемы, влияющие на редактор конфигурации шифрования EFI-системы.</li>
		<li>Исправлена регрессия при создании Переносного (Traveler) диска (GH #886).</li>
		<li>Теперь для генерации безопасных случайных данных вместо устаревшего CryptGenRandom применяется BCryptGenRandom.</li>
		<li>Сбор системной энтропии для генерации случайных данных выполняется с помощью современного API вместо устаревших способов.</li>
		<li>LZMA SDK обновлён до версии 24.09.</li>
		<li>Библиотека libzip обновлена до версии 1.11.2.</li>
	</ul>
</li>
<li><strong>Linux:</strong>
	<ul>
		<li>CVE-2024-54187: Добавлены абсолютные пути при выполнении системных двоичных файлов для предотвращения перехвата пути (сотрудничество с SivertPL @__tfr).</li>
		<li>CVE-2025-23021: Запрещено монтирование томов к системным каталогам и пути к ним (сообщил SivertPL @__tfr).</li>
		<li>Исправлена ошибка утверждения во включённой в Ubuntu библиотеке wxWidgets.</li>
		<li>Улучшена логика открытия каталогов: теперь в первую очередь используется xdg-open с добавлением резервных механизмов.</li>
		<li>Перед началом монтирования проверяется, что том существует.</li>
		<li>Исправлено сообщение об ошибке "Слишком длинный Пароль", не расширенное до максимальной длины (GH #1456).</li>
		<li>Упрощена логика обнаружения сеанса sudo.</li>
	</ul>
</li>
<li><strong>macOS:</strong>
	<ul>
		<li>CVE-2024-54187: Добавлены абсолютные пути при выполнении системных двоичных файлов для предотвращения перехвата пути (сотрудничество с SivertPL @__tfr).</li>
		<li>CVE-2025-23021: Запрещено монтирование томов к системным каталогам и пути к ним (сообщил SivertPL @__tfr).</li>
		<li>Захват экрана по умолчанию отключён. Если вам требуется эта функция, используйте ключ --allow-screencapture в командной строке.</li>
		<li>Перед началом монтирования проверяется, что том существует.</li>
		<li>Реализована логика обнаружения сеанса sudo.</li>
	</ul>
</li>
</ul>

<p><strong style="text-align:left">1.26.15</strong> (2 сентября 2024 года):</p>
<ul>
<li><strong>Windows:</strong>
<ul>
	<li>Устранены проблемы с установкой/удалением MSI:
		<ul>
			<li>Исправлена ошибка 1603, возвращаемая автоматической установкой MSI, если указан параметр REBOOT=ReallySuppress и требуется перезагрузка.</li>
			<li>Добавлены отсутствующие файлы документации и языков из пакета MSI.</li>
			<li>Исправлена ошибка, из-за которой MSI не устанавливал новую документацию и языковые файлы при обновлении с установки на основе EXE.</li>
			<li>Исправлена ошибка, из-за которой в некоторых случаях не удалялась установочная папка после удаления MSI.</li>
		</ul>
	</li>
	<li>Исправлена регрессия при расшифровке системы UEFI, из-за которой сохранялся загрузчик.</li>
</ul>
</li>
</ul>

<p><strong style="text-align:left">1.26.14</strong> (25 августа 2024 года):</p>
<ul>
<li><strong>Все ОС:</strong>
<ul>
<li>Обновлены переводы и документация.</li>
<li>Реализован выбор языка в версиях для операционных систем, отличных от Windows.</li>
<li>Обеспечена совместимость кодовой базы с wxWidgets 3.3 в версиях для операционных систем, отличных от Windows.</li>
<li>Реализовано обнаружение томов, затронутых уязвимостью главного ключа XTS, и предупреждение пользователя об этой уязвимости.</li>
<li>Обновлены сообщения об ошибках при монтировании с упоминанием удаления поддержки TrueCrypt и старых алгоритмов.</li>
</ul>
</li>
<li><strong>Windows:</strong>
	<ul>
	<li>Улучшено исправление проблем с безопасным рабочим столом в Windows 11 22H2.
		<ul>
			<li>На безопасном рабочем столе компонент IME теперь отключён, так как известно, что он вызывает проблемы.</li>
		</ul>
	</li>
	<li>VeraCrypt Expander: Исправлено расширение томов на дисках с размером сектора, отличным от 512 байтов (от skl0n6).</li>
	<li>Исправлена запись неправильных дополнительных параметров шифрования системы EFI в реестр.</li>
	<li>Не закрывается программа установки при выходе из процесса VeraCrypt через меню выхода на панели задач.</li>
	<li>Исправлена ошибка форматирования некоторых дисков (например, VHDX), вызванная смещением виртуального раздела, не выровненным по 4K.</li>
	<li>Возврат к абсолютному позиционированию при доступе к дискам в случае сбоя относительного позиционирования.</li>
	<li>Библиотека zlib обновлена до версии 1.3.1.</li>
	</ul>
</li>
<li><strong>Linux:</strong>
	<ul>
	<li>Фокусировка на поле PIM при выборе (#1239).</li>
	<li>Исправлен общий сценарий установки в Konsole в Wayland (#1244).</li>
	<li>Добавлена возможность сборки с использованием wolfCrypt в качестве криптографического бэкенда. По умолчанию отключено. (Разработано командой wolfSSL, GH PR #1227).</li>
	<li>Разрешено запускать GUI в среде только с Wayland (GH #1264).</li>
	<li>CLI: Если PIM уже был указан, то повторно он не запрашивается (GH #1288).</li>
	<li>CLI: Исправлен неверный максимальный размер скрытого тома для файловых контейнеров (GH #1338).</li>
	<li>Повышена безопасность ASLR для универсальных установочных бинарных файлов путём добавления связанного флага для старой версии GCC. (Сообщено @morton-f на SourceForge).</li>
	</ul>
</li>
<li><strong>macOS:</strong>
	<ul>
	<li>Исправлено повреждение значка диска на главном экране программы (GH #1218).</li>
	<li>Исправлена почти нулевая ширина поля ввода PIM и упрощена логика wxTextValidator (GH #1274).</li>
	<li>Использование корректного расположения Disk Utility при проверке файловой системы (GH #1273).</li>
	<li>Добавлена поддержка FUSE-T в качестве альтернативы MacFUSE (GH #1055).</li>
	</ul>
</li>
<li><strong>FreeBSD:</strong>
	<ul>
	<li>Исправлена ошибка, из-за которой не отображались запросы на повышение привилегий (GH #1349).</li>
	<li>Поддержка автоматического обнаружения и монтирования файловых систем ext2/3/4, exFAT, NTFS (GH #1350).</li>
	<li>Использование корректного расположения Disk Utility при проверке файловой системы (GH #1273).</li>
	</ul>
</li>
</ul>

<p><strong style="text-align:left">1.26.7</strong> (1 октября 2023 года):</p>
<ul>
<li><strong>Все операционные системы:</strong>
<ul>
<li>Безопасность: при создании томов добавлена проверка, гарантирующая, что первичный ключ XTS отличается от вторичного.
	<ul>
		<li>Проблема маловероятна благодаря свойствам генератора случайных чисел, но эту проверку необходимо было добавить для предотвращения атак.</li>
		<li>Ссылка: комментарий CCSS,NSA на стр. 3 в этом документе: <a href="https://csrc.nist.gov/csrc/media/Projects/crypto-publication-review-project/documents/initial-comments/sp800-38e-initial-public-comments-2021.pdf">https://csrc.nist.gov/csrc/media/Projects/crypto-publication-review-project/documents/initial-comments/sp800-38e-initial-public-comments-2021.pdf</a></li>
	</ul>
</li>
<li>Удалён режим совместимости с TrueCrypt. Для монтирования или преобразования томов TrueCrypt можно использовать версию 1.25.9.</li>
<li>Полностью удалены алгоритмы RIPEMD160 и GOST89. Тома, использующие любой из них, больше нельзя монтировать в VeraCrypt.</li>
<li>Добавлена поддержка нового PRF-алгоритма (псевдослучайной функции) BLAKE2s для шифрования как системных дисков, так и обычных томов.</li>
<li>В качестве ключевых файлов для несистемных томов можно использовать банковские карты стандарта EMV.
<ul>
<li>Нет необходимости настраивать отдельный модуль PKCS#11.</li>
<li>Не требуется PIN-код карты.</li>
<li>Для безопасного генерирования содержимого ключевого файла используются уникальные закодированные данные, имеющиеся на банковской карте.</li>
<li>Поддерживаются все банковские карты, соответствующие стандарту EMV.</li>
<li>Поддержку EMV нужно включить в настройках программы (меню <em>Настройки->Токены безопасности</em>).</li>
<li>Разработано командой студентов из <a href="https://www.insa-rennes.fr">Institut national des sciences appliquées de Rennes</a> (Национального института прикладных наук Ренна).</li>
<li>Репозитарий проекта на Github: <a href="https://github.com/veracrypt-EMV-INSA-Rennes-4INFO/VeraCrypt_EMV">https://github.com/veracrypt-EMV-INSA-Rennes-4INFO/VeraCrypt_EMV</a></li>
</ul>
</li>
<li>Полностью удалены алгоритмы RIPEMD160 и GOST89 (ГОСТ 28147-89). Устаревшие тома, использующие любой из них, больше не могут быть смонтированы в VeraCrypt.</li>
<li>Добавлена поддержка корсиканского языка. Обновлено несколько переводов. </li>
<li>Обновлена документация.</li>
</ul>
</li>
<li><strong>Windows:</strong>
<ul>
<li>Официально минимальная поддерживаемая версия теперь – <strong>Windows 10</strong>. VeraCrypt всё ещё может работать в Windows 7 и Windows 8/8.1, но активные тесты на этих платформах не проводятся.</li>
<li>Загрузчик EFI:
<ul>
<li>Исправлена ошибка в обработке значения PasswordTimeout, из-за которой оно ограничивалось 255 секундами.</li>
<li>Rescue Disk: если на диске отсутствует исходный загрузчик Windows, то загрузка выполняется из его встроенной резервной копии.</li>
<li>Добавлена хеш-функция BLAKE2s, удалены хеш-функция RIPEMD160 и алгоритм шифрования GOST89.</li>
</ul>
</li>
<li>По умолчанию включена защита памяти. Чтобы отключить/включить защиту, выберите меню "Настройки > Производительность и драйвер".
<ul>
	<li>Защита памяти блокирует чтение памяти VeraCrypt процессами без прав администратора.</li>
	<li>Защита может блокировать чтение пользовательского интерфейса VeraCrypt программами чтения с экрана (поддержка специальных возможностей), в этом случае её следует отключить.</li>
	<li>Защиту можно отключить, установив в реестре значение "VeraCryptEnableMemoryProtection" в 0 в ключе "HKEY_LOCAL_MACHINE\SYSTEM\CurrentControlSet\Services\veracrypt".</li>
</ul>
</li>
<li>Добавлена защита от подстановки кода в память VeraCrypt другими процессами.</li>
<li>Незначительные улучшения в реализации шифрования оперативной памяти.</li>
<li>Исправлены проблемы с безопасным рабочим столом в Windows 11 22H2.</li>
<li>Реализована поддержка монтирования частично зашифрованных системных разделов.</li>
<li>Исправлено ложное обнаружение вставки нового устройства, когда включена опция очистки ключей шифрования (только в случае системного шифрования).</li>
<li>Улучшена реализация быстрого создания файловых контейнеров, использующих UAC для запроса необходимых привилегий, если они ещё не предоставлены.</li>
<li>При создании файловых контейнеров разрешён выбор быстрого создания в интерфейсе мастера форматирования.</li>
<li>Исправлены проблемы с форматированием при создании тома на некоторых машинах.</li>
<li>Исправлена проблема с зависанием при быстром форматировании больших файловых контейнеров.</li>
<li>Добавлено выпадающее меню к кнопке <em>Смонтировать</em>, позволяющее выполнять монтирование без кэша.</li>
<li>Решена проблема нарастающего замедления шифрования на месте на больших томах.</li>
<li>Расширитель томов (Expander) сначала проверяет наличие файла, прежде чем продолжить.</li>
<li>Возможен выбор единицы размера (байты/КиБ/МиБ/ГиБ) для генерируемых ключевых файлов.</li>
<li>При создании томов отображается полный список поддерживаемых размеров кластеров для файловых систем NTFS, ReFS и exFAT.</li>
<li>Добавлена поддержка перетаскивания файлов и ключевых файлов в Расширителе томов (Expander).</li>
<li>Реализован перевод интерфейса Расширителя томов (Expander).</li>
<li>Заменён устаревший API выбора файла/папки на современный интерфейс IFileDialog для лучшей совместимости с Windows 11.</li>
<li>Улучшения в безопасной загрузке библиотек зависимостей, включая задержку загрузки.</li>
<li>Удалена рекомендация по расширениям ключевых файлов, а в документации указаны риски применения сторонних расширений файлов.</li>
<li>Добавлена поддержка дополнительных языков в программе установки.</li>
<li>Обновлена библиотека LZMA до версии 23.01.</li>
<li>Обновлены библиотеки libzip до версии 1.10.1 и zlib до версии 1.3.</li>
</ul>
</li>
<li><strong>Linux:</strong>
<ul>
<li>Исправлена ошибка в генераторе случайных чисел в Linux при использовании с BLAKE2s, которая вызывала сбой самопроверки.</li>
<li>Изменён генератор случайных чисел в Linux, чтобы он точно соответствовал официальной документации и реализации в Windows.</li> 
<li>Исправлены проблемы совместимости с Ubuntu 23.04.</li>
<li>Исправлены сообщения об утверждениях, отображаемые при использовании wxWidgets 3.1.6 и новее.</li>
<li>Исправлены проблемы с запуском fsck в Linux.</li>
<li>Исправлено игнорирование запросов на повышение привилегий.</li>
<li>Исправлен неправильный размер скрытого тома при выборе опции использования всего свободного места.</li>
<li>Исправлена при создании скрытого тома на диске с помощью CLI, вызванная неправильным определением максимального размера.</li>
<li>Исправлены различные проблемы при работе в текстовом режиме:
<ul>
<li>Запрет выбора файловых систем exFAT/BTRFS, если они отсутствуют или несовместимы с созданным томом.</li>
<li>Исправлено неправильное сообщение о размонтировании, отображаемое при монтировании тома.</li>
<li>Скрытие PIM при вводе и повторный запрос PIM при вводе неправильного значения.</li>
<li>Исправлена ошибка печати при проверке свободного места во время создания тома в несуществующем пути.</li>
</ul>
</li>
<li>Для статических сборок (например только консольной версии) используется wxWidgets 3.2.2.1.</li>
<li>Исправлена совместимость универсальных установщиков со старыми дистрибутивами Linux.</li>
</ul>
</li>
</ul>

<p><strong style="text-align:left">1.25.9</strong> (19 февраля 2022 года)</p>
<ul>
<li><strong>Все операционные системы:</strong>
<ul>
<li>Обновлены переводы (китайский, голландский, французский, немецкий, турецкий).</li>
</ul>
</li>
<li><strong>Windows:</strong>
<ul>
<li>MSI-установщик сделан совместимым с системным шифрованием.</li>
<li>Для MSI-установки требуется Windows 7 или более новая версия Windows.</li>
<li>Исправлена ошибка при создании Переносного диска, если программа VeraCrypt установлена с помощью MSI.</li>
<li>Пароль внешнего тома не кэшируется при монтировании с включённой защитой скрытого тома, если был указан неправильный пароль скрытого тома.</li> 
<li>Благодаря использованию сжатия LZMA вместо DEFLATE размер EXE-установщиков уменьшен почти на 50%.</li>
<li>Исправлена ошибка, из-за которой в некоторых особых конфигурациях Windows не работал двойной щелчок по смонтированному диску в интерфейсе VeraCrypt.</li>
<li>Добавлен раздел реестра для исправления ошибки BSOD при выключении/перезагрузке на некоторых компьютерах, использующих шифрование системы.
<ul>
<li>В ветви <code>HKEY_LOCAL_MACHINE\SYSTEM\CurrentControlSet\Services\veracrypt</code> создайте параметр REG_DWORD с именем <code>VeraCryptEraseKeysShutdown</code>.</li>
<li>Если у этого параметра установить значение 0, то будет отключено удаление ключей шифрования системы, что является причиной появления "синего экрана" BSOD во время выключения на некоторых компьютерах.</li>
</ul>
</li>
</ul>
</li>
<li><strong>Linux:</strong>
<ul>
<li>Исправлено некорректное отображение настроек скрытого тома при включении защиты скрытого тома в окне параметров монтирования.</li>
<li>Исправлена ошибка перезаписи /usr/sbin общего установщика Linux, если это символическая ссылка.</li>
<li>Устранён сбой при сборке с определёнными _GLIBCXX_ASSERTIONS.</li>
<li>Разрешена сборка из исходного кода без поддержки AES-NI.</li>
</ul>
</li>
<li><strong>Mac OS X:</strong>
<ul>
<li>Исправлено некорректное отображение настроек скрытого тома при включении защиты скрытого тома в окне параметров монтирования.</li>
</ul>
</li>
</ul>
<p><strong style="text-align:left">1.25.7</strong> (7 января 2022 года)</p>
<ul>
<li><strong>Все операционные системы:</strong>
<ul>
<li>Обновлены переводы.</li>
</ul>
</li>
<li><strong>Windows:</strong>
<ul>
<li>Возвращена поддержка Windows Vista, Windows 7 и Windows 8/8.1.
<ul>
<li>Поддержка Windows 7 требует, чтобы было установлено либо обновление KB3033929, либо KB4474419.</li>
<li>Поддержка Windows Vista требует, чтобы было установлено либо обновление KB4039648, либо KB4474419.</li>
</ul>
</li>
<li>Только при MSI-установке: исправлена ошибка, при которой двойной щелчок по файлу-контейнеру .hc вставлял %1 вместо имени тома в поле пути.</li>
<li>Для опытных пользователей добавлены параметры в реестре, управляющие внутренней очередью шифрования драйвера, что позволяет настроить производительность SSD-дисков и повысить стабильность при большой нагрузке.
<ul>
<li>Параметры в ключе реестра HKEY_LOCAL_MACHINE\SYSTEM\CurrentControlSet\Services\veracrypt:
<ul>
<li>VeraCryptEncryptionFragmentSize (REG_DWORD): размер фрагмента зашифрованных данных в кибибайтах (КиБ). По умолчанию – 256. Максимум – 2048.</li>
<li>VeraCryptEncryptionIoRequestCount (REG_DWORD): максимальное количество параллельных запросов ввода/вывода. По умолчанию – 16. Максимум – 8192.</li>
<li>VeraCryptEncryptionItemCount (REG_DWORD): максимальное количество элементов очереди шифрования, обрабатываемых параллельно. Значение по умолчанию, как и максимальное, равно половине значения VeraCryptEncryptionIoRequestCount.</li>
</ul>
</li>
<li>Триплет (Fragmentsize=512, IoRequestCount=128, ItemCount=64) – пример параметров, которые повышают скорость последовательного чтения в некоторых системах с SSD NVMe.</li>
<li>Исправлено усечение текста в установщике для некоторых языков.</li>
</ul>
</li>
</ul>
<li><strong>Mac OS X:</strong>
<ul>
<li>Исправлены файлы ресурсов внутри пакета приложений VeraCrypt (например, HTML-документация, языковые XML-файлы), доступные для записи во всем мире (сообщил Niall O'Reilly).</li>
</ul>
</li>
</ul>
<p><strong style="text-align:left">1.25.4</strong> (3 декабря 2021 года)</p>
<ul>
<li><strong>Все операционные системы:</strong>
<ul>
<li>Ускорен Streebog.</li>
<li>Обновлены переводы.</li>
</ul>
</li>
<li><strong>Windows:</strong>
<ul>
<li>Добавлена поддержка Windows на ARM64 (например Microsoft Surface Pro X), но шифрование системы пока не поддерживается.</li>
<li>Добавлен MSI-установщик для развёртывания в автоматическом режиме (в командной строке msiexec должно быть установлено ACCEPTLICENSE=YES).
<ul>
<li>На данный момент MSI-установщик нельзя использовать, если системный раздел зашифрован с помощью VeraCrypt.</li>
<li>Для MSI-установщика требуется Windows 10 или новее.</li>
</ul>
</li>
<li>Windows Vista, Windows 7, Windows 8 и Windows 8.1 больше не поддерживаются из-за нового требования к подписи кода драйвера.</li>
<li>Ускорено монтирование, если выбрано автоматическое определение PRF.</li>
<li>Исправлено потенциальное повреждение памяти в драйвере, вызванное переполнением целых чисел в IOCTL_STORAGE_MANAGE_DATA_SET_ATTRIBUTES (сообщил Ilja van Sprundel).</li>
<li>Замена небезопасных функций времени выполнения wcscpy/wcscat/strcpy безопасными эквивалентами.</li>
<li>Изменения в EFI-загрузчике:
<ul>
<li>Устранена возникавшая в некоторых случаях утечка памяти, вызванная неправильной проверкой указателя для вызова MEM_FREE.</li>
<li>При остановке системы из-за фатальной ошибки происходит очистка переменной bootParams, которая может содержать конфиденциальную информацию.</li>
<li>Добавлена опция "KeyboardInputDelay" в DcsProp для управления минимальной задержкой между двумя нажатиями клавиш.</li>
</ul></li>
<li>Попытка обойти проблемы обновлений функций Windows с системным шифрованием путём исправления загрузчика и SetupConfig.ini при возобновлении работы системы или при открытии/разблокировке сеанса.</li>
<li>Исправлена ошибка при загрузке локальной HTML-документации, если приложение запущено с правами администратора.</li>
<li>Исправлена блокировка при отображении окна пароля на защищённом рабочем столе и попытке доступа к файлам ключей токенов, защищённых ПИН-кодом.</li>
<li>Исправлена ошибка при запуске генератора файлов ключей в режиме защищённого рабочего стола.</li>
<li>Блокировка Windows от изменения размера системного раздела, если он зашифрован.</li>
<li>Добавлено сочетание клавиш для опции "Режим TrueCrypt" в окне монтирования.</li>

</ul>
</li>
<li><strong>Mac OS X:</strong>
<ul>
<li>Встроенная поддержка Apple Silicon M1.</li>
<li>Операционные системы Mac OS X 10.7 Lion и Mac OS X 10.8 Mountain Lion больше официально не поддерживаются.</li>
<li>Добавлена поддержка многоязычности с помощью установленных XML-файлов. Язык автоматически определяется по переменной среды "LANG".</li>
<li>Добавлены ключ командной строки <code>--size=max</code> и опция в графическом интерфейсе, позволяющие предоставить файлу-контейнеру всё доступное свободное пространство на диске, на котором он создан.</li>
<li>Возвращается ошибка, если в командной строке в ключе <code>--filesystem</code> указана неизвестная файловая система. Ранее создание файловой системы молча пропускалось.</li>
</ul>
</li>
<li><strong>Linux:</strong>
<ul>
<li>Добавлена поддержка многоязычности с помощью установленных XML-файлов. Язык автоматически определяется по переменной среды "LANG".</li>
<li>Совместимость с pam_tmpdir.</li>
<li>Отображается значок в области уведомлений в Ubuntu 18.04 и новее (помощь от https://unit193.net/).</li>
<li>Добавлены ключ командной строки <code>--size=max</code> и опция в графическом интерфейсе, позволяющие предоставить файлу-контейнеру всё доступное свободное пространство на диске, на котором он создан.</li>
<li>Возвращается ошибка, если в командной строке в ключе <code>--filesystem</code> указана неизвестная файловая система. Ранее создание файловой системы молча пропускалось.</li>
</ul>
</li>
<li><strong>FreeBSD:</strong>
<ul>
<li>Системные устройства работают под FreeBSD.</li>
<li>Добавлены ключ командной строки <code>--size=max</code> и опция в графическом интерфейсе, позволяющие предоставить файлу-контейнеру всё доступное свободное пространство на диске, на котором он создан.</li>
<li>Возвращается ошибка, если в командной строке в ключе <code>--filesystem</code> указана неизвестная файловая система. Ранее создание файловой системы молча пропускалось.</li>
</ul>
</li>
<li><strong>OpenBSD:</strong>
<ul>
<li>Добавлена базовая поддержка OpenBSD.</li>
<li>Добавлены ключ командной строки <code>--size=max</code> и опция в графическом интерфейсе, позволяющие предоставить файлу-контейнеру всё доступное свободное пространство на диске, на котором он создан.</li>
<li>Возвращается ошибка, если в командной строке в ключе <code>--filesystem</code> указана неизвестная файловая система. Ранее создание файловой системы молча пропускалось.</li>
</ul>
</li>
</ul>

<p><strong style="text-align:left">1.24-Update8</strong> (28 ноября 2020 года)</p>
<ul>
<li><strong>Mac OS X:</strong>
<ul>
<li>Исправлены проблемы совместимости с macOS Big Sur, особенно на Apple Silicon M1 с macFUSE 4.0.x.</li>
</ul>
</li>
</ul>

<p><strong style="text-align:left">1.24-Update7</strong> (7 августа 2020 года)</p>
<ul>
<li><strong>Все операционные системы:</strong>
<ul>
<li>Не разрешается, чтобы у скрытого тома были те же пароль, PIM и ключевые файлы, что и у внешнего тома.</li>
<li>Исправлен случайный сбой в 32-разрядных сборках при использовании Streebog.</li>
<li>Включён режим FIPS в генераторе случайных чисел JitterEntropy.</li>
<li>Обновлено Руководство для начинающих в документации: для имени файла-контейнера вместо "My Volume" используется "MyVolume.hc", чтобы избежать путаницы в отношении природы файла.</li>
<li>Незначительная очистка кода.</li>
</ul>
</li>
<li><strong>Windows:</strong>
<ul>
<li>Исправлен вывод неправильных результатов в тесте алгоритмов шифрования при включённом шифровании ОЗУ.</li>
<li>Устранена проблема, когда использовалось шифрование ОЗУ, был выбран AES, а AES-NI не поддерживается процессором, что приводило к тому, что свободное пространство вновь созданных томов не заполнялось случайными данными, даже если не было выбрано быстрое форматирование.</li>
<li>Исправлен пользовательский интерфейс для блокировки TRIM в системном шифровании, не работающей в режиме загрузки MBR.</li>
<li>Поддержка перетаскивания пароля из внешних приложений (например, из KeePass) в поля пользовательского интерфейса пароля, что более безопасно, чем использование буфера обмена.</li>
<li>Добавлена совместимость с моделью электропитания Windows 10 Modern Standby и Windows 8.1 Connected Standby. Это делает более надёжным обнаружение входа в режим энергосбережения.</li>
<li>Не отображается окно ожидания, если для "VeraCrypt Format" указан ключ <code>/silent</code> при создании файла-контейнера с использованием ключа <code>/create</code> и файловой системы, отличной от FAT.</li>
<li>Для форматирования томов используется родная программа форматирования Windows, поскольку она более надёжна и в случае возникновения проблемы использует только функцию FormatEx из fmifs.dll.</li>
<li>Программа не использует API для поддержки групп процессоров, если в системе есть только одна группа процессоров. Это может исправить проблему замедления на некоторых ПК с процессорами AMD.</li>
<li>Запрет шифрования системного диска, если он уже зашифрован утилитой BitLocker.</li>
<li>Реализовано обнаружение гибернации и функции быстрого запуска и их отключение, если активировано шифрование ОЗУ.</li>
<li>Предупреждение о функции быстрого запуска, если она включена при установке/обновлении VeraCrypt, при запуске системного шифрования или при создании тома, и предложение её отключить.</li>
<li>Добавлены опции в графическом интерфейсе, управляющие поведением автоматического исправления загрузчика при использовании шифрования системы.</li>
<li>Запрещено вводить путь к папке для файла-контейнера, создаваемого в мастере форматирования.</li>
<li>Программа не пытается использовать исправление для CVE-2019-19501, если оболочка Windows была изменена или не запущена, так как нет надёжного способа исправить это в такой нестандартной конфигурации.</li>
<li>MBR-загрузчик: исправлен неправильный размер сжатых данных, передаваемых декомпрессору в загрузочном секторе.</li>
<li>Добавлено предупреждение, когда введённый пароль достигает максимальной длины во время работы мастера шифрования системы.</li>
<li>Исправлено неправильное сообщение об ошибке, когда кодировка UTF-8 вводимого пароля превышает максимально поддерживаемую длину.</li>
<li>Исправлен сбой при использовании переносимого 32-разрядного файла "VeraCrypt Format.exe" для создания скрытого тома на 64-разрядной машине, где VeraCrypt уже установлен.</li>
<li>Библиотека libzip обновлена до версии 1.7.3.</li>
<li>Обновлены переводы.</li>
</ul>
</li>
<li><strong>Linux/Mac OS X:</strong>
<ul>
<li>Принудительно считываются не менее 32 байтов из /dev/random, прежде чем позволить корректно завершиться ошибкой.</li>
<li>Для внешнего тома можно выбирать файловую систему, отличную от FAT, но отображается предупреждение о рисках такого выбора. В этом случае реализована оценка максимально возможного размера скрытого тома.</li>
<li>Явное стирание чувствительной памяти вместо того, чтобы полагаться на то, что компилятор не оптимизирует вызовы метода Memory::Erase.</li>
<li>Добавлена поддержка файловой системы Btrfs при создании томов (только Linux).</li>
<li>Обновлён wxWidgets для статических сборок до версии 3.0.5.</li>
</ul>
</li>
</ul>

<p><strong style="text-align:left">1.24-Update6 </strong>(10 марта 2020 года)</p>
<ul>
<li><strong>Windows:</strong>
<ul>
<li>Исправлено усечение текста метки PIM в окне пароля.</li>
<li>Исправлена ошибка, из-за которой программа установки использовала неправильный язык, если пользователь выбрал язык, отличный от английского, а затем выбрал английский, прежде чем нажать <i>ОК</i> в окне выбора языка.</li>
</ul>
</li>
</ul>

<p><strong style="text-align:left">1.24-Update5 </strong>(9 марта 2020 года)</p>
<ul>
<li><strong>Windows:</strong>
<ul>
<li>Оптимизирована производительность для ЦП с более чем 64 логическими процессорами (помог Sachin Keswani из AMD).</li>
<li>Поддержка указания ключевых файлов (как в токенах, так и в файловой системе) при создании файловых контейнеров с помощью командной строки (ключи /keyfile, /tokenlib и /tokenpin поддерживаются в VeraCrypt Format).</li>
<li>Исправлена утечка пути и имени файлов ключей после завершения процесса VeraCrypt.</li>
<li>В VeraCrypt Format добавлен ключ <code>/secureDesktop</code> для командной строки.</li>
<li>Библиотека libzip обновлена до версии 1.6.1.</li>
<li>Незначительные исправления пользовательского интерфейса.</li>
</ul>
</li>
</ul>

<p><strong style="text-align:left">1.24-Update4 </strong>(23 января 2020 года)</p>
<ul>
<li><strong>Windows:</strong>
<ul>
<li>Исправлена регрессия в VeraCryptExpander.exe и VeraCrypt Format.exe при включённом шифровании ОЗУ, которая приводила к повреждению заголовков томов.</li>
<li>Исправлена ошибка средств чтения с экрана (поддержка специальных возможностей) для чтения пользовательского интерфейса – отключена недавно введённая защита памяти по умолчанию и добавлен ключ <code>/protectMemory</code> в командной строке, позволяющий её включить, если это потребуется.</li>
<li>Исправлены побочные эффекты, связанные с исправлением для CVE-2019-19501, из-за которого не открывались ссылки в графическом интерфейсе.</li>
<li>Добавлен ключ <code>/signalExit</code> для поддержки уведомления Windows-команды <a href="https://docs.microsoft.com/en-us/windows-server/administration/windows-commands/waitfor" target="_blank">WAITFOR</a> при завершении работы VeraCrypt.exe, если в командной строке был указан ключ <code>/q</code> (см. документацию).</li>
<li>Не отображаются примеры монтирования/демонтирования в окне справки по командной строке у VeraCrypt Format.exe и VeraCryptExpander.exe.</li>
<li>Обновления документации и переводов.</li>
</ul>
</li>
<li><strong>Linux:</strong>
<ul>
<li>Исправлена регрессия, ограничивающая размер, доступный для скрытых томов, созданных на диске или разделе.</li>
</ul>
</li>
<li><strong>Mac OS X:</strong>
<ul>
<li>Исправлена регрессия, ограничивающая размер, доступный для скрытых томов, созданных на диске или разделе.</li>
</ul>
</li>
</ul>

<p><strong style="text-align:left">1.24-Update3 </strong>(21 декабря 2019 года)</p>
<ul>
<li><strong>Linux:</strong>
<ul>
<li>В сборке для консоли удалена зависимость от GTK, которая нежелательна на серверах без монитора, мыши и клавиатуры.</li>
</ul>
</li>
</ul>

<p><strong style="text-align:left">1.24-Update2 </strong>(16 декабря 2019 года)</p>
<ul>
<li><strong>Все операционные системы:</strong>
<ul>
<li>Ключ AES удаляется из стековой памяти при использовании неоптимизированной реализации. Не относится к официальной сборке VeraCrypt (сообщил и исправил Hanno Böck).</li>
<li>RNG-библиотека Jitterentropy обновлена до версии 2.2.0.</li>
<li>Используется согласованное с IEEE 1541 именование байтовых размеров (КиБ, МиБ, ГиБ, ТиБ, ПиБ).</li>
<li>Различные усовершенствования документации.</li>
</ul>
</li>
<li><strong>Windows:</strong>
<ul>
<li>Исправлена возможная уязвимость локального повышения привилегий при работе расширителя томов VeraCrypt Expander (CVE-2019-19501).</li>
<li>MBR-загрузчик:
<ul>
<li>Обходной путь проблемы с SSD-накопителями, не позволяющими выполнять операции записи в режиме BIOS с буферами менее 4096 байт.</li>
<li>MBR не восстанавливается до значения VeraCrypt, если оно поступает из загрузчика, отличного от нашего или от Microsoft.</li>
</ul>
</li>
<li>EFI-загрузчик:
<ul>
<li>Исправлена ошибка, из-за которой не работало "Actionfailed", и добавлено "ActionCancelled", чтобы настроить обработку нажатия пользователем клавиши Esc при вводе пароля.</li>
<li>Исправлена ошибка, когда при нажатии F5 отображался предыдущий пароль после неудачной попытки аутентификации. Теперь гарантируется, что из памяти удаляется даже неверный пароль.</li>
</ul>
</li>
<li>Исправлена проблема совместимости с загрузкой нескольких ОС путём установки VeraCrypt в качестве первого загрузчика системы, если текущий первый загрузчик – это загрузчик Windows.</li>
<li>Добавлены новые флаги реестра для SystemFavoritesService, управляющие обновлением загрузочного меню EFI BIOS при завершении работы.</li>
<li>Разрешено монтирование системного зашифрованного диска в WindowsPE, даже если не удаётся изменить раскладку клавиатуры (сообщил и исправил Sven Strickroth).</li>
<li>Усовершенствован механизм сохранения временн<i>ы</i>х меток файлов, особенно для ключевых файлов.</li>
<li>Исправлена ошибка, из-за которой инструкция RDRAND не обнаруживалась на процессорах AMD.</li>
<li>Обнаруживается неисправность RDRAND (например, в AMD Ryzen), чтобы избежать его использования, если он включён пользователем.</li>
<li>При изменении через графический интерфейс в конец файла DcsProp не записывается дополнительный байт 0x00.</li>
<li>Уменьшено использование памяти обработчиком IOCTL_DISK_VERIFY, используемым при проверке дисков в Windows.</li>
<li>Добавлен ключ <code>/FastCreateFile</code> для <code>VeraCrypt Format.exe</code>, ускоряющий создание больших файлов-контейнеров, если выбрано быстрое форматирование.</li>
<li>Исправлена опция для пропуска проверки Диска восстановления, не отражающая значение ключа <code>/noisocheck</code>, указанного в командной строке VeraCrypt Format.</li>
<li>При монтировании файла-контейнера с расширением .tc, в окне пароля включается опция <i>Режим TrueCrypt</i>.</li>
<li>Обновлены языковые XML-файлы.</li>
</ul>
</li>
<li><strong>Linux:</strong>
<ul>
<li>Исправлена регрессия, из-за которой в некоторых случаях слишком много раз запрашивался пароль администратора.</li>
<li>Устранено переполнение буфера в функции Process::Execute (сообщил и исправил Hanno Böck).</li>
<li>Добавлена проверка, что пароль удалён в случае внутренней ошибки при монтировании тома (сообщил и исправил Hanno Böck).</li>
<li>Исправлена проблема с паролями, использующими юникодные символы, которые не распознаются в текстовом режиме.</li>
<li>Устранён сбой при запуске бинарного файла VeraCrypt, созданного для консольного режима, на компьютерах без монитора, клавиатуры и мыши.</li>
<li>Добавлен ключ для принудительного использования устаревшей максимальной длины пароля (64 байта UTF-8).</li>
<li>Добавлен ключ командной строки (<code>--use-dummy-sudo-password</code>) для принудительного использования старого поведения sudo при отправке фиктивного пароля.</li>
<li>При удалении сообщение об ошибке выводится в STDERR вместо STDOUT для лучшей совместимости с менеджерами пакетов.</li>
<li>Больше подробностей о несоответствии размера сектора при монтировании дисков.</li>
<li>Ускорение SHA256 в 64-разрядном режиме с помощью ассемблерного кода.</li>
</ul>
</li>
<li><strong>Mac OS X:</strong>
<ul>
<li>Добавлен ключ для принудительного использования устаревшей максимальной длины пароля (64 байта UTF-8).</li>
<li>Устранено переполнение буфера в функции Process::Execute (сообщил и исправил Hanno Böck).</li>
<li>Исправлена проблема с паролями, использующими юникодные символы, которые не распознаются в текстовом режиме.</li>
<li>Больше подробностей о несоответствии размера сектора при монтировании дисков.</li>
<li>Ускорение SHA256 в 64-разрядном режиме с помощью ассемблерного кода.</li>
<li>Ссылка на последнюю версию wxWidgets 3.1.3.</li>
</ul>
</li>
</ul>


<p><strong style="text-align:left">1.24-Hotfix1 </strong>(27 октября 2019 года)</p>
<ul>
<li><strong>Windows:</strong>
<ul>
<li>Исправлена регрессия версии 1.24, из-за которой избранное не монтировалось при загрузке системы, если VeraCrypt был только что установлен.</li>
<li>Исправлена ошибка шифрования системы, если текущее имя пользователя Windows содержало юникодный символ, отличный от ASCII.</li>
<li>VeraCrypt Expander получил возможность возобновлять расширение томов, предыдущее расширение которых было прервано до его завершения.</li>
<li>Добавлена опция <i>Quick Expand</i> (Быстрое расширение) в VeraCrypt Expander, чтобы ускорить расширение больших файлов-контейнеров.</li>
<li>Добавлено несколько проверок надёжности и корректности для шифрования системы, чтобы лучше справляться в ряде крайних случаев.</li>
<li>Небольшие изменения пользовательского интерфейса и документации.</li>
</ul>
</li>
<li><strong>Linux:</strong>
<ul>
<li>Обход ошибки gcc 4.4.7 в CentOS 6, которая приводила к сбою программы VeraCrypt, собранной в CentOS 6, при использовании хеша Whirlpool.</li>
<li>Исправлена ошибка "попытка ввода неверного пароля", записанная в /var/log/auth.log при монтировании томов.</li>
<li>Исправлена ошибка, из-за которой при перетаскивании файла в пользовательском интерфейсе не отображался его правильный путь, особенно в GTK-3.</li>
<li>Добавлена отсутствующая реализация JitterEntropy.</li>
</ul>
</li>
<li><strong>Mac OS X:</strong>
<ul>
<li>Исправлена ошибка, из-за которой некоторые устройства и разделы не отображались в окне выбора устройства в OS X 10.13 и новее.</li>
<li>Исправлена навигация с клавиатуры между полями пароля на странице <i>Пароль тома</i> мастера создания томов.</li>
<li>Добавлена отсутствующая реализация JitterEntropy.</li>
<li>Поддержка файловой системы APFS для создания томов.</li>
<li>Поддержка тёмного режима.</li>
</ul>
</li>
</ul>


<p><strong style="text-align:left">1.24 </strong>(6 октября 2019 года)</p>
<ul>
<li><strong>Все операционные системы:</strong>
<ul>
<li>Для несистемных томов максимальная длина пароля увеличена до 128 байт в кодировке UTF-8.</li>
<ul>
<li>По соображениям совместимости добавлена возможность использовать устаревшую максимальную длину пароля (64 символа) вместо новой.</li>
</ul>
<li>Используется аппаратный генератор случайных чисел, основанный на джиттере синхронизации ЦП "Jitterentropy" (автор Stephan Mueller), как хорошая альтернатива процессорного RDRAND (<a href="http://www.chronox.de/jent.html" target="_blank">http://www.chronox.de/jent.html</a>)</li>
<li>Оптимизация скорости режима XTS на 64-разрядных машинах с использованием SSE2 (до 10% быстрее).</li>
<li>Исправлено обнаружение функций AVX2/BMI2 в процессоре. Добавлено обнаружение функций RDRAND/RDSEED. Процессор Hygon определяется как AMD.</li>
</ul>
</li>
<li><strong>Windows:</strong>
<ul>
<li>Реализовано шифрование ключей и паролей в ОЗУ с использованием шифра ChaCha12, некриптографического быстрого хеширования t1ha и CSPRNG на основе ChaCha20.</li>
<ul>
<li>Доступно только на 64-разрядных машинах.</li>
<li>По умолчанию отключено. Включается опцией в графическом интерфейсе.</li>
<li>На современных процессорах накладные расходы – менее 10%.</li>
<li>Побочный эффект: режим гибернации Windows невозможен, если также используется системное шифрование VeraCrypt.</li>
</ul>
<li>Память приложений VeraCrypt недоступна для пользователей без прав администратора, благодаря чему смягчаются некоторые атаки на память (на основе реализации KeePassXC).</li>
<li>Новые функции безопасности:</li>
<ul>
<li>Системные ключи шифрования стираются из памяти при выключении/перезагрузки, чтобы помочь смягчить некоторые атаки с "холодной" загрузкой.</li>
<li>Добавлена опция, стирающая все ключи шифрования из памяти при подключении нового устройства к системе, когда используется системное шифрование.</li>
<li>Добавлена новая точка входа драйвера, которая может быть вызвана приложениями для удаления ключей шифрования из памяти в случае чрезвычайной ситуации.</li>
</ul>
<li>Загрузчик MBR: динамическое определение сегмента памяти загрузчика вместо жёстко заданных значений (предложено neos6464).</li>
<li>Загрузчик MBR: обходной путь для проблемы, влияющей на создание скрытой ОС на некоторых SSD-накопителях.</li>
<li>Устранена проблема, связанная с тем, что Центр обновления Windows повреждал UEFI-загрузчик VeraCrypt.</li>
<li>Несколько улучшений и исправлений для загрузчика EFI:</li>
<ul>
<li>Реализован механизм истечения времени ожидания для ввода пароля. Ожидание по умолчанию – 3 минуты, действие по умолчанию – выключение.</li>
<li>Реализованы новые действия "shutdown" и "reboot" ("выключение" и "перезагрузка") для файла конфигурации EFI DcsProp.</li>
<li>Улучшена реализация Диска восстановления для восстановления загрузчика VeraCrypt.</li>
<li>Исправлена ошибка, когда нажатие Esc при запросе пароля во время пре-теста не запускало Windows.</li>
<li>На Диске восстановления добавлен пункт меню, позволяющий запускать исходный загрузчик Windows.</li>
<li>Устранена проблема, из-за которой хеш Streebog нельзя было выбрать вручную во время предзагрузочной аутентификации.</li>
<li>Если на Диске восстановления нет папки <code>VeraCrypt</code>, компьютер загружается непосредственно из загрузчика на жёстком диске.</li>
<ul>
<li>Это позволяет легко создать загрузочный диск для VeraCrypt из Диска восстановления, просто удалив/переименовав его папку <code>VeraCrypt</code>.</li>
</ul>
</ul>
<li>Добавлена опция (по умолчанию отключена) для использования CPU RDRAND или RDSEED в качестве дополнительного источника энтропии для генератора случайных чисел VeraCrypt, если это возможно.</li>
<li>Добавлена опция монтирования (как в графическом интерфейсе, так и в командной строке), позволяющая монтировать том, не назначая ему указанную букву диска.</li>
<li>Библиотека libzip обновлена до версии 1.5.2.</li>
<li>Добавлен запрет создания ярлыка удаления в меню <i>Пуск</i> при установке VeraCrypt (от Sven Strickroth).</li>
<li>Разрешён выбор быстрого форматирования для создания файлов-контейнеров. Отдельные опции быстрого форматирования и динамического тома в пользовательском интерфейсе мастера.</li>
<li>Исправлен редактор файла конфигурации шифрования системы EFI, который не принимал клавишу Enter для добавления новых строк.</li>
<li>Программа избегает одновременных вызовов монтирования избранного, например, если соответствующая горячая клавиша нажата несколько раз.</li>
<li>Гарантируется, что создать безопасный рабочий стол может только один поток за раз.</li>
<li>Изменён размер некоторых окон в параметрах форматирования и монтирования, чтобы исправить ряд проблем с усечением текста на языках, отличных от английского.</li>
<li>Исправлена высокая загрузка ЦП при использовании избранного и добавлен ключ, запрещающий периодическую проверку устройств, чтобы снизить нагрузку на ЦП.</li>
<li>Небольшие изменения пользовательского интерфейса.</li>
<li>Обновления и исправления в переводах и документации.</li>
</ul>
</li>
<li><strong>Mac OS X:</strong>
<ul>
<li>Добавлена проверка размера файла-контейнера во время создания, чтобы убедиться, что он меньше доступного свободного места на диске. Добавлен ключ командной строки <code>--no-size-check</code>, запрещающий эту проверку.</li>
</ul>
</li>
<li><strong>Linux:</strong>
<ul>
<li>Добавлен ключ командной строки <code>--import-token-keyfiles</code>, совместимый с неинтерактивным режимом.</li>
<li>Добавлена проверка размера файла-контейнера во время создания, чтобы убедиться, что он меньше доступного свободного места на диске. Добавлен ключ командной строки <code>--no-size-check</code>, запрещающий эту проверку.</li>
</ul>
</li>
</ul>

<p><strong style="text-align:left">1.23-Hotfix-2 </strong>(8 октября 2018 года)</p>
<ul>
<li><strong>Windows:</strong>
<ul>
<li>Исправлена малоопасная уязвимость, унаследованная от TrueCrypt, которая позволяла считывать 3 байта памяти стека ядра (редко – 25 дополнительных байт).
<ul>
<li>Сообщил Tim Harrison.</li>
</ul>
</li>
<li>Отключено быстрое форматирование при создании файлов-контейнеров из командной строки. Добавлен ключ <code>/quick</code>, чтобы включить его, если это необходимо.</li>
<li>Добавлен ключ <code>/nosizecheck</code>, отключающий проверку размера контейнера на наличие свободного места во время его создания.
<ul>
<li>Это позволяет обойти ошибку в распределённой файловой системе Microsoft DFS (Distributed File System).</li>
</ul>
</li>
</ul>
</li>
</ul>

<p><strong style="text-align:left">1.23 </strong>(12 сентября 2018 года)</p>
<ul>
<li><strong>Windows:</strong>
<ul>
<li>VeraCrypt теперь совместим с конфигурацией EFI SecureBoot по умолчанию для системного шифрования.</li>
<li>Исправление проблем с шифрованием системы EFI на некоторых машинах (например на HP, Acer).</li>
<li>Поддержка системного шифрования EFI в Windows LTSB.</li>
<li>Добавлена совместимость шифрования системы с обновлением Windows 10 с помощью механизма ReflectDrivers.</li>
<li>EFI-диск восстановления правильно расшифровывает раздел, если его первый сектор был перезаписан программой восстановления Windows.</li>
<li>Добавлена опция драйвера в пользовательский интерфейс, чтобы явно разрешить дефрагментатору Windows 8.1 и Windows 10 видеть зашифрованные диски VeraCrypt.</li>
<li>Добавлена внутренняя проверка встроенной подписи двоичных файлов для защиты от некоторых типов атак.</li>
<li>Исправлена ошибка, из-за которой при некоторых обстоятельствах не работал безопасный рабочий стол для избранных томов, настроенных на монтирование при входе в Windows 10.</li>
<li>Когда включён безопасный рабочий стол, он используется для окна параметров монтирования, если оно отображается перед окном пароля.</li>
<li>При извлечении файлов в установочном или переносном режиме распаковываются zip-файлы docs.zip и Languages.zip, чтобы иметь готовую к использованию конфигурацию.</li>
<li>Если текст, вставленный в поле пароля, превышает максимальную длину, и потому будет усечён, то отображается предупреждающее сообщение.</li>
<li>Реализован механизм выбора языка в начале программы установки, чтобы упростить работу пользователям из разных стран.</li>
<li>Добавлена проверка размера файла-контейнера во время создания, чтобы убедиться, что он меньше доступного свободного места на диске.</li>
<li>Исправлены проблема, из-за которой не отображались кнопки внизу, если в Windows 7 пользователь установил большой системный шрифт.</li>
<li>Исправлены проблемы совместимости с некоторыми драйверами дисков, которые не поддерживают IOCTL_DISK_GET_DRIVE_GEOMETRY_EX ioctl.</li>
</ul>
</li>
<li><strong>Mac OS X:</strong>
<ul>
<li>Поддержка вставки значений в поля пароля с клавиатуры (сочетания клавиш CMD+V и CMD+A теперь работают правильно).
<li>В окне параметров монтирования добавлена опция, чтобы при монтировании принудительно использовать встроенную резервную копию заголовка.</li>
<li>При резервном копировании заголовка тома программа автоматически пробует использовать встроенную резервную копию заголовка, если не удаётся использовать основной заголовок.</li>
<li>Реализован пользовательский интерфейс тестов алгоритмов хеширования и PKCS-5 PRF.</li>
</ul>
</li>
<li><strong>Linux:</strong>
<ul>
<li>Не разрешено закрывать окно ожидания до завершения соответствующей операции. Это исправляет сбой в Lubuntu 16.04.
<li>В окне параметров монтирования добавлена опция, чтобы при монтировании принудительно использовать встроенную резервную копию заголовка.</li>
<li>При резервном копировании заголовка тома программа автоматически пробует использовать встроенную резервную копию заголовка, если не удаётся использовать основной заголовок.</li>
<li>Реализован пользовательский интерфейс тестов алгоритмов хеширования и PKCS-5 PRF.</li>
<li>Удалено ограничение защиты скрытых томов на диске с размером сектора более 512 байт.</li>
</ul>
</li>
</ul>


<p><strong style="text-align:left">1.22 </strong>(30 марта 2018 года)</p>
<ul>
<li><strong>Все операционные системы:</strong>
<ul>
<li>SIMD-оптимизация скорости для реализации шифра "Кузнечик" (ускорение до двух раз).</li>
<li>Добавлено пять новых каскадов алгоритмов шифрования: Camellia-Kuznyechik, Camellia-Serpent, Kuznyechik-AES, Kuznyechik-Serpent-Camellia и Kuznyechik-Twofish.</li>
</ul>
</li>
<li><strong>Windows:</strong>
<ul>
<li>Загрузчик MBR: исправлена ошибка при загрузке скрытой ОС на некоторых компьютерах.</li>
<li>Загрузчик MBR: уменьшена загрузка процессора при запросе пароля.</li>
<li>Улучшение безопасности: добавлена опция для блокировки команды TRIM для шифрования системы на твердотельных (SSD) накопителях.</li>
<li>Внедрена поддержка TRIM для несистемных твердотельных (SSD) накопителей и добавлена опция для её включения (для несистемных томов TRIM по умолчанию отключена).</li>
<li>Улучшено исправление проблем с "Неправильным параметром" во время шифрования системы EFI на некоторых компьютерах.</li>
<li>Драйвер: удалена ненужная зависимость от wcsstr, которая может вызвать проблемы на некоторых машинах.</li>
<li>Драйвер: исправлена ошибка "Неверный параметр" при монтировании томов на некоторых компьютерах.</li>
<li>Исправлена ошибка при монтировании на некоторых компьютерах системных избранных томов во время загрузки.</li>
<li>Исправлена ошибка, из-за которой текущее приложение теряет фокус при запуске VeraCrypt с ключами /quit /silent в командной строке.</li>
<li>Исправлены некоторые случаи зависания внешних приложений при монтирования/демонтировании.</li>
<li>Исправлены редкие случаи, когда не отображалось окно пароля на безопасном рабочем столе, что приводило к блокировке пользовательского интерфейса.</li>
<li>Обновлена библиотека libzip до версии 1.5.0, которая включает исправления некоторых проблем безопасности.</li>
<li>Расширена функция безопасного рабочего стола до окна ввода ПИН-кода смарт-карты.</li>
<li>Исправлено усечение текста лицензии в мастере установки.</li>
<li>Добавлен переносимый пакет, позволяющий извлекать двоичные файлы без запроса прав администратора.</li>
<li>Упрощение формата языковых XML-файлов.</li>
<li>Обходной путь для случаев, когда окно пароля не получает фокуса для ввода с клавиатуры, если не включён безопасный рабочий стол.</li>
</ul>
<li><strong>Linux:</strong>
<ul>
<li>Исправлена ошибка при установке версии с графическим интерфейсом в последних версиях KDE.</li>
<li>Исправлена ошибка утверждения wxWidgets при резервном копировании/восстановлении заголовка тома.</li>
</ul>
</li>
<li><strong>Mac OS X:</strong>
<ul>
<li>Устранена проблема, из-за которой некоторые локальные файлы справки не открывались в браузере.</li>
</ul>
</li>
</ul>

<p><strong style="text-align:left">1.21 </strong>(9 июля 2017 года)</p>
<ul>
<li><strong>Все операционные системы:</strong>
<ul>
<li>Исправлена регрессия в версии 1.20, приводившая к сбою при работе на процессоре, не поддерживающем расширенные функции.</li>
</ul>
</li>
<li><strong>Windows:</strong>
<ul>
<li>Исправлена регрессия в версии 1.20, из-за которой во время монтирования игнорировалось значение PIM, хранящееся в избранном.</li>
<li>Исправлена регрессия в версии 1.20, из-за которой в некоторых случаях не монтировались системные избранные тома.</li>
<li>Исправлены некоторые случаи ошибки "Неверный параметр" в мастере шифрования системы EFI.</li>
<li>Устанавливаются документы PDF, связанные с конфигурацией шифрования системы EFI для опытных пользователей:
<ul>
<li>disk_encryption_v1_2.pdf – скрытая EFI ОС и полнодисковое шифрование;</li>
<li>dcs_tpm_owner_02.pdf – конфигурация TPM для шифрования системы EFI.</li>
</ul>
</li>
</ul>
</li>
<li><strong>FreeBSD:</strong>
<ul>
<li>Добавлена поддержка сборки на FreeBSD.</li>
</ul>
</li>
</ul>

<p><strong style="text-align:left">1.20 </strong>(29 июня 2017 года)</p>
<ul>
<li><strong>Все операционные системы:</strong>
<ul>
<li>Используется 64-разрядная оптимизированная реализация сборки Twofish и Camellia от Jussi Kivilinna.
<ul>
<li>Camellia работает в 2&#189; раза быстрее на процессорах, поддерживающих расширение системы команд AES-NI, и на 30% быстрее без такой поддержки.</li>
</ul>
</li>
<li>Оптимизирована реализация SHA-512/SHA256.
<ul>
<li>33%-ное ускорение на 64-разрядных системах.</li>
</ul>
</li>
<li>Локальная документация развёртывается в формате HTML вместо Руководства пользователя в формате PDF.</li>
<li>Изменены ссылки в пользовательском интерфейсе со ссылок на Codeplex на ссылки, размещённые на veracrypt.fr.</li>
<li>Безопасность: сборка двоичных файлов с поддержкой рандомизации адресного пространства (ASLR).</li>
</ul>
</li>
<li><strong>Windows:</strong>
<ul>
<li>Несколько исправлений и модификаций для шифрования системы EFI:
<ul>
<li>Исправлена ошибка дешифрования системы EFI с помощью EFI-диска восстановления.</li>
<li>Добавлена ​​поддержка TPM 1.2 и TPM 2.0 (экспериментальная) с помощью низкоуровневой конфигурации DCS.
<ul>
<li><a href="https://dc5.sourceforge.io/docs/dcs_tpm_owner_02.pdf" target="_blank">https://dc5.sourceforge.io/docs/dcs_tpm_owner_02.pdf</a>
</li>
</ul>
<li>Добавлена поддержка полного шифрования диска EFI и скрытой ОС с помощью ручной процедуры (не отображается в пользовательском интерфейсе).
<ul>
<li><a href="https://dc5.sourceforge.io/docs/disk_encryption_v1_2.pdf" target="_blank">https://dc5.sourceforge.io/docs/disk_encryption_v1_2.pdf</a>
</li>
</ul>
</li>
</ul>
</li>

<li>Для ввода пароля можно использовать безопасный рабочий стол. Чтобы включить эту функцию, добавлена опция в настройках и ключ <code>/secureDesktop</code> в командной строке.</li>
<li>При монтировании нескольких избранных томов с кэшированием паролей используются параметры монтирования по умолчанию.</li>
<li>Разрешено указывать <i>PRF</i> и <i>Режим TrueCrypt</i> для избранного.</li>
<li>Предварительные изменения драйвера для поддержки функциональности скрытой ОС EFI.</li>
<li>Исправлена ошибка, из-за которой Streebog не распознавался командной строкой /hash.</li>
<li>Добавлена поддержка файловой системы ReFS в Windows 10 при создании обычных томов.</li>
<li>Исправлена высокая загрузка ЦП, когда избранное настроено на монтирование с помощью VolumeID при обнаружении.</li>
<li>Руководство пользователя предоставляется в формате CHM, а не в PDF.</li>
<li>Исправлено ложное предупреждение при шифровании системы EFI о том, что Windows не установлена на загрузочном диске.</li>
<li>Улучшения в обработке драйвером различных дисковых IOCTL.</li>
<li>Усовершенствования загрузчика EFI. Добавлена возможность вручную редактировать файл конфигурации EFI.</li>
<li>Безопасность драйвера: используется усиленная защита пула NX в Windows 8 и новее.</li>
<li>Снижено влияние на производительность внутренней проверки отключённых сетевых дисков.</li>
<li>Мелкие исправления.</li>
</ul>
</li>
<li><strong>Mac OS X:</strong>
<ul>
<li>Для запуска VeraCrypt требуется OS X 10.7 Lion или новее.</li>
<li>Программа VeraCrypt сделана обработчиком файлов .hc и .tc по умолчанию.</li>
<li>В Finder для файлов .hc и .tc добавлен пользовательский значок VeraCrypt.</li>
<li>При открытии файла-контейнера с расширением .tc, в окне пароля включается опция <i>Режим TrueCrypt</i>.</li>
</ul>
</li>
<li><strong>Linux:</strong>
<ul>
<li>При открытии файла-контейнера с расширением .tc, в окне пароля включается опция <i>Режим TrueCrypt</i>.</li>
<li>Исправлен исполняемый стек в результирующем двоичном файле, который был вызван файлами криптографической сборки, в которых отсутствовало примечание GNU-stack.</li>
</ul>
</li>
</ul>

<p><strong style="text-align:left">1.19 </strong>(17 октября 2016 года)</p>
<ul>
<li><strong>Все операционные системы:</strong>
<ul>
<li>Устранены проблемы, выявленные аудитом Quarkslab:
<ul>
<li>Удалён алгоритм шифрования GOST89 (ГОСТ 28147-89).</li>
<li>Очищен и сделан проще для анализа код PBKDF2 и HMAC.</li>
<li>Добавлены тестовые векторы для алгоритма "Кузнечик".</li>
<li>В документацию добавлено предупреждение о рисках использования ключа &rdquo;tokenpin&rdquo; в командной строке.</li>
</ul></li>
<li>Используется оптимизированная для SSE2 реализация алгоритма Serpent из проекта Botan (в 2&#189; раза быстрее на 64-разрядных платформах).</li>
</ul>
</li><li><strong>Windows:</strong>
<ul>
<li>Устранены проблемы с клавиатурой в загрузчике EFI.</li>
<li>Исправлен сбой на 32-разрядных машинах при создании тома, использующего Streebog в качестве PRF.</li>
<li>Исправлено ложное обнаружение атак "Evil Maid" в некоторых случаях (например, создание скрытой ОС).</li>
<li>Устранён сбой при доступе к данным EFS на томах VeraCrypt в Windows 10.</li>
<li>Исправлена ошибка неправильного пароля в процессе копирования скрытой ОС.</li>
<li>Устранены проблемы, выявленные аудитом Quarkslab:
<ul>
<li>Исправлена утечка длины пароля в загрузчике MBR, унаследованном от TrueCrypt.</li>
<li>Загрузчик EFI: Исправлены различные утечки и стирание буфера клавиатуры после ввода пароля.</li>
<li>Для обработки zip-файла Диска восстановления вместо уязвимой библиотеки XUnzip используется библиотека libzip.</li>
</ul></li>
<li>Поддержка системного шифрования EFI для 32-разрядных версий Windows.</li>
<li>Во время пре-теста шифрования системы EFI для обнаружения несовместимых системных плат выполняется выключение, а не перезагрузка.</li>
<li>Небольшие исправления графического интерфейса и переводов.</li></ul>
</li><li><strong>Mac OS X:</strong>
<ul>
<li>Удалена зависимость от уровня совместимости MacFUSE в OSXFuse.</li></ul>
</li></ul>
<p>&nbsp;</p>
<p><strong style="text-align:left">1.18a </strong>(17 августа 2016 года)</p>
<ul>
<li><strong>Все операционные системы:</strong>
<ul>
<li>Поддержка японского стандарта шифрования Camellia, в том числе для системного шифрования Windows (MBR и EFI).</li>
<li>Поддержка российских стандартов шифрования и хеширования "Кузнечик", "Магма" и "Стрибог", в том числе для системного шифрования Windows EFI.</li>
<li>Исправлена уязвимость TrueCrypt, позволявшая обнаруживать наличие скрытых томов (сообщил Алексей Михайлович Иванов, alekc96 [at] mail dot ru)
<ul><li> <strong style="color:#ff0000;">Чтобы избежать намёков на то, что ваши тома (не) содержат скрытый том, или если вам необходима возможность правдоподобно отрицать наличие шифрования при использовании скрытых томов/ОС, вы должны создать заново как внешние, так и скрытые тома, включая шифрование системы и скрытую ОС, и удалить существующие тома, созданные версией VeraCrypt старее, чем 1.18a.</strong></li></ul>
</li></ul>
</li><li><strong>Windows:</strong>
<ul>
<li>Поддержка шифрования системы EFI Windows (ограничения: нет скрытой ОС, нет пользовательского сообщения при загрузке).</li>
<li>Улучшена защита от атак перехвата DLL.</li>
<li>Исправлена проблема с загрузкой на некоторых компьютерах увеличением требуемой памяти на 1 КиБ.</li>
<li>Добавлен тест алгоритмов хеширования и PRF с PIM (включая предзагрузку).</li>
<li>Для лучшей стабильности система сборки переведена на Visual C&#43;&#43; 2010.</li>
<li>Реализован обходной путь для поддержки AES-NI в Hyper-V на Windows Server 2008 R2.</li>
<li>Корректное удаление файла драйвера veracrypt.sys при удалении в 64-разрядной Windows.</li>
<li>Реализована передача ПИН-кода смарт-карты как аргумента командной строки (/tokenpin) при явном монтировании тома.</li>
<li>Если не указана буква диска, выбирается A: или B:, только если нет другой свободной буквы диска.</li>
<li>Уменьшена загрузка ЦП, вызванная опцией запрета использования отключённых сетевых дисков.</li>
<li>Добавлен новый механизм идентификатора тома, который будет использоваться для идентификации дисков/разделов вместо их имени устройства.</li>
<li>Добавлена опция отключения запроса PIM во время предзагрузочной аутентификации, сохраняющая значение PIM в незашифрованном виде в MBR.</li>
<li>Добавлены опция и ключ в командной строке, позволяющие скрыть окно ожидания при выполнении операций.</li>
<li>В графическом интерфейсе мастера форматирования VeraCrypt добавлена опция, отключающая проверку Диска восстановления во время шифрования системы.</li>
<li>Разрешено перетаскивание файлов, когда VeraCrypt работает как процесс с повышенными правами.</li>
<li>Небольшие исправления графического интерфейса и переводов.</li></ul>
</li><li><strong>Linux:</strong>
<ul>
<li>Исправлена проблема с монтированием в Fedora 23.</li>
<li>Исправлена ошибка монтирования при компиляции исходного кода с помощью gcc 5.x.</li>
<li>Соответствие спецификации рабочего стола XDG, используя XDG_CONFIG_HOME для определения местоположения файлов конфигурации.</li>
</ul>
</li><li><strong>Mac OS X:</strong>
<ul>
<li>Решена проблема совместимости с новыми версиями OSXFuse.</li></ul>
</li></ul>
<p>&nbsp;</p>
<p><strong style="text-align:left">1.17 </strong>(13 февраля 2016 года)</p>
<ul>
<li><strong>Все операционные системы:</strong>
<ul>
<li>Поддержка паролей в Юникоде: теперь в паролях принимаются все символы (кроме системного шифрования Windows).</li>
<li>Вдвое сокращено время монтирования/загрузки вдвое благодаря оригинальной оптимизации формирования ключей (обнаружил <a href="https://madiba.encs.concordia.ca/~x_decarn/" target="_blank">Xavier de Carn&eacute; de Carnavalet</a>)</li>
<li>Оптимизирована производительность Whirlpool PRF (быстрее на 25% по сравнению с предыдущим кодом).</li>
<li>Добавлена поддержка создания томов exFAT.</li>
<li>Добавлен графический индикатор количества случайностей, собранных с помощью перемещений мыши.</li>
<li>Добавлены новые значки и графика, предоставленные <em>Andreas Becker</em> (<a href="http://www.andreasbecker.de" target="_blank">http://www.andreasbecker.de</a>)
</li></ul>
</li><li><strong>Windows:</strong>
<ul>
<li>Устранена проблема захвата DLL, влияющая на установщик, который позволяет выполнять код с повышением привилегий (CVE-2016-1281). Сообщил Stefan Kanthak (<a href="http://home.arcor.de/skanthak/" target="_blank">http://home.arcor.de/skanthak/</a>)</li>
<li>Двоичные файлы подписаны как SHA-1, так и SHA-256, чтобы следовать новым рекомендациям Microsoft.</li>
<li>Устранены проблемы с Comodo/Kaspersky при запуске приложения с тома VeraCrypt (сообщил и исправил Robert Geisler).</li>
<li>В загрузчике добавлена защита длины пароля/PIM заполнением полей до максимальной длины с помощью '*' после Enter.</li>
<li>Решена проблема, из-за которой системные избранные тома нельзя было смонтировать на диск A:.</li>
<li>Решена проблема с потерей фокуса после отображения окна ожидания.</li>
<li>Устранена редкая проблема, из-за которой некоторые разделы были ассоциированы с неправильным диском в окне выбора устройства.</li>
<li>Реализовано кэширование PIM как для шифрования системы, так и для обычных томов. Добавлена опция для его активирования.</li>
<li>Программа не пытается монтировать тома, используя кэшированные пароли, если пароль и/или ключевой файл указаны в командной строке.</li>
<li>Внутренняя переработка, позволившая сделать VeraCrypt полностью юникодным приложением.</li>
<li>Добавлен обходной способ, чтобы избежать ложного срабатывания некоторых антивирусных программ.</li>
<li>Отключённые сетевые диски не показываются в списке доступных дисков. Добавлена опция, позволяющая сделать их доступными для монтирования.</li>
<li>Устранена проблема, из-за которой в некоторых случаях XML-файлы конфигурации и истории обновлялись, даже когда это не требовалось.</li>
<li>Устранена утечка пути к выбранным ключевым файлам в ОЗУ.</li>
<li>Исправлена ошибка, из-за которой нельзя было отменить выбор терабайтов как единицы размера в VeraCryptExpander.</li>
<li>Добавлено клавиатурное сокращение Alt&#43;i для опции <i>Использовать PIM</i> в графическом интерфейсе.</li>
<li>Незначительные исправления графического интерфейса и переводов.</li></ul>
</li><li><strong>Linux/Mac OS X:</strong>
<ul>
<li>Исправлена ​​ошибка, из-за которой опция <code>--stdin</code> неправильно обрабатывала пароли, содержащие пробел (сообщил и исправил пользователь Codeplex horsley1953).</li>
<li>Устранена проблема с созданием томов из командной строки с файловой системой, отличной от FAT.</li>
<li>Поддержка суффиксов K/M/G/T в ключе <code>--size</code>, указывающих единицу размера.</li>
</ul>
</li></ul>
<p id="116"><strong style="text-align:left">1.16 </strong>(7 октября 2015 года)</p>
<ul>
<li><strong><strong>Windows:</strong></strong>
<ul>
<li>Изменён патч для уязвимости CVE-2015-7358, устраняющий побочные эффекты в Windows, но при этом очень сложно злоупотреблять обработкой букв дисков.</li>
<li>Исправлена ошибка восстановления заголовка тома из внешнего файла в некоторых конфигурациях.</li>
<li>Добавлена опция отключения обнаружения атак "Evil Maid" для тех, кто сталкивается с ложными срабатываниями (например, проблема с FLEXnet/Adobe issue).</li>
<li>По умолчанию программа не пытается монтировать том с пустым паролем, если настроен ключевой файл по умолчанию или в командной строке указан ключевой файл. Добавлена опция, восстанавливающая прежнее поведение.
<ul>
<li>Если требуется монтирование с использованием пустого пароля, это указывается явно: <code>/p &quot;&quot;</code>
</li></ul>
</li></ul>
</li></ul>
<p><strong style="text-align:left">1.15 </strong>(26 сентября 2015 года)</p>
<ul>
<li><strong>Windows:</strong>
<ul>
<li>Исправлены две уязвимости TrueCrypt, о которых сообщил James Forshaw (Google Project Zero).
<ul>
<li><a href="https://code.google.com/p/google-security-research/issues/detail?id=538" target="_blank">CVE-2015-7358</a> (критическое): Локальное повышение привилегий в Windows путём злоупотребления обработкой букв дисков.</li>
<li><a href="https://code.google.com/p/google-security-research/issues/detail?id=537" target="_blank">CVE-2015-7359</a>: Локальное повышение привилегий в Windows, вызванное неправильной обработкой токена олицетворения.</li>
</ul></li>
<li>Исправлена регрессия в монтировании избранных томов при входе пользователя в систему.</li>
<li>Исправлено отображение некоторых юникодных языков (например китайского) в мастере форматирования.</li>
<li>Фокус клавиатуры устанавливается на поле PIM, если включена опция <i>Использовать PIM</i>.</li>
<li>Ключу приложения разрешено открывать контекстное меню в списке букв дисков.</li>
<li>Поддержка указания размера томов в терабайтах в графическом интерфейсе (в командной строке это уже поддерживалось).</li>
</ul>
</li></ul>
<p><strong style="text-align:left">1.14 </strong>(16 сентября 2015 года)</p>
<ul>
<li><strong>Все операционные системы:</strong>
<ul>
<li>Подобно паролю, добавлено скрытие/показ значения PIM в графическом интерфейсе и в загрузчике.</li></ul>
</li></ul>
<ul>
<li><strong>Windows:</strong>
<ul>
<li>Исправлена ошибка, приводившая к повреждению Диска восстановления при использовании каскадных шифров и SHA256 для шифрования системы.</li>
<li>Решена проблема, из-за которой опция <i>Кэшировать пароль в памяти драйвера</i> была всегда отключена, даже если она включена в настройках.</li>
<li>Решена проблема, из-за которой изменение языка интерфейса не учитывается для новой установки, если не изменена настройка.</li>
<li>Реализовано создание файлов-контейнеров из командной строки.</li>
<li>Драйвер: по умолчанию отключена поддержка IOCTL_STORAGE_QUERY_PROPERTY и добавлена опция её включения.</li>
<li>Драйвер: поддержка возврата StorageDeviceProperty при запросе через IOCTL_STORAGE_QUERY_PROPERTY.</li>
<li>Поддержка установки метки тома в Проводнике с помощью параметра монтирования или значения метки избранного.</li>
<li>Исправлена проблема с окном назначения горячих клавиш, из-за которой всегда отображается OEM-233 и нельзя изменить.</li>
<li>При установке VeraCrypt и при настройке Переносного диска всегда копируются как 32-, так и 64-разрядные исполняемые двоичные файлы.
<ul>
<li>Переносной диск снова будет по умолчанию использовать 32-разрядный исполняемый файл, но также предлагать и 64-разрядный.</li>
<li>В 64-разрядной Windows теперь доступны 32-разрядные exe-файлы (например, если нужно использовать 32-разрядную библиотеку PKCS#11).</li>
</ul></li>
<li>В настройку Переносного диска (Traveler Disk) включён Расширитель томов (Volume Expander).</li>
<li>Не предлагается создать точку восстановления, если это отключено в Windows.</li>
<li>Добавлена ​​возможность проверки файла ISO-образа Диска восстановления (Rescue Disk).</li>
<li>Небольшие исправления в установщике, графическом интерфейсе и драйвере.</li></ul>
</li></ul>
<ul>
<li><strong>Linux:</strong>
<ul>
<li>Поддержка ввода пароля с использованием stdin в неинтерактивном режиме (помог
<a href="https://github.com/LouisTakePILLz" target="_blank">LouisTakePILLz</a>)
<ul>
<li>Пример: <code>veracrypt -t ${IMAGE_PATH} ${MOUNT_PATH} --mount --non-interactive --stdin &lt;&lt;&lt; &quot;$PWD&quot;</code>
</li></ul>
</li></ul>
</li></ul>
<p><strong style="text-align:left">1.13 </strong>(9 августа 2015 года)</p>
<ul>
<li><strong>Windows:</strong>
<ul>
<li>Устранён сбой TOR при запуске с тома VeraCrypt.</li></ul>
</li></ul>
<p><strong style="text-align:left">1.12 </strong>(5 августа 2015 года)</p>
<ul>
<li><strong>Все операционные системы:</strong>
<ul>
<li>Реализован "Динамический режим" поддержкой Персонального множителя итераций (Personal Iterations Multiplier, PIM). См. дополнительную информацию в документации.
</li></ul>
</li></ul>
<ul>
<li><strong>Windows:</strong>
<ul>
<li>Обнаруживается несанкционированный доступа к загрузчику (атаки "Evil Maid") для шифрования системы и предлагаются варианты восстановления.</li>
<li>Исправлена проблема с переполнением буфера и другие ошибки, связанные с памятью, при разборе языковых XML-файлов.</li>
<li>Исправление, связанное с ошибочно сообщёнными утилитой chkdsk повреждёнными секторами из-за ошибки в обработке IOCTL_DISK_VERIFY.</li>
<li>Устранена проблема конфиденциальности, вызванная обновлением файлов конфигурации и истории всякий раз, когда используется VeraCrypt (сообщил Liran Elharar).</li>
<li>Исправлена ​​ошибка, из-за которой после холодного запуска не всегда монтировалось системное избранное.</li>
<li>Устранена ошибка установщика при обновлении VeraCrypt в Windows 10.</li>
<li>Реализовано дешифрование несистемного раздела/диска.</li>
<li>Для повышения производительности на 64-разрядных компьютерах в программу установки включены и развёртываются 64-разрядные исполняемые файлы.</li>
<li>Для монтирования томов можно использовать буквы дисков A: и B:.</li>
<li>Синтаксический анализ аргументов командной строки сделан более строгим и надёжным (например, ключ /lz отклонён, должно быть /l z).</li>
<li>Добавлена возможность отображать пароль шифрования системы в графическом интерфейсе Windows и в загрузчике.</li>
<li>Устранена возникавшая у некоторых пользователей ошибка "Класс уже существует".</li>
<li>Решена проблема, из-за которой некоторые пункты меню и поля графического интерфейса не переводились на другие языки.</li>
<li>Тома правильно сообщают в Windows размер физического сектора.</li>
<li>Правильно определяются операции отключения пользователя/RDP для автоматического размонтирования при блокировке сеанса.</li>
<li>Добавлен ручной выбор раздела при возобновлении шифрования на месте.</li>
<li>Добавлен параметр командной строки (/cache f) для временного кэширования пароля во время монтирования избранного.</li>
<li>Добавлено окно ожидания для операций автоматического монтирования устройств, чтобы избежать зависания графического интерфейса.</li>
<li>Добавлена дополнительная информация к сообщению об ошибке, чтобы помочь в анализе обнаруженных проблем.</li>
<li>Отключён пункт меню для изменения PRF шифрования системы, поскольку он ещё не реализован.</li>
<li>Исправлена ошибка смены пароля, когда требуется UAC (унаследовано от TrueCrypt).</li>
<li>Незначительные исправления ошибок и изменения (см. подробности в git-истории).</li></ul>
</li></ul>
<ul>
<li><strong>Linux:</strong>
<ul>
<li>Решена проблема с установщиком в KDE, когда недоступен xterm.</li>
<li>Исправлены предупреждения в диалогах about/LegalNotice при динамическом связывании wxWidgets (Н/Д для официального двоичного файла).</li>
<li>В командной строке поддерживаются имена хешей с дефисом (sha-256, sha-512 и ripemd-160).</li>
<li>Удалён ключ &quot;--current-hash&quot; и добавлен &quot;--new-hash&quot; для большей согласованности с имеющимися ключами.</li>
<li>Если в командной строке указан только ключевой файл, программа не пытается выполнить монтирование с пустым паролем.
<ul>
<li>Если требуется монтирование с использованием пустого пароля, это указывается явно: <code>-p &quot;&quot;</code>
</li></ul>
</li></ul>
</li></ul>
<p id="1.0f-2"><strong style="text-align:left">1.0f-2</strong> (5 апреля 2015 года)</p>
<ul>
<li><strong>Все операционные системы:</strong>
<ul>
<li>Ускорено монтирование, на 64-разрядной версии он стало до 20% быстрее (благодарим Nils Maier).</li>
<li>Добавлена опция для установки хеша/режима TrueCrypt как используемых по умолчанию для монтирования томов.</li>
<li>Использование режима TrueCrypt/хеша, указанных в командной строке, в диалоговом окне пароля.</li></ul>
</li><li><strong>Windows:</strong>
<ul>
<li>Устранение уязвимости CryptAcquireContext, обнаруженной Open Crypto Audit Phase II.</li>
<li>Правильная обработка сбоев генератора случайных чисел. Информирование пользователя в таких случаях.</li>
<li>Изменения, относящиеся к режиму TrueCrypt:
<ul>
<li>Поддержка монтирования системного раздела TrueCrypt (пока без преобразования).</li>
<li>Поддержка томов TrueCrypt как системных избранных.</li>
<li>Исправлено отображение неправильного режима TrueCrypt в свойствах тома при использовании SHA-256.</li>
</ul></li>
<li>Решена проблема блокировки ПИН-кода со смарт-картами в особом случае.</li>
<li>Правильно обрабатываются ошибки доступа к файлам при монтировании контейнеров.</li>
<li>Решено несколько проблем, о которых сообщил анализ статического кода.</li>
<li>В загрузчик добавлено сообщение &quot;Verifying Password...&quot; (Проверка пароля...). </li>
<li>В случае сбоя запроса UAC (например, из-за истечения времени ожидания) пользователю предлагается повторить операцию.</li>
<li>Ссылка для удаления теперь открывает стандартное окно добавления/удаления программ.</li>
<li>При удалении программы удаляются все ссылки VeraCrypt из реестра и с диска.</li>
<li>В пакет установки включён Расширитель томов VeraCryptExpander.</li>
<li>Добавлена опция для временного пароля кэша при монтировании нескольких избранных томов.</li>
<li>Незначительные исправления ошибок и улучшения (см. подробности в git-истории).</li></ul>
</li><li><strong>Mac OS X:</strong>
<ul>
<li>Решена проблема с тем, что тома не размонтировались автоматически при выходе из VeraCrypt..</li>
<li>Решена проблема, когда окно VeraCrypt не открывалось повторно при щелчке по значку в доке.</li></ul>
</li><li><strong>Linux/Mac OS X:</strong>
<ul>
<li>Устранена проблема, из-за которой не закрывалось окно настроек при нажатии на значок «X».</li>
<li>Устранена возникавшая в некоторых случаях проблема с доступом только для чтения при монтировании томов, отличных от FAT.</li>
<li>Поддержка открытия/обзора смонтированных томов на рабочих столах, отличных от Gnome/KDE.</li>
<li>Устранены различные проблемы с установщиком при работе в не слишком распространённых конфигурациях.</li>
<li>Незначительные исправления (см. подробности в git-истории).</li></ul>
</li></ul>
<p><strong style="text-align:left">1.0f-1 </strong>(4 января 2015 года)</p>
<ul>
<li><strong>Все операционные системы:</strong>
<ul>
<li>Добавлена поддержка старого TrueCrypt 6.0.</li>
<li>Изменено наименование каскадов алгоритмов в графическом интерфейсе для лучшего описания.</li></ul>
</li><li><strong>Linux/Mac OS X:</strong>
<ul>
<li>Кнопка отмены в окне настроек теперь работает.</li>
<li>Устранена невозможность ввести одну цифру в размере тома.</li>
<li>Добавлено окно ожидания при вычислении в тесте.</li></ul>
</li><li><strong>Windows:</strong>
<ul>
<li>В информацию о смонтированном томе добавлен режим TrueCrypt.</li>
<li>Исправлены графические артефакты установщика в Windows XP.</li></ul>
</li></ul>
<p><strong style="text-align:left">1.0f </strong>(30 декабря 2014)</p>
<ul>
<li><strong>Все операционные системы:</strong>
<ul>
<li>Добавлена поддержка монтирования томов TrueCrypt.</li>
<li>Добавлена поддержка преобразования контейнеров и несистемных разделов TrueCrypt.</li>
<li>Добавлена поддержка SHA-256 для шифрования томов.</li>
<li>SHA-512 сделан алгоритмом формирования ключа по умолчанию, изменён порядок предпочтения алгоритмов формирования ключа: SHA-512 &gt; Whirlpool &gt; SHA-256 &gt; RIPEMD160</li>
<li>RIPEMD160 для несистемного шифрования больше не используется.</li>
<li>Ускорение монтирования благодаря возможности сразу указать правильный алгоритм хеширования.</li>
<li>Во время длительных операций отображается окна ожидания, чтобы избежать зависания графического интерфейса.</li>
<li>Реализовано создание сразу нескольких ключевых файлов, с предопределённым или случайным размером.</li>
<li>Перед выполнением конфиденциальных операций всегда отображается диалоговое окно случайного сбора.</li>
<li>Ссылки в приложении теперь указывают на онлайн-ресурсы Codeplex.</li>
<li>Первая версия правильного Руководства пользователя VeraCrypt.</li></ul>
</li><li><strong>Mac OS X:</strong>
<ul>
<li>Реализована поддержка жёстких дисков с большим размером секторов (&gt; 512).</li>
<li>Ссылка на новую версию wxWidgets 3.0.2.</li>
<li>Решена проблема с усечённым текстом в некоторых окнах мастера.</li></ul>
</li><li><strong>Linux:</strong>
<ul>
<li>Добавлена ​​поддержка форматирования томов NTFS.</li>
<li>Исправлена ​​проблема с открытием руководства пользователя в формате PDF.</li>
<li>Улучшена поддержка жёстких дисков с большим размером секторов (&gt; 512).</li>
<li>Ссылка на новую версию wxWidgets 3.0.2.</li></ul>
</li><li><strong>Windows:</strong><br>
<ul>
<li>Безопасность: исправлена уязвимость в загрузчике, обнаруженная Open Crypto Audit, загрузчик сделан более надёжным.</li>
<li>Добавлена ​​поддержка SHA-256 при шифровании загрузки системы.</li>
<li>Различные оптимизации в загрузчике.</li>
<li>Полное исправление проблемы безопасности ShellExecute.</li>
<li>Драйвер ядра: проверка, что длина пароля, полученного от загрузчика, меньше или равна 64.</li>
<li>Исправлен случайный сбой при переходе по ссылке для получения дополнительной информации о ключевых файлах.</li>
<li>Реализована возможность автоматического размонтирования, когда сеанс пользователя заблокирован.</li>
<li>Добавлены векторы самопроверки для SHA-256.</li>
<li>Современный внешний вид благодаря использованию визуальных стилей.</li>
<li>Несколько незначительных исправлений.</li></ul>
</li></ul>
<div style="text-align:left; margin-top:19px; margin-bottom:19px; padding-top:0px; padding-bottom:0px">
<strong style="text-align:left">1.0e </strong>(4 сентября 2014 года)</div>
<div style="text-align:left; margin-top:19px; margin-bottom:19px; padding-top:0px; padding-bottom:0px">
<ul>
<li><strong style="text-align:left">Улучшения и исправления ошибок:</strong>
<ul>
<li>Исправлено большинство уязвимостей безопасности, о которых сообщил Open Crypto Audit Project.</li>
<li>Устранены проблемы безопасности, обнаруженные с помощью статического анализа кода, в основном в Windows.</li>
<li>Исправлена ​​проблема с зависанием при смене пароля/ключевого файла тома. Ускорено создание зашифрованного тома/раздела.</li>
<li>Незначительные улучшения и исправления ошибок (см. подробности в git-истории).</li></ul>
</li></ul>
</div>
<div style="text-align:left; margin-top:19px; margin-bottom:19px; padding-top:0px; padding-bottom:0px">
<br style="text-align:left">
<strong style="text-align:left">1.0d </strong>(3 июня 2014 года)</div>
<div style="text-align:left; margin-top:19px; margin-bottom:19px; padding-top:0px; padding-bottom:0px">
<ul>
<li><strong style="text-align:left">Улучшения и исправления ошибок:</strong>
<ul>
<li>Исправлена ошибка при создании скрытой операционной системы.</li>
<li>Незначительные улучшения и исправления ошибок.</li></ul>
</li></ul>
</div>
</div><div class="ClearBoth"></div></body></html>
