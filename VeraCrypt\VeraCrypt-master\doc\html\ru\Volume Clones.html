﻿<!DOCTYPE html>
<html lang="ru">
<head>
<meta http-equiv="content-type" content="text/html; charset=utf-8" />
<title>VeraCrypt - Бесплатное надёжное шифрование дисков с открытым исходным кодом</title>
<meta name="description" content="VeraCrypt это бесплатное программное обеспечение для шифрования дисков с открытым исходным кодом для Windows, Mac OS X (macOS) и Linux. В случае, если злоумышленник вынуждает вас раскрыть пароль, VeraCrypt обеспечивает правдоподобное отрицание наличия шифрования. В отличие от пофайлового шифрования, VeraCrypt шифрует данные в реальном времени (на лету), автоматически, прозрачно, требует очень мало памяти и не использует временные незашифрованные файлы."/>
<meta name="keywords" content="encryption, security, шифрование, безопасность"/>
<link href="styles.css" rel="stylesheet" type="text/css" />
</head>
<body>

<div>
<a href="Documentation.html"><img src="VeraCrypt128x128.png" alt="VeraCrypt"/></a>
</div>

<div id="menu">
	<ul>
	  <li><a href="Home.html">Начало</a></li>
	  <li><a href="Code.html">Исходный код</a></li>
	  <li><a href="Downloads.html">Загрузить</a></li>
	  <li><a class="active" href="Documentation.html">Документация</a></li>
	  <li><a href="Donation.html">Поддержать разработку</a></li>
	  <li><a href="https://sourceforge.net/p/veracrypt/discussion/" target="_blank">Форум</a></li>
	</ul>
</div>

<div>
<p>
<a href="Documentation.html">Документация</a>
<img src="arrow_right.gif" alt=">>" style="margin-top: 5px">
<a href="Security%20Requirements%20and%20Precautions.html">Требования безопасности и меры предосторожности</a>
<img src="arrow_right.gif" alt=">>" style="margin-top: 5px">
<a href="Volume%20Clones.html">Клонирование томов</a>
</p></div>

<div class="wikidoc">
<div>
<h2>Клонирование томов</h2>
<p>Никогда не создавайте новый том VeraCrypt путём клонирования какого-либо уже существующего тома VeraCrypt.
Чтобы создать новый том, всегда используйте мастер создания томов VeraCrypt. Если создать клон тома, а затем
начать использовать как этот том, так и его клон так, что оба в конечном итоге будут содержать разные данные,
этим вы можете облегчить криптоанализ (поскольку при разном содержимом оба тома будут использовать один и тот же
набор ключей). Это особенно критично, если в томе находится скрытый том. Также учтите, что в подобных случаях
невозможно правдоподобно отрицать наличие шифрования (см. раздел
<a href="Plausible%20Deniability.html"><em>Правдоподобное отрицание наличия шифрования</em></a>). См. также главу
<a href="How%20to%20Back%20Up%20Securely.html">
<em>О безопасном резервном копировании</em></a>.</p>
</div>
</div><div class="ClearBoth"></div></body></html>
