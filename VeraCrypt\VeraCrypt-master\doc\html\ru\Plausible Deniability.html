﻿<!DOCTYPE html>
<html lang="ru">
<head>
<meta http-equiv="content-type" content="text/html; charset=utf-8" />
<title>VeraCrypt - Бесплатное надёжное шифрование дисков с открытым исходным кодом</title>
<meta name="description" content="VeraCrypt это бесплатное программное обеспечение для шифрования дисков с открытым исходным кодом для Windows, Mac OS X (macOS) и Linux. В случае, если злоумышленник вынуждает вас раскрыть пароль, VeraCrypt обеспечивает правдоподобное отрицание наличия шифрования. В отличие от пофайлового шифрования, VeraCrypt шифрует данные в реальном времени (на лету), автоматически, прозрачно, требует очень мало памяти и не использует временные незашифрованные файлы."/>
<meta name="keywords" content="encryption, security, шифрование, безопасность"/>
<link href="styles.css" rel="stylesheet" type="text/css" />
</head>
<body>

<div>
<a href="Documentation.html"><img src="VeraCrypt128x128.png" alt="VeraCrypt"/></a>
</div>

<div id="menu">
	<ul>
	  <li><a href="Home.html">Начало</a></li>
	  <li><a href="Code.html">Исходный код</a></li>
	  <li><a href="Downloads.html">Загрузить</a></li>
	  <li><a class="active" href="Documentation.html">Документация</a></li>
	  <li><a href="Donation.html">Поддержать разработку</a></li>
	  <li><a href="https://sourceforge.net/p/veracrypt/discussion/" target="_blank">Форум</a></li>
	</ul>
</div>

<div>
<p>
<a href="Documentation.html">Документация</a>
<img src="arrow_right.gif" alt=">>" style="margin-top: 5px">
<a href="Plausible%20Deniability.html">Правдоподобное отрицание наличия шифрования</a>
</p></div>

<div class="wikidoc">
<h1>Правдоподобное отрицание наличия шифрования</h1>
<div style="text-align:left; margin-top:19px; margin-bottom:19px; padding-top:0px; padding-bottom:0px">
На случай, если злоумышленник вынудит вас сообщить пароль, в VeraCrypt предусмотрено два вида правдоподобного
отрицания наличия шифрования:</div>
<ol style="text-align:left; margin-top:18px; margin-bottom:19px; padding-top:0px; padding-bottom:0px">
<li style="text-align:left; margin-top:0px; margin-bottom:0px; padding-top:0px; padding-bottom:0px">
Скрытые тома (см. раздел <a href="Hidden%20Volume.html" style="text-align:left; color:#0080c0; text-decoration:none; font-weight:bold">
Скрытый том</a>) и скрытые операционные системы (см. раздел <a href="Hidden%20Operating%20System.html" style="text-align:left; color:#0080c0; text-decoration:none">
<strong style="text-align:left">Скрытая операционная система</strong></a>). </li><li style="text-align:left; margin-top:0px; margin-bottom:0px; padding-top:0px; padding-bottom:0px">
Пока не будет выполнено дешифрование, раздел/устройство VeraCrypt выглядит как содержащее случайные данные
(в нём нет никаких "сигнатур"). Поэтому должно быть невозможно доказать, что раздел или устройство являются
томом VeraCrypt или что они зашифрованы (при условии соблюдения требований, перечисленных в главе
<a href="Security%20Requirements%20and%20Precautions.html" style="text-align:left; color:#0080c0; text-decoration:none">
Требования безопасности и меры предосторожности</a>). Правдоподобное объяснение, что раздел/устройство
содержит только случайные данные, может быть таким: вы уничтожили (стёрли с затиранием данных) содержимое
раздела/устройства с помощью одной из программ для удаления информации с перезаписью случайными данными 
(на самом деле, VeraCrypt также можно использовать для безопасного стирания раздела/устройства, создав
внутри него пустой зашифрованный раздел/том на основе устройства). При этом, однако, требуется предотвращать
утечки данных (см. раздел
<a href="Data%20Leaks.html" style="text-align:left; color:#0080c0; text-decoration:none">
Утечки данных</a>), а также иметь в виду, что при <a href="System%20Encryption.html" style="text-align:left; color:#0080c0; text-decoration:none">
шифровании системы</a> первая дорожка диска содержит (незашифрованный) загрузчик VeraCrypt, который можно
легко идентифицировать (см. подробности в главе
<a href="System%20Encryption.html" style="text-align:left; color:#0080c0; text-decoration:none">
Шифрование системы</a>). В случае <a href="System%20Encryption.html" style="text-align:left; color:#0080c0; text-decoration:none">
шифрования системы</a>, правдоподобное отрицание наличия шифрования достигается созданием скрытой операционной
системы (см. раздел
<a href="Hidden%20Operating%20System.html" style="text-align:left; color:#0080c0; text-decoration:none">
Скрытая операционная система</a>).<br style="text-align:left">
<br style="text-align:left">
Хотя тома VeraCrypt на основе файлов (контейнеров) тоже не содержат никаких опознавательных "сигнатур"
(до тех пор, пока не выполнено дешифрование, тома выглядят лишь как набор случайных данных), они не обеспечивают
никакого правдоподобного отрицания наличия шифрования, так как практически невозможно правдоподобно объяснить
наличие файла, содержащего только случайные данные. Тем не менее, правдоподобного отрицания наличия шифрования
можно добиться и при использовании тома VeraCrypt на основе файла (контейнера): для этого нужно создать внутри
обычного тома скрытый том (см. выше).
</li></ol>
<h4 style="text-align:left; font-family:Arial,Helvetica,Verdana,sans-serif; font-weight:bold; margin-top:0px; font-size:12px; margin-bottom:1px">
<br style="text-align:left">
Примечания</h4>
<ul style="text-align:left; margin-top:18px; margin-bottom:19px; padding-top:0px; padding-bottom:0px">
<li style="text-align:left; margin-top:0px; margin-bottom:0px; padding-top:0px; padding-bottom:0px">
При форматировании раздела жёсткого диска как тома VeraCrypt (или шифрования раздела на месте), таблица
разделов (включая тип раздела)
<em style="text-align:left">никогда</em> не изменяется (в таблицу разделов не вносится никаких "сигнатур" или
"идентификаторов" VeraCrypt).
</li><li style="text-align:left; margin-top:0px; margin-bottom:0px; padding-top:0px; padding-bottom:0px">
Существуют методы обнаружения файлов и устройств, содержащих случайные данные (таких, как тома VeraCrypt).
Тем не менее это никаким образом <em style="text-align:left">не</em> должно влиять на правдоподобное отрицание
наличия шифрования. Злоумышленник по-прежнему не должен быть в состоянии <i>доказать</i>, что раздел/устройство является
томом VeraCrypt или что файл, раздел или устройство содержат скрытый том VeraCrypt (если вы соблюли все условия,
описанные в главе
<a href="Security%20Requirements%20and%20Precautions.html" style="text-align:left; color:#0080c0; text-decoration:none">
Требования безопасности и меры предосторожности</a> и в подразделе <a href="Security%20Requirements%20for%20Hidden%20Volumes.html" style="text-align:left; color:#0080c0; text-decoration:none">
Требования безопасности и меры предосторожности, касающиеся скрытых томов</a>). </li></ul>
<p>&nbsp;</p>
<p><a href="Hidden%20Volume.html" style="text-align:left; color:#0080c0; text-decoration:none; font-weight:bold">Следующий раздел &gt;&gt;</a></p>
</div><div class="ClearBoth"></div></body></html>
