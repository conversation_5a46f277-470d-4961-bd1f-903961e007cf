<!DOCTYPE html>
<html lang="en">
<head>
<meta http-equiv="content-type" content="text/html; charset=utf-8" />
<title>VeraCrypt - Free Open source disk encryption with strong security for the Paranoid</title>
<meta name="description" content="VeraCrypt is free open-source disk encryption software for Windows, Mac OS X and Linux. In case an attacker forces you to reveal the password, VeraCrypt provides plausible deniability. In contrast to file encryption, data encryption performed by VeraCrypt is real-time (on-the-fly), automatic, transparent, needs very little memory, and does not involve temporary unencrypted files."/>
<meta name="keywords" content="encryption, security"/>
<link href="styles.css" rel="stylesheet" type="text/css" />
</head>
<body>

<div>
<a href="Documentation.html"><img src="VeraCrypt128x128.png" alt="VeraCrypt"/></a>
</div>

<div id="menu">
	<ul>
	  <li><a href="Home.html">Home</a></li>
	  <li><a href="Code.html">Source Code</a></li>
	  <li><a href="Downloads.html">Downloads</a></li>
	  <li><a class="active" href="Documentation.html">Documentation</a></li>
	  <li><a href="Donation.html">Donate</a></li>
	  <li><a href="https://sourceforge.net/p/veracrypt/discussion/" target="_blank">Forums</a></li>
	</ul>
</div>

<div>
<p>
<a href="Documentation.html">Documentation</a>
<img src="arrow_right.gif" alt=">>" style="margin-top: 5px">
<a href="Technical%20Details.html">Technical Details</a>
<img src="arrow_right.gif" alt=">>" style="margin-top: 5px">
<a href="Notation.html">Notation</a>
</p></div>

<div class="wikidoc">
<h1>Notation</h1>
<p>&nbsp;</p>
<table cellspacing="0">
<tbody>
<tr>
<td><em>C</em></td>
<td>Ciphertext block</td>
</tr>
<tr>
<td><em>DK()</em></td>
<td>Decryption algorithm using encryption/decryption key <em>K</em></td>
</tr>
<tr>
<td><em>EK()</em></td>
<td>Encryption algorithm using encryption/decryption key <em>K</em></td>
</tr>
<tr>
<td><em>H()</em></td>
<td>Hash function</td>
</tr>
<tr>
<td><em>i</em></td>
<td>Block index for n-bit blocks; n is context-dependent</td>
</tr>
<tr>
<td><em>K</em></td>
<td>Cryptographic key</td>
</tr>
<tr>
<td><em>^</em></td>
<td>Bitwise exclusive-OR operation (XOR)</td>
</tr>
<tr>
<td><em>&oplus;</em></td>
<td>Modulo 2n addition, where n is the bit size of the left-most operand and of the resultant value (e.g., if the left operand is a 1-bit value, and the right operand is a 2-bit value, then: 1 &oplus; 0 = 1; 1 &oplus; 1 = 0; 1 &oplus; 2 = 1; 1 &oplus; 3 = 0;
 0 &oplus; 0 = 0; 0 &oplus; 1 = 1; 0 &oplus; 2 = 0; 0 &oplus; 3 = 1)</td>
</tr>
<tr>
<td><em>&otimes;</em></td>
<td>Modular multiplication of two polynomials over the binary field GF(2) modulo x128&#43;x7&#43;x2&#43;x&#43;1 (GF stands for Galois Field)</td>
</tr>
<tr>
<td><em>||</em></td>
<td>Concatenation</td>
</tr>
</tbody>
</table>
<p>&nbsp;</p>
<p><a href="Encryption%20Scheme.html" style="text-align:left; color:#0080c0; text-decoration:none; font-weight:bold">Next Section &gt;&gt;</a></p>
</div>
</body></html>
