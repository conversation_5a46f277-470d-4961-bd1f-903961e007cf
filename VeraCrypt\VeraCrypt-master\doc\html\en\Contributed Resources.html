<!DOCTYPE html>
<html lang="en">
<head>
<meta http-equiv="content-type" content="text/html; charset=utf-8" />
<title>VeraCrypt - Free Open source disk encryption with strong security for the Paranoid</title>
<meta name="description" content="VeraCrypt is free open-source disk encryption software for Windows, Mac OS X and Linux. In case an attacker forces you to reveal the password, VeraCrypt provides plausible deniability. In contrast to file encryption, data encryption performed by VeraCrypt is real-time (on-the-fly), automatic, transparent, needs very little memory, and does not involve temporary unencrypted files."/>
<meta name="keywords" content="encryption, security"/>
<link href="styles.css" rel="stylesheet" type="text/css" />
</head>
<body>

<div>
<a href="Documentation.html"><img src="VeraCrypt128x128.png" alt="VeraCrypt"/></a>
</div>

<div id="menu">
	<ul>
	  <li><a class="active" href="Home.html">Home</a></li>
	  <li><a href="Code.html">Source Code</a></li>
	  <li><a href="Downloads.html">Downloads</a></li>
	  <li><a href="Documentation.html">Documentation</a></li>
	  <li><a href="Donation.html">Donate</a></li>
	  <li><a href="https://sourceforge.net/p/veracrypt/discussion/" target="_blank">Forums</a></li>
	</ul>
</div>

<div class="wikidoc">
<p>Here you'll find useful resources contributed by VeraCrypt users.</p>
<h3>Third party binaries:</h3>
<ul>
<li>Linux Ubuntu <strong>PPA</strong> provided by user&nbsp;<a href="https://unit193.net/" target="_blank">&quot;Unit 193&quot;</a> (build done by Launchpad):
<ul>
<li><a href="https://launchpad.net/~unit193/&#43;archive/ubuntu/encryption" target="_blank">https://launchpad.net/~unit193/&#43;archive/ubuntu/encryption</a>
</li></ul>
</li><li>Linux <strong>Armv7</strong> GUI/console 32-bit build on ChromeBook by user <a href="https://www.codeplex.com/site/users/view/haggster">
haggster</a>:
<ul>
<li><a href="http://sourceforge.net/projects/veracrypt/files/Contributions/ARM%20Linux/veracrypt-1.0f-1-setup-arm.tar.bz2/download" target="_blank">veracrypt-1.0f-1-setup-arm.tar.bz2</a>
</li></ul>
</li></ul>
<h3>Tutorials:</h3>
<ul>
<li><a href="http://schneckchen.in/veracrypt-anleitung-zum-daten-verschluesseln/" target="_blank">http://schneckchen.in/veracrypt-anleitung-zum-daten-verschluesseln/</a>:
<ul>
<li>German tutorial on VeraCrypt by Andreas Heinz. </li></ul>
</li><li><a href="http://howto.wared.fr/raspberry-pi-arch-linux-arm-installation-veracrypt/" target="_blank">http://howto.wared.fr/raspberry-pi-arch-linux-arm-installation-veracrypt/</a>:
<ul>
<li>French HowTo for building VeraCrypt on Raspberry Pi Arch Linux by <a href="http://howto.wared.fr/author/wared/" target="_blank">
Edouard WATTECAMPS</a>. </li></ul>
</li><li><a href="http://sourceforge.net/projects/veracrypt/files/Contributions/clonezilla_using_veracrypt_ver_1.1.doc/download" target="_blank">clonezilla_using_veracrypt_ver_1.1.doc</a>:
<ul>
<li>Tutorial on using VeraCrypt in CloneZilla for accessing encrypted backups. By
<a href="https://www.codeplex.com/site/users/view/pjc123" target="_blank">pjc123</a>.
</li></ul>
</li><li><a href="https://bohdan-danishevsky.blogspot.fr/2016/11/raspberry-pi-raspbian-installing.html" target="_blank">https://bohdan-danishevsky.blogspot.fr/2016/11/raspberry-pi-raspbian-installing.html</a>
<ul>
<li>Tutorial on installing and using official VeraCrypt binaries on Raspberry Pi (Raspbian) by Bohdan Danishevsky.
</li></ul>
</li></ul>
<h3>Miscellaneous:</h3>
<ul>
<li><a href="http://sourceforge.net/projects/veracrypt/files/Contributions/vcsteg2.py/download" target="_blank">vcsteg2.py</a>&nbsp;: a Python script that tries to hide a VeraCrypt volume inside a video file (Steganography)
</li></ul>
</div><div class="ClearBoth"></div></body></html>
