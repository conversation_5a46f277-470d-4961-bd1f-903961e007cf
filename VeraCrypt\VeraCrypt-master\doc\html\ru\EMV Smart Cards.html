﻿<!DOCTYPE html>
<html lang="ru">
  <head>
    <meta http-equiv="content-type" content="text/html; charset=utf-8" />
    <title>
      VeraCrypt - Бесплатное надёжное шифрование дисков с открытым исходным кодом
    </title>
    <meta
      name="description"
      content="VeraCrypt это бесплатное программное обеспечение для шифрования дисков с открытым исходным кодом для Windows, Mac OS X (macOS) и Linux. В случае, если злоумышленник вынуждает вас раскрыть пароль, VeraCrypt обеспечивает правдоподобное отрицание наличия шифрования. В отличие от пофайлового шифрования, VeraCrypt шифрует данные в реальном времени (на лету), автоматически, прозрачно, требует очень мало памяти и не использует временные незашифрованные файлы."
    />
    <meta name="keywords" content="encryption, security, шифрование, безопасность" />
    <link href="styles.css" rel="stylesheet" type="text/css" />
  </head>
  <body>
    <div>
      <a href="Documentation.html"
        ><img src="VeraCrypt128x128.png" alt="VeraCrypt"
      /></a>
    </div>

    <div id="menu">
      <ul>
        <li><a href="Home.html">Начало</a></li>
        <li><a href="Code.html">Исходный код</a></li>
        <li><a href="Downloads.html">Загрузить</a></li>
        <li><a class="active" href="Documentation.html">Документация</a></li>
        <li><a href="Donation.html">Поддержать разработку</a></li>
        <li>
          <a
            href="https://sourceforge.net/p/veracrypt/discussion/"
            target="_blank"
            >Форум</a
          >
        </li>
      </ul>
    </div>

    <div>
      <p>
        <a href="Documentation.html">Документация</a>
        <img src="arrow_right.gif" alt=">>" style="margin-top: 5px" />
        <a href="EMV%20Smart%20Cards.html">Смарт-карты EMV</a>
      </p>
    </div>

    <div class="wikidoc">
      <h1>Смарт-карты EMV</h1>
      <div
        style="
          text-align: left;
          margin-top: 19px;
          margin-bottom: 19px;
          padding-top: 0px;
          padding-bottom: 0px;
        "
      >
        <p>
          Версии VeraCrypt для Windows и Linux позволяют использовать смарт-карты,
          совместимые с EMV, в качестве функции. Использование смарт-карт,
          совместимых с PKCS#11, предназначено для пользователей с определёнными
          навыками кибербезопасности. Однако в некоторых ситуациях наличие такой
          карты сильно снижает правдоподобность отрицания пользователем наличия шифрования.</p>
        <p>
          Чтобы решить эту проблему, появилась идея использовать карты, которые есть у каждого,
          а именно смарт-карты, совместимые с EMV. Согласно одноимённому стандарту,
          эти карты, распространённые во всем мире, применяются для банковских операций.
          Использование внутренних данных карты EMV пользователя в качестве ключевых файлов
          повысит безопасность его тома, сохраняя при этом правдоподобность отрицания наличия
          шифрования.
        </p>
        <p>
          Более подробную техническую информацию см. в разделе
          <em style="text-align: left">Смарт-карты EMV</em> в главе
          <a
            href="Keyfiles%20in%20VeraCrypt.html"
            style="text-align: left; color: #0080c0; text-decoration: none"
          >
            <em style="text-align: left">Ключевые файлы</em></a
          >.
        </p>
      </div>
    </div>
  </body>
</html>
