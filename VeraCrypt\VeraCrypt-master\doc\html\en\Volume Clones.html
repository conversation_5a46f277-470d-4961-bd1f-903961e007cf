<!DOCTYPE html>
<html lang="en">
<head>
<meta http-equiv="content-type" content="text/html; charset=utf-8" />
<title>VeraCrypt - Free Open source disk encryption with strong security for the Paranoid</title>
<meta name="description" content="VeraCrypt is free open-source disk encryption software for Windows, Mac OS X and Linux. In case an attacker forces you to reveal the password, VeraCrypt provides plausible deniability. In contrast to file encryption, data encryption performed by VeraCrypt is real-time (on-the-fly), automatic, transparent, needs very little memory, and does not involve temporary unencrypted files."/>
<meta name="keywords" content="encryption, security"/>
<link href="styles.css" rel="stylesheet" type="text/css" />
</head>
<body>

<div>
<a href="Documentation.html"><img src="VeraCrypt128x128.png" alt="VeraCrypt"/></a>
</div>

<div id="menu">
	<ul>
	  <li><a href="Home.html">Home</a></li>
	  <li><a href="Code.html">Source Code</a></li>
	  <li><a href="Downloads.html">Downloads</a></li>
	  <li><a class="active" href="Documentation.html">Documentation</a></li>
	  <li><a href="Donation.html">Donate</a></li>
	  <li><a href="https://sourceforge.net/p/veracrypt/discussion/" target="_blank">Forums</a></li>
	</ul>
</div>

<div>
<p>
<a href="Documentation.html">Documentation</a>
<img src="arrow_right.gif" alt=">>" style="margin-top: 5px">
<a href="Security%20Requirements%20and%20Precautions.html">Security Requirements and Precautions</a>
<img src="arrow_right.gif" alt=">>" style="margin-top: 5px">
<a href="Volume%20Clones.html">Volume Clones</a>
</p></div>

<div class="wikidoc">
<div>
<h2>Volume Clones</h2>
<p>Never create a new VeraCrypt volume by cloning an existing VeraCrypt volume. Always use the VeraCrypt Volume Creation Wizard to create a new VeraCrypt volume. If you clone a volume and then start using both this volume and its clone in a way that both eventually
 contain different data, then you might aid cryptanalysis (both volumes will share a single key set). This is especially critical when the volume contains a hidden volume. Also note that plausible deniability (see section
<a href="Plausible%20Deniability.html"><em>Plausible Deniability</em></a>) is impossible in such cases. See also the chapter
<a href="How%20to%20Back%20Up%20Securely.html">
<em>How to Back Up Securely</em></a>.</p>
</div>
</div><div class="ClearBoth"></div></body></html>
