<!DOCTYPE html>
<html lang="en">
<head>
<meta http-equiv="content-type" content="text/html; charset=utf-8" />
<title>VeraCrypt - Free Open source disk encryption with strong security for the Paranoid</title>
<meta name="description" content="VeraCrypt is free open-source disk encryption software for Windows, Mac OS X and Linux. In case an attacker forces you to reveal the password, VeraCrypt provides plausible deniability. In contrast to file encryption, data encryption performed by VeraCrypt is real-time (on-the-fly), automatic, transparent, needs very little memory, and does not involve temporary unencrypted files."/>
<meta name="keywords" content="encryption, security"/>
<link href="styles.css" rel="stylesheet" type="text/css" />
</head>
<body>

<div>
<a href="Documentation.html"><img src="VeraCrypt128x128.png" alt="VeraCrypt"/></a>
</div>

<div id="menu">
	<ul>
	  <li><a href="Home.html">Home</a></li>
	  <li><a href="Code.html">Source Code</a></li>
	  <li><a href="Downloads.html">Downloads</a></li>
	  <li><a class="active" href="Documentation.html">Documentation</a></li>
	  <li><a href="Donation.html">Donate</a></li>
	  <li><a href="https://sourceforge.net/p/veracrypt/discussion/" target="_blank">Forums</a></li>
	</ul>
</div>

<div>
<p>
<a href="Documentation.html">Documentation</a>
<img src="arrow_right.gif" alt=">>" style="margin-top: 5px">
<a href="Troubleshooting.html">Troubleshooting</a>
</p></div>

<div class="wikidoc">
<h1>Troubleshooting</h1>
<div style="text-align:left; margin-top:19px; margin-bottom:19px; padding-top:0px; padding-bottom:0px">
This section presents possible solutions to common problems that you may run into when using VeraCrypt.</div>
<div style="text-align:left; margin-top:19px; margin-bottom:19px; padding-top:0px; padding-bottom:0px">
Note: If your problem is not listed here, it might be listed in one of the following sections:</div>
<ul style="text-align:left; margin-top:18px; margin-bottom:19px; padding-top:0px; padding-bottom:0px">
<li style="text-align:left; margin-top:0px; margin-bottom:0px; padding-top:0px; padding-bottom:0px">
<a href="Incompatibilities.html" style="text-align:left; color:#0080c0; text-decoration:none">Incompatibilities</a>
</li><li style="text-align:left; margin-top:0px; margin-bottom:0px; padding-top:0px; padding-bottom:0px">
<a href="Issues%20and%20Limitations.html" style="text-align:left; color:#0080c0; text-decoration:none">Known Issues &amp; Limitations</a>
</li><li style="text-align:left; margin-top:0px; margin-bottom:0px; padding-top:0px; padding-bottom:0px">
<a href="FAQ.html" style="text-align:left; color:#0080c0; text-decoration:none">Frequently Asked Questions</a>
</li></ul>
<table style="border-collapse:separate; border-spacing:0px; text-align:left; font-size:11px; line-height:13px; font-family:Verdana,Arial,Helvetica,sans-serif">
<tbody style="text-align:left">
<tr style="text-align:left">
<td style="color:#000000; text-align:left; font-size:11px; line-height:13px; font-family:Verdana,Arial,Helvetica,sans-serif; padding:15px; border:1px solid #000000">
Make sure you use the latest stable version of VeraCrypt. If the problem is caused by a bug in an old version of VeraCrypt, it may have already been fixed. Note: Select
<em style="text-align:left"><strong style="text-align:left">Help</strong></em> &gt;
<em style="text-align:left"><strong style="text-align:left">About</strong></em> to find out which version you use.</td>
</tr>
</tbody>
</table>
<p>&nbsp;</p>
<hr style="text-align:left">
<div style="text-align:left; margin-top:19px; margin-bottom:19px; padding-top:0px; padding-bottom:0px">
<strong style="text-align:left">Problem: </strong></div>
<div style="text-align:left; margin-top:19px; margin-bottom:19px; padding-top:0px; padding-bottom:0px">
<em style="text-align:left">Writing/reading to/from volume is very slow even though, according to the benchmark, the speed of the cipher that I'm using is higher than the speed of the hard drive.</em></div>
<div style="text-align:left; margin-top:19px; margin-bottom:19px; padding-top:0px; padding-bottom:0px">
<strong style="text-align:left">Probable Cause: </strong></div>
<div style="text-align:left; margin-top:19px; margin-bottom:19px; padding-top:0px; padding-bottom:0px">
This is probably caused by an interfering application.</div>
<div style="text-align:left; margin-top:19px; margin-bottom:19px; padding-top:0px; padding-bottom:0px">
<strong style="text-align:left">Possible Solution: </strong></div>
<div style="text-align:left; margin-top:19px; margin-bottom:19px; padding-top:0px; padding-bottom:0px">
First, make sure that your VeraCrypt container does not have a file extension that is reserved for executable files (for example, .exe, .sys, or .dll). If it does, Windows and antivirus software may interfere with the container and adversely affect the performance
 of the volume.</div>
<div style="text-align:left; margin-top:19px; margin-bottom:19px; padding-top:0px; padding-bottom:0px">
Second, disable or uninstall any application that might be interfering, which usually is antivirus software or automatic disk defragmentation tool, etc. In case of antivirus software, it often helps to turn off real-time (on-access) scanning in the preferences
 of the antivirus software. If it does not help, try temporarily disabling the virus protection software. If this does not help either, try uninstalling it completely and restarting your computer subsequently.</div>
<hr style="text-align:left">
<div style="text-align:left; margin-top:19px; margin-bottom:19px; padding-top:0px; padding-bottom:0px">
<strong style="text-align:left">Problem: </strong></div>
<div style="text-align:left; margin-top:19px; margin-bottom:19px; padding-top:0px; padding-bottom:0px">
<em style="text-align:left">VeraCrypt volume cannot be mounted; VeraCrypt reports &quot;</em>Incorrect password or not a VeraCrypt volume<em style="text-align:left">&quot;.</em></div>
<div style="text-align:left; margin-top:19px; margin-bottom:19px; padding-top:0px; padding-bottom:0px">
<strong style="text-align:left">Possible Cause: </strong></div>
<div style="text-align:left; margin-top:19px; margin-bottom:19px; padding-top:0px; padding-bottom:0px">
The volume header may have been damaged by a third-party application or malfunctioning hardware component.</div>
<div style="text-align:left; margin-top:19px; margin-bottom:19px; padding-top:0px; padding-bottom:0px">
<strong style="text-align:left">Possible Solutions: </strong></div>
<ul style="text-align:left; margin-top:18px; margin-bottom:19px; padding-top:0px; padding-bottom:0px">
<li style="text-align:left; margin-top:0px; margin-bottom:0px; padding-top:0px; padding-bottom:0px">
You can try to restore the volume header from the backup embedded in the volume by following these steps:
<ol style="text-align:left; margin-top:18px; margin-bottom:19px; padding-top:0px; padding-bottom:0px">
<li style="text-align:left; margin-top:0px; margin-bottom:0px; padding-top:0px; padding-bottom:0px">
Run VeraCrypt. </li><li style="text-align:left; margin-top:0px; margin-bottom:0px; padding-top:0px; padding-bottom:0px">
Click <em style="text-align:left">Select Device</em> or <em style="text-align:left">
Select File</em> to select your volume. </li><li style="text-align:left; margin-top:0px; margin-bottom:0px; padding-top:0px; padding-bottom:0px">
Select <em style="text-align:left"><a href="Program%20Menu.html#tools-restore-volume-header" style="text-align:left; color:#0080c0; text-decoration:none">Tools &gt; Restore Volume Header</a></em>.
</li></ol>
</li></ul>
<hr style="text-align:left">
<div style="text-align:left; margin-top:19px; margin-bottom:19px; padding-top:0px; padding-bottom:0px">
<strong style="text-align:left">Problem:</strong></div>
<div style="text-align:left; margin-top:19px; margin-bottom:19px; padding-top:0px; padding-bottom:0px">
<em style="text-align:left">After successfully mounting a volume, Windows reports &quot;</em>This device does not contain a valid file system<em style="text-align:left">&quot; or a similar error.</em></div>
<div style="text-align:left; margin-top:19px; margin-bottom:19px; padding-top:0px; padding-bottom:0px">
<strong style="text-align:left">Probable Cause: </strong></div>
<div style="text-align:left; margin-top:19px; margin-bottom:19px; padding-top:0px; padding-bottom:0px">
The file system on the VeraCrypt volume may be corrupted (or the volume is unformatted).</div>
<div style="text-align:left; margin-top:19px; margin-bottom:19px; padding-top:0px; padding-bottom:0px">
<strong style="text-align:left">Possible Solution: </strong></div>
<div style="text-align:left; margin-top:19px; margin-bottom:19px; padding-top:0px; padding-bottom:0px">
You can use filesystem repair tools supplied with your operating system to attempt to repair the filesystem on the VeraCrypt volume. In Windows, it is the '<em style="text-align:left">chkdsk</em>' tool. VeraCrypt provides an easy way to use this tool on a VeraCrypt
 volume: First, make a backup copy of the VeraCrypt volume (because the '<em style="text-align:left">chkdsk</em>' tool might damage the filesystem even more) and then mount it. Right-click the mounted volume in the main VeraCrypt window (in the drive list)
 and from the context menu select '<em style="text-align:left">Repair Filesystem</em>'.</div>
<hr style="text-align:left">
<div id="hidden_volume_small_max_size" style="text-align:left; margin-top:19px; margin-bottom:19px; padding-top:0px; padding-bottom:0px">
<strong style="text-align:left">Problem:</strong></div>
<div style="text-align:left; margin-top:19px; margin-bottom:19px; padding-top:0px; padding-bottom:0px">
<em style="text-align:left">When trying to create a hidden volume, its maximum possible size is unexpectedly small (there is much more free space than this on the outer volume).</em></div>
<div style="text-align:left; margin-top:19px; margin-bottom:19px; padding-top:0px; padding-bottom:0px">
<strong style="text-align:left">Probable Causes: </strong></div>
<ol style="text-align:left; margin-top:18px; margin-bottom:19px; padding-top:0px; padding-bottom:0px">
<li style="text-align:left; margin-top:0px; margin-bottom:0px; padding-top:0px; padding-bottom:0px">
The outer volume has been formatted as NTFS </li><li style="text-align:left; margin-top:0px; margin-bottom:0px; padding-top:0px; padding-bottom:0px">
Fragmentation </li><li style="text-align:left; margin-top:0px; margin-bottom:0px; padding-top:0px; padding-bottom:0px">
Too small cluster size &#43; too many files/folders in the root directory of the outer volume.
</li></ol>
<div style="text-align:left; margin-top:19px; margin-bottom:19px; padding-top:0px; padding-bottom:0px">
<strong style="text-align:left">Possible Solutions: </strong></div>
<div style="text-align:left; margin-top:19px; margin-bottom:19px; padding-top:0px; padding-bottom:0px">
Solution Related to Cause 1:</div>
<blockquote style="text-align:left">
<div style="text-align:left; margin-top:19px; margin-bottom:19px; padding-top:0px; padding-bottom:0px">
Unlike the FAT filesystem, the NTFS filesystem always stores internal data exactly in the middle of the volume. Therefore, the hidden volume can reside only in the second half of the outer volume. If this constraint is unacceptable, do one of the following:</div>
<ul style="text-align:left; margin-top:18px; margin-bottom:19px; padding-top:0px; padding-bottom:0px">
<li style="text-align:left; margin-top:0px; margin-bottom:0px; padding-top:0px; padding-bottom:0px">
Reformat the outer volume as FAT and then create a hidden volume within it. </li><li style="text-align:left; margin-top:0px; margin-bottom:0px; padding-top:0px; padding-bottom:0px">
If the outer volume is too large to be formatted as FAT, split the volume to several 2-terabyte volumes (or 16-terabyte volumes if the device uses 4-kilobyte sectors) and format each of them as FAT.
</li></ul>
</blockquote>
<div style="text-align:left; margin-top:19px; margin-bottom:19px; padding-top:0px; padding-bottom:0px">
Solution Related to Cause 2:</div>
<blockquote style="text-align:left">
<div style="text-align:left; margin-top:19px; margin-bottom:19px; padding-top:0px; padding-bottom:0px">
Create a new outer volume (defragmentation is not a solution, because it would adversely affect plausible deniability &ndash; see section
<a href="Defragmenting.html" style="text-align:left; color:#0080c0; text-decoration:none">
Defragmenting</a>).</div>
</blockquote>
<div style="text-align:left; margin-top:19px; margin-bottom:19px; padding-top:0px; padding-bottom:0px">
Solution Related to Cause 3:</div>
<blockquote style="text-align:left">
<div style="text-align:left; margin-top:19px; margin-bottom:19px; padding-top:0px; padding-bottom:0px">
Note: The following solution applies only to hidden volumes created within FAT volumes.</div>
<div style="text-align:left; margin-top:19px; margin-bottom:19px; padding-top:0px; padding-bottom:0px">
Defragment the outer volume (mount it, right-click its drive letter in the '<em style="text-align:left">Computer</em>' or '<em style="text-align:left">My Computer</em>' window, click
<em style="text-align:left">Properties</em>, select the <em style="text-align:left">
Tools</em> tab, and click '<em style="text-align:left">Defragment Now</em>'). After the volume is defragmented, exit
<em style="text-align:left">Disk Defragmenter</em> and try to create the hidden volume again.
<br style="text-align:left">
<br style="text-align:left">
If this does not help, delete <em style="text-align:left">all</em> files and folders on the outer volume by pressing Shift&#43;Delete, not by formatting, (do not forget to disable the Recycle Bin and System Restore for this drive beforehand) and try creating the
 hidden volume on this <em style="text-align:left">completely empty </em>outer volume again (for testing purposes only). If the maximum possible size of the hidden volume does not change even now, the cause of the problem is very likely an extended root directory.
 If you did not use the '<em style="text-align:left">Default</em>' cluster size (the last step in the Wizard), reformat the outer volume and this time leave the cluster size at '<em style="text-align:left">Default</em>'.</div>
<div style="text-align:left; margin-top:19px; margin-bottom:19px; padding-top:0px; padding-bottom:0px">
If it does not help, reformat the outer volume again and copy less files/folders to its root folder than you did last time. If it does not help, keep reformatting and decreasing the number of files/folders in the root folder. If this is unacceptable or if it
 does not help, reformat the outer volume and select a larger cluster size. If it does not help, keep reformatting and increasing the cluster size, until the problem is solved. Alternatively, try creating a hidden volume within an NTFS volume.</div>
</blockquote>
<hr style="text-align:left">
<div style="text-align:left; margin-top:19px; margin-bottom:19px; padding-top:0px; padding-bottom:0px">
<strong style="text-align:left">Problem: </strong></div>
<div style="text-align:left; margin-top:19px; margin-bottom:19px; padding-top:0px; padding-bottom:0px">
One of the following problems occurs:</div>
<ul style="text-align:left; margin-top:18px; margin-bottom:19px; padding-top:0px; padding-bottom:0px">
<li style="text-align:left; margin-top:0px; margin-bottom:0px; padding-top:0px; padding-bottom:0px">
A VeraCrypt volume cannot be mounted. </li><li style="text-align:left; margin-top:0px; margin-bottom:0px; padding-top:0px; padding-bottom:0px">
NTFS VeraCrypt volumes cannot be created. </li></ul>
<div style="text-align:left; margin-top:19px; margin-bottom:19px; padding-top:0px; padding-bottom:0px">
In addition, the following error may be reported: &quot;<em style="text-align:left">The process cannot access the file because it is being used by another process.</em>&quot;</div>
<div style="text-align:left; margin-top:19px; margin-bottom:19px; padding-top:0px; padding-bottom:0px">
<strong style="text-align:left">Probable Cause: </strong></div>
<div style="text-align:left; margin-top:19px; margin-bottom:19px; padding-top:0px; padding-bottom:0px">
This is probably caused by an interfering application. Note that this is not a bug in VeraCrypt. The operating system reports to VeraCrypt that the device is locked for an exclusive access by an application (so VeraCrypt is not allowed to access it).</div>
<div style="text-align:left; margin-top:19px; margin-bottom:19px; padding-top:0px; padding-bottom:0px">
<strong style="text-align:left">Possible Solution: </strong></div>
<div style="text-align:left; margin-top:19px; margin-bottom:19px; padding-top:0px; padding-bottom:0px">
It usually helps to disable or uninstall the interfering application, which is usually an anti-virus utility, a disk management application, etc.</div>
<hr style="text-align:left">
<div style="text-align:left; margin-top:19px; margin-bottom:19px; padding-top:0px; padding-bottom:0px">
<strong style="text-align:left">Problem: </strong></div>
<div style="text-align:left; margin-top:19px; margin-bottom:19px; padding-top:0px; padding-bottom:0px">
<em style="text-align:left">In the VeraCrypt Boot Loader screen, I'm trying to type my password and/or pressing other keys but the VeraCrypt boot loader is not responding.</em></div>
<div style="text-align:left; margin-top:19px; margin-bottom:19px; padding-top:0px; padding-bottom:0px">
<strong style="text-align:left">Probable Cause: </strong></div>
<div style="text-align:left; margin-top:19px; margin-bottom:19px; padding-top:0px; padding-bottom:0px">
You have a USB keyboard (not a PS/2 keyboard) and pre-boot support for USB keyboards is disabled in your BIOS settings.</div>
<div style="text-align:left; margin-top:19px; margin-bottom:19px; padding-top:0px; padding-bottom:0px">
<strong style="text-align:left">Possible Solution: </strong></div>
<div style="text-align:left; margin-top:19px; margin-bottom:19px; padding-top:0px; padding-bottom:0px">
You need to enable pre-boot support for USB keyboards in your BIOS settings. To do so, follow the below steps:</div>
<div style="text-align:left; margin-top:19px; margin-bottom:19px; padding-top:0px; padding-bottom:0px">
Restart your computer, press F2 or Delete (as soon as you see a BIOS start-up screen), and wait until a BIOS configuration screen appears. If no BIOS configuration screen appears, restart (reset) the computer again and start pressing F2 or Delete repeatedly
 as soon as you restart (reset) the computer. When a BIOS configuration screen appears, enable pre-boot support for USB keyboards. This can typically be done by selecting:
<em style="text-align:left">Advanced</em> &gt; '<em style="text-align:left">USB Configuration</em>' &gt; '<em style="text-align:left">Legacy USB Support</em>' (or '<em style="text-align:left">USB Legacy</em>') &gt;
<em style="text-align:left">Enabled</em>. (Note that the word 'legacy' is in fact misleading, because pre-boot components of modern versions of MS Windows require this option to be enabled to allow user interaction/control.) Then save the BIOS settings (typically
 by pressing F10) and restart your computer. For more information, please refer to the documentation for your BIOS/motherboard or contact your computer vendor's technical support team for assistance.</div>
<hr style="text-align:left">
<div style="text-align:left; margin-top:19px; margin-bottom:19px; padding-top:0px; padding-bottom:0px">
<strong style="text-align:left">Problem: </strong></div>
<div style="text-align:left; margin-top:19px; margin-bottom:19px; padding-top:0px; padding-bottom:0px">
<em style="text-align:left">After the system partition/drive is encrypted, the computer cannot boot after it is restarted (it is also impossible to enter the BIOS configuration screen).</em></div>
<div style="text-align:left; margin-top:19px; margin-bottom:19px; padding-top:0px; padding-bottom:0px">
<strong style="text-align:left">Probable Cause:</strong></div>
<div style="text-align:left; margin-top:19px; margin-bottom:19px; padding-top:0px; padding-bottom:0px">
A bug in the BIOS of your computer.</div>
<div style="text-align:left; margin-top:19px; margin-bottom:19px; padding-top:0px; padding-bottom:0px">
<strong style="text-align:left">Possible Solutions: </strong></div>
<p>Follow these steps:</p>
<ol style="text-align:left; margin-top:18px; margin-bottom:19px; padding-top:0px; padding-bottom:0px">
<li style="text-align:left; margin-top:0px; margin-bottom:0px; padding-top:0px; padding-bottom:0px">
Disconnect the encrypted drive. </li><li style="text-align:left; margin-top:0px; margin-bottom:0px; padding-top:0px; padding-bottom:0px">
Connect an unencrypted drive with an installed operating system (or install it on the drive).
</li><li style="text-align:left; margin-top:0px; margin-bottom:0px; padding-top:0px; padding-bottom:0px">
Upgrade the BIOS. </li><li style="text-align:left; margin-top:0px; margin-bottom:0px; padding-top:0px; padding-bottom:0px">
If it does not help, please report this bug to the manufacturer or vendor of the computer.
</li></ol>
<p>OR</p>
<ul style="text-align:left; margin-top:18px; margin-bottom:19px; padding-top:0px; padding-bottom:0px">
<li style="text-align:left; margin-top:0px; margin-bottom:0px; padding-top:0px; padding-bottom:0px">
If the BIOS/motherboard/computer manufacturer/vendor does not provide any updates that resolve the issue and you use Windows 7 or later and there is an extra boot partition (whose size is less than 1 GB) on the drive, you can try reinstalling Windows without
 this extra boot partition (to work around a bug in the BIOS). </li></ul>
<hr style="text-align:left">
<div style="text-align:left; margin-top:19px; margin-bottom:19px; padding-top:0px; padding-bottom:0px">
<strong style="text-align:left">Problem: </strong></div>
<div style="text-align:left; margin-top:19px; margin-bottom:19px; padding-top:0px; padding-bottom:0px">
One of the following problems occurs:</div>
<ul style="text-align:left; margin-top:18px; margin-bottom:19px; padding-top:0px; padding-bottom:0px">
<li style="text-align:left; margin-top:0px; margin-bottom:0px; padding-top:0px; padding-bottom:0px">
<em style="text-align:left">After the pre-boot authentication password is entered during the system encryption pretest, the computer hangs (after the message '</em>Booting...<em style="text-align:left">' is displayed).</em>
</li><li style="text-align:left; margin-top:0px; margin-bottom:0px; padding-top:0px; padding-bottom:0px">
<em style="text-align:left">When the system partition/drive is encrypted (partially or fully) and the system is restarted for the first time since the process of encryption of the system partition/drive started, the computer hangs after you enter the pre-boot
 authentication password (after the message '</em>Booting...<em style="text-align:left">' is displayed).</em>
</li><li style="text-align:left; margin-top:0px; margin-bottom:0px; padding-top:0px; padding-bottom:0px">
<em style="text-align:left">After the hidden operating system is cloned and the password for it entered, the computer hangs (after the message '</em>Booting...'<em style="text-align:left"> is displayed).</em>
</li></ul>
<div style="text-align:left; margin-top:19px; margin-bottom:19px; padding-top:0px; padding-bottom:0px">
<strong style="text-align:left">Probable Cause: </strong></div>
<div style="text-align:left; margin-top:19px; margin-bottom:19px; padding-top:0px; padding-bottom:0px">
A bug in the BIOS of your computer or an issue with Windows bootloader.</div>
<div style="text-align:left; margin-top:19px; margin-bottom:19px; padding-top:0px; padding-bottom:0px">
<strong style="text-align:left">Possible Solution:</strong></div>
<ul style="text-align:left; margin-top:18px; margin-bottom:19px; padding-top:0px; padding-bottom:0px">
<li style="text-align:left; margin-top:0px; margin-bottom:0px; padding-top:0px; padding-bottom:0px">
Upgrade your BIOS (for information on how to do so, please refer to the documentation for your BIOS/motherboard or contact your computer vendor's technical support team for assistance).
</li><li style="text-align:left; margin-top:0px; margin-bottom:0px; padding-top:0px; padding-bottom:0px">
Use a different motherboard model/brand. </li><li style="text-align:left; margin-top:0px; margin-bottom:0px; padding-top:0px; padding-bottom:0px">
If the BIOS/motherboard/computer manufacturer/vendor does not provide any updates that resolve the issue and you use Windows 7 or later and there is an extra boot partition (whose size is less than 1 GB) on the drive, you can try reinstalling Windows without
 this extra boot partition (to work around a bug in the BIOS). </li><li style="text-align:left; margin-top:0px; margin-bottom:0px; padding-top:0px; padding-bottom:0px">
There two other known workarounds for this issue that require having a Windows Installation disk:
<ul>
<li style="text-align:left; margin-top:0px; margin-bottom:0px; padding-top:0px; padding-bottom:0px">
Boot your machine using a Windows Installation disk and select to repair your computer. Choose &quot;Command Prompt&quot; option and when it opens, type the commands below and then restart your system:
<ul>
<li style="text-align:left; margin-top:0px; margin-bottom:0px; padding-top:0px; padding-bottom:0px">
BootRec /fixmbr </li><li style="text-align:left; margin-top:0px; margin-bottom:0px; padding-top:0px; padding-bottom:0px">
BootRec /FixBoot </li></ul>
</li><li style="text-align:left; margin-top:0px; margin-bottom:0px; padding-top:0px; padding-bottom:0px">
Delete the 100 MB System Reserved partition located at the beginning of your drive, set the system partition next to it as the active partition (both can be done using diskpart utility available in Windows Installation disk repair option). After that, run Startup
 Repair after rebooting on Windows Installation disk. The following link contains detailed instructions:
<a href="https://www.sevenforums.com/tutorials/71363-system-reserved-partition-delete.html" target="_blank">
https://www.sevenforums.com/tutorials/71363-system-reserved-partition-delete.html</a>
</li></ul>
</li></ul>
<hr style="text-align:left">
<div style="text-align:left; margin-top:19px; margin-bottom:19px; padding-top:0px; padding-bottom:0px">
<strong style="text-align:left">Problem: </strong></div>
<div style="text-align:left; margin-top:19px; margin-bottom:19px; padding-top:0px; padding-bottom:0px">
<em style="text-align:left">When trying to encrypt the system partition/drive, during the pretest, the VeraCrypt Boot Loader always reports that the pre-boot authentication password I entered is incorrect (even though I'm sure it is correct).</em></div>
<div style="text-align:left; margin-top:19px; margin-bottom:19px; padding-top:0px; padding-bottom:0px">
<strong style="text-align:left">Possible Causes:</strong></div>
<ul style="text-align:left; margin-top:18px; margin-bottom:19px; padding-top:0px; padding-bottom:0px">
<li style="text-align:left; margin-top:0px; margin-bottom:0px; padding-top:0px; padding-bottom:0px">
Different state of the <em style="text-align:left">Num Lock</em> and/or <em style="text-align:left">
Caps Lock</em> key </li><li style="text-align:left; margin-top:0px; margin-bottom:0px; padding-top:0px; padding-bottom:0px">
Data corruption </li></ul>
<div style="text-align:left; margin-top:19px; margin-bottom:19px; padding-top:0px; padding-bottom:0px">
<strong style="text-align:left">Possible Solution: </strong></div>
<ol style="text-align:left; margin-top:18px; margin-bottom:19px; padding-top:0px; padding-bottom:0px">
<li style="text-align:left; margin-top:0px; margin-bottom:0px; padding-top:0px; padding-bottom:0px">
When you <em style="text-align:left">set</em> a pre-boot authentication password, remember whether the
<em style="text-align:left">Num Lock</em> and <em style="text-align:left">Caps Lock</em> keys are on or off (depending on the manufacturer, the keys may have different labels, such as
<em style="text-align:left">Num LK</em>). Note: You can change the state of each of the keys as desired before you set the password, but you need to remember the states.
</li><li style="text-align:left; margin-top:0px; margin-bottom:0px; padding-top:0px; padding-bottom:0px">
When you enter the password in the VeraCrypt Boot Loader screen, make sure the state of each of the keys is the same as when you set the password.
</li></ol>
<div style="text-align:left; margin-top:19px; margin-bottom:19px; padding-top:0px; padding-bottom:0px">
Note: For other possible solutions to this problem, see the other sections of this chapter.</div>
<hr style="text-align:left">
<div style="text-align:left; margin-top:19px; margin-bottom:19px; padding-top:0px; padding-bottom:0px">
<strong style="text-align:left">Problem:</strong></div>
<div style="text-align:left; margin-top:19px; margin-bottom:19px; padding-top:0px; padding-bottom:0px">
<em style="text-align:left">When the system partition/drive is encrypted, the operating system 'freezes' for approx. 10-60 seconds every 5-60 minutes (100% CPU usage may co-occur).</em></div>
<div style="text-align:left; margin-top:19px; margin-bottom:19px; padding-top:0px; padding-bottom:0px">
<strong style="text-align:left">Probable Cause: </strong></div>
<div style="text-align:left; margin-top:19px; margin-bottom:19px; padding-top:0px; padding-bottom:0px">
A CPU and/or motherboard issue.</div>
<div style="text-align:left; margin-top:19px; margin-bottom:19px; padding-top:0px; padding-bottom:0px">
<strong style="text-align:left">Possible Solutions: </strong></div>
<ul style="text-align:left; margin-top:18px; margin-bottom:19px; padding-top:0px; padding-bottom:0px">
<li style="text-align:left; margin-top:0px; margin-bottom:0px; padding-top:0px; padding-bottom:0px">
Try disabling all power-saving-related features (including any special CPU enhanced halt functions) in the BIOS settings and in the 'Power Options' Windows control panel.
</li><li style="text-align:left; margin-top:0px; margin-bottom:0px; padding-top:0px; padding-bottom:0px">
Replace the processor with a different one (different type and/or brand). </li><li style="text-align:left; margin-top:0px; margin-bottom:0px; padding-top:0px; padding-bottom:0px">
Replace the motherboard with a different one (different type and/or brand). </li></ul>
<hr style="text-align:left">
<div style="text-align:left; margin-top:19px; margin-bottom:19px; padding-top:0px; padding-bottom:0px">
<strong style="text-align:left">Problem:</strong></div>
<div style="text-align:left; margin-top:19px; margin-bottom:19px; padding-top:0px; padding-bottom:0px">
<em style="text-align:left">When mounting or unmounting a VeraCrypt volume, the system crashes (a 'blue screen' error screen appears or the
<span style="text-align:left">computer abruptly restarts)</span>.</em></div>
<div style="text-align:left; margin-top:19px; margin-bottom:19px; padding-top:0px; padding-bottom:0px">
OR</div>
<div style="text-align:left; margin-top:19px; margin-bottom:19px; padding-top:0px; padding-bottom:0px">
<em style="text-align:left">Since I installed VeraCrypt, the operating system has been crashing frequently.</em></div>
<div style="text-align:left; margin-top:19px; margin-bottom:19px; padding-top:0px; padding-bottom:0px">
<strong style="text-align:left">Possible Causes: </strong></div>
<ul style="text-align:left; margin-top:18px; margin-bottom:19px; padding-top:0px; padding-bottom:0px">
<li style="text-align:left; margin-top:0px; margin-bottom:0px; padding-top:0px; padding-bottom:0px">
A bug in a third-party application (e.g. antivirus, system &quot;tweaker&quot;, driver, etc.)
</li><li style="text-align:left; margin-top:0px; margin-bottom:0px; padding-top:0px; padding-bottom:0px">
A bug in VeraCrypt </li><li style="text-align:left; margin-top:0px; margin-bottom:0px; padding-top:0px; padding-bottom:0px">
A bug in Windows or a malfunctioning hardware component </li></ul>
<div style="text-align:left; margin-top:19px; margin-bottom:19px; padding-top:0px; padding-bottom:0px">
<strong style="text-align:left">Possible Solutions: </strong></div>
<ul style="text-align:left; margin-top:18px; margin-bottom:19px; padding-top:0px; padding-bottom:0px">
<li style="text-align:left; margin-top:0px; margin-bottom:0px; padding-top:0px; padding-bottom:0px">
Try disabling any antivirus tools, system &quot;tweakers&quot;, and any other similar applications. If it does not help, try uninstalling them and restarting Windows.
<br style="text-align:left">
<br style="text-align:left">
If the problem persists, run VeraCrypt and select <em style="text-align:left">Help</em> &gt; '<em style="text-align:left">Analyze a System Crash</em>' shortly after the system crashes or restarts. VeraCrypt will then analyze crash dump files that Windows automatically
 created when it crashed (if any). If VeraCrypt determines that a bug in a third party driver is likely to have caused the crash, it will show the name and provider of the driver (note that updating or uninstalling the driver might resolve the issue). Whatever
 the results, you will be able to choose to send us essential information about the system crash to help us determine whether it was caused by a bug in VeraCrypt.
</li></ul>
<hr style="text-align:left">
<div style="text-align:left; margin-top:19px; margin-bottom:19px; padding-top:0px; padding-bottom:0px">
<strong style="text-align:left">Problem:</strong></div>
<div style="text-align:left; margin-top:19px; margin-bottom:19px; padding-top:0px; padding-bottom:0px">
<em style="text-align:left">On Windows 7/Vista (and possibly later versions), the Microsoft Windows Backup tool cannot be used to backup data to a non-system VeraCrypt Volume.</em></div>
<div style="text-align:left; margin-top:19px; margin-bottom:19px; padding-top:0px; padding-bottom:0px">
<strong style="text-align:left">Cause: </strong></div>
<div style="text-align:left; margin-top:19px; margin-bottom:19px; padding-top:0px; padding-bottom:0px">
A bug in the Windows Backup tool.</div>
<div style="text-align:left; margin-top:19px; margin-bottom:19px; padding-top:0px; padding-bottom:0px">
<strong style="text-align:left">Possible Solution: </strong></div>
<ol style="text-align:left; margin-top:18px; margin-bottom:19px; padding-top:0px; padding-bottom:0px">
<li style="text-align:left; margin-top:0px; margin-bottom:0px; padding-top:0px; padding-bottom:0px">
Mount the VeraCrypt volume to which you want to back up data. </li><li style="text-align:left; margin-top:0px; margin-bottom:0px; padding-top:0px; padding-bottom:0px">
Right-click a folder located on the volume (or right-click its drive letter in the '<em style="text-align:left">Computer</em>' list) and select an item from the '<em style="text-align:left">Share with</em>' submenu (on Windows Vista, select '<em style="text-align:left">Share</em>').
</li><li style="text-align:left; margin-top:0px; margin-bottom:0px; padding-top:0px; padding-bottom:0px">
Follow the instructions to share the folder with your user account. </li><li style="text-align:left; margin-top:0px; margin-bottom:0px; padding-top:0px; padding-bottom:0px">
In the Windows Backup tool, select the shared folder (the network location/path) as the destination.
</li><li style="text-align:left; margin-top:0px; margin-bottom:0px; padding-top:0px; padding-bottom:0px">
Start the backup process. </li></ol>
<div style="text-align:left; margin-top:19px; margin-bottom:19px; padding-top:0px; padding-bottom:0px">
Note: The above solution does not apply to the <em style="text-align:left">Starter</em> and
<em style="text-align:left">Home</em> editions of Windows 7 (and possibly later versions).</div>
<hr style="text-align:left">
<div style="text-align:left; margin-top:19px; margin-bottom:19px; padding-top:0px; padding-bottom:0px">
<strong style="text-align:left">Problem:</strong></div>
<div style="text-align:left; margin-top:19px; margin-bottom:19px; padding-top:0px; padding-bottom:0px">
<em style="text-align:left">The label of a filesystem in a VeraCrypt volume cannot be changed from within the 'Computer' window under Windows Vista or a later version of Windows.</em></div>
<div style="text-align:left; margin-top:19px; margin-bottom:19px; padding-top:0px; padding-bottom:0px">
<strong style="text-align:left">Cause: </strong></div>
<div style="text-align:left; margin-top:19px; margin-bottom:19px; padding-top:0px; padding-bottom:0px">
A Windows issue causes the label to be written only to the Windows registry file, instead of being written to the filesystem.</div>
<div style="text-align:left; margin-top:19px; margin-bottom:19px; padding-top:0px; padding-bottom:0px">
<strong style="text-align:left">Possible Solutions: </strong></div>
<ul style="text-align:left; margin-top:18px; margin-bottom:19px; padding-top:0px; padding-bottom:0px">
<li style="text-align:left; margin-top:0px; margin-bottom:0px; padding-top:0px; padding-bottom:0px">
Right-click the mounted volume in the '<em style="text-align:left">Computer</em>' window, select
<em style="text-align:left">Properties</em>, and enter a new label for the volume.
</li></ul>
<hr style="text-align:left">
<div style="text-align:left; margin-top:19px; margin-bottom:19px; padding-top:0px; padding-bottom:0px">
<strong style="text-align:left">Problem: </strong></div>
<div style="text-align:left; margin-top:19px; margin-bottom:19px; padding-top:0px; padding-bottom:0px">
<em style="text-align:left">I cannot encrypt a partition/device because VeraCrypt Volume Creation Wizard says it is in use.</em></div>
<div style="text-align:left; margin-top:19px; margin-bottom:19px; padding-top:0px; padding-bottom:0px">
<strong style="text-align:left">Possible Solution: </strong></div>
<div style="text-align:left; margin-top:19px; margin-bottom:19px; padding-top:0px; padding-bottom:0px">
Close, disable, or uninstall all programs that might be using the partition/device in any way (for example an anti-virus utility). If it does not help, right-click the '<em style="text-align:left">Computer</em>' (or '<em style="text-align:left">My Computer</em>')
 icon on your desktop and select <em style="text-align:left">Manage </em>-&gt;<em style="text-align:left"> Storage
</em>-&gt;<em style="text-align:left"> Disk Management.</em> Then right-click the partition that you want to encrypt, and click
<em style="text-align:left">Change Drive Letter and Paths. </em>Then click <em style="text-align:left">
Remove</em> and <em style="text-align:left">OK. </em>Restart the operating system.</div>
<hr style="text-align:left">
<div style="text-align:left; margin-top:19px; margin-bottom:19px; padding-top:0px; padding-bottom:0px">
<strong style="text-align:left">Problem: </strong></div>
<div style="text-align:left; margin-top:19px; margin-bottom:19px; padding-top:0px; padding-bottom:0px">
<em style="text-align:left">When creating a hidden volume, the Wizard reports that the outer volume cannot be locked.</em></div>
<div style="text-align:left; margin-top:19px; margin-bottom:19px; padding-top:0px; padding-bottom:0px">
<strong style="text-align:left">Probable Cause: </strong></div>
<div style="text-align:left; margin-top:19px; margin-bottom:19px; padding-top:0px; padding-bottom:0px">
The outer volume contains files being used by one or more applications.</div>
<div style="text-align:left; margin-top:19px; margin-bottom:19px; padding-top:0px; padding-bottom:0px">
<strong style="text-align:left">Possible Solution: </strong></div>
<div style="text-align:left; margin-top:19px; margin-bottom:19px; padding-top:0px; padding-bottom:0px">
Close all applications that are using files on the outer volume. If it does not help, try disabling or uninstalling any anti-virus utility you use and restarting the system subsequently.</div>
<hr style="text-align:left">
<div style="text-align:left; margin-top:19px; margin-bottom:19px; padding-top:0px; padding-bottom:0px">
<strong style="text-align:left">Problem: </strong></div>
<div style="text-align:left; margin-top:19px; margin-bottom:19px; padding-top:0px; padding-bottom:0px">
When accessing a file-hosted container shared over a network, you receive one or both of the following error messages:
<br style="text-align:left">
&quot;<em style="text-align:left">Not enough server storage is available to process this command.</em>&quot; and/or,<br style="text-align:left">
&quot;<em style="text-align:left">Not enough memory to complete transaction.</em>&quot;</div>
<div style="text-align:left; margin-top:19px; margin-bottom:19px; padding-top:0px; padding-bottom:0px">
<strong style="text-align:left">Probable Cause: </strong></div>
<div style="text-align:left; margin-top:19px; margin-bottom:19px; padding-top:0px; padding-bottom:0px">
<em style="text-align:left">IRPStackSize</em> in the Windows registry may have been set to a too small value.</div>
<div style="text-align:left; margin-top:19px; margin-bottom:19px; padding-top:0px; padding-bottom:0px">
<strong style="text-align:left">Possible Solution: </strong></div>
<div style="text-align:left; margin-top:19px; margin-bottom:19px; padding-top:0px; padding-bottom:0px">
Locate the <em style="text-align:left">IRPStackSize </em>key in the Windows registry and set it to a higher value. Then restart the system. If the key does not exist in the Windows registry, create it at
<em style="text-align:left">HKEY_LOCAL_MACHINE\SYSTEM\CurrentControlSet\Services\LanmanServer\Parameters</em> and set its value to 16 or higher. Then restart the system. For more information, see:
<a href="https://support.microsoft.com/kb/285089/" style="text-align:left; color:#0080c0; text-decoration:none">
https://support.microsoft.com/kb/285089/ </a>and <a href="https://support.microsoft.com/kb/177078/" style="text-align:left; color:#0080c0; text-decoration:none">
https://support.microsoft.com/kb/177078/</a></div>
<hr style="text-align:left">
<p><br style="text-align:left">
<br style="text-align:left">
<br style="text-align:left">
<br style="text-align:left">
&nbsp;&nbsp;See also: <a href="Issues%20and%20Limitations.html" style="text-align:left; color:#0080c0; text-decoration:none; font-weight:bold">
Known Issues &amp; Limitations</a>,&nbsp;&nbsp;<a href="Incompatibilities.html" style="text-align:left; color:#0080c0; text-decoration:none; font-weight:bold">Incompatibilities</a></p>
</div><div class="ClearBoth"></div></body></html>
