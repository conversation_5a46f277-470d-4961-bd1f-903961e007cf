<!DOCTYPE html>
<html lang="en">
<head>
<meta http-equiv="content-type" content="text/html; charset=utf-8" />
<title>VeraCrypt - Free Open source disk encryption with strong security for the Paranoid</title>
<meta name="description" content="VeraCrypt is free open-source disk encryption software for Windows, Mac OS X and Linux. In case an attacker forces you to reveal the password, VeraCrypt provides plausible deniability. In contrast to file encryption, data encryption performed by VeraCrypt is real-time (on-the-fly), automatic, transparent, needs very little memory, and does not involve temporary unencrypted files."/>
<meta name="keywords" content="encryption, security"/>
<link href="styles.css" rel="stylesheet" type="text/css" />
</head>
<body>

<div>
<a href="Documentation.html"><img src="VeraCrypt128x128.png" alt="VeraCrypt"/></a>
</div>

<div id="menu">
	<ul>
	  <li><a href="Home.html">Home</a></li>
	  <li><a href="Code.html">Source Code</a></li>
	  <li><a href="Downloads.html">Downloads</a></li>
	  <li><a class="active" href="Documentation.html">Documentation</a></li>
	  <li><a href="Donation.html">Donate</a></li>
	  <li><a href="https://sourceforge.net/p/veracrypt/discussion/" target="_blank">Forums</a></li>
	</ul>
</div>

<div>
<p>
<a href="Documentation.html">Documentation</a>
<img src="arrow_right.gif" alt=">>" style="margin-top: 5px">
<a href="Security%20Requirements%20and%20Precautions.html">Security Requirements and Precautions</a>
<img src="arrow_right.gif" alt=">>" style="margin-top: 5px">
<a href="VeraCrypt%20RAM%20Encryption.html">VeraCrypt RAM Encryption</a>
</p></div>

<div class="wikidoc">
<h1>VeraCrypt RAM Encryption Mechanism</h1>

<h2>Introduction</h2>

<p>
    VeraCrypt RAM Encryption aims to protect disk encryption keys stored in volatile memory against certain types of attacks. The primary objectives of this mechanism are:
    </p><ul>
        <li>To protect against cold boot attacks.</li>
        <li>To add an obfuscation layer to significantly complicate the recovery of encryption master keys from memory dumps, be it live or offline.</li>
    </ul>
<p></p>

<h3>Implementation Overview</h3>

<p>Here's a summary of how RAM encryption is achieved:</p>
<ol>
    <li>At Windows startup, the VeraCrypt driver allocates a 1MiB memory region. If this fails, we device the size by two until allocation succeeds (minimal size being 8KiB). All these variables are allocated in non-paged Kernel memory space.</li>
    <li>This memory region is then populated with random bytes generated by a CSPRNG based on ChaCha20.</li>
    <li>Two random 64-bit integers, <code>HashSeedMask</code> and <code>CipherIVMask</code>, are generated.</li>
    <li>For every master key of a volume, the RAM encryption algorithm derives a unique key from a combination of the memory region and unique values extracted from the memory to be encrypted. This ensures a distinct key for each encrypted memory region. The use of location-dependent keys and IVs prevents master keys from being easily extracted from memory dumps.</li>
    <li>The master keys are decrypted for every request, requiring a fast decryption algorithm. For this, ChaCha12 is utilized.</li>
    <li>Once a volume is mounted, its master keys are immediately encrypted using the described algorithm.</li>
    <li>For each I/O request for a volume, the master keys are decrypted only for the duration of that request and then securely wiped.</li>
    <li>Upon volume unmounting, the encrypted master keys are securely removed from memory.</li>
    <li>At Windows shutdown or reboot, the memory region allocated during startup is securely wiped.</li>
</ol>

<h3>Protection against Cold Boot Attacks</h3>

<p>
    The mitigation of cold boot attacks is achieved by utilizing a large memory page for key derivation. This ensures that attackers cannot recover the master key since parts of this large memory area would likely be corrupted and irrecoverable after shutdown. Further details on cold boot attacks and mitigation techniques can be found in the referenced papers:
</p>
<ul>
    <li><a href="https://www.blackhat.com/presentations/bh-usa-08/McGregor/BH_US_08_McGregor_Cold_Boot_Attacks.pdf" target="_blank">Cold Boot Attacks (BlackHat)</a></li>
    <li><a href="https://www.grc.com/sn/files/RAM_Hijacks.pdf" target="_blank">RAM Hijacks</a></li>
</ul>

<h3>Incompatibility with Windows Hibernate and Fast Startup</h3>
<p>
	RAM Encryption in VeraCrypt is not compatible with the Windows Hibernate and Fast Startup features. Before activating RAM Encryption, these features will be disabled by VeraCrypt to ensure the security and functionality of the encryption mechanism.

</p>

<h3>Algorithm Choices</h3>

<p>
    The choice of algorithms was based on a balance between security and performance:
</p>
<ul>
    <li><strong>t1ha2:</strong> A non-cryptographic hash function chosen for its impressive speed and ability to achieve GiB/s hashing rates. This is vital since keys are derived from a 1MB memory region for every encryption/decryption request. It also respects the strict avalanche criteria which is crucial for this use case.</li>
    <li><strong>ChaCha12:</strong> Chosen over ChaCha20 for performance reasons, it offers sufficient encryption strength while maintaining fast encryption/decryption speeds.</li>
</ul>

<h3>Key Algorithms</h3>

<p>
    Two core algorithms are fundamental to the RAM encryption process:
</p>

<h4>1. VcGetEncryptionID</h4>

<p>
    Computes a unique ID for the RAM buffer set to be encrypted.
</p>
<pre>    <code>
    - Input: pCryptoInfo, a CRYPTO_INFO variable to encrypt/decrypt
    - Output: A 64-bit integer identifying the pCryptoInfo variable
    - Steps:
      - Compute the sum of the virtual memory addresses of the fields ks and ks2 of pCryptoInfo: encID = ((uint64) pCryptoInfo-&gt;ks) + ((uint64) pCryptoInfo-&gt;ks2)
      - Return the result
    </code>
</pre>

<h4>2. VcProtectMemory</h4>

<p>
    Encrypts the RAM buffer using the unique ID generated by VcGetEncryptionID.
</p>
<pre>    <code>
    - Input:
      - encID, unique ID for memory to be encrypted
      - pbData, pointer to the memory to be encrypted
      - pbKeyDerivationArea, memory area allocated by the driver at startup
      - HashSeedMask and CipherIVMask, two 64-bit random integers from startup
    - Output:
      - None; memory at pbData is encrypted in place
    - Steps:
      - Derive hashSeed: hashSeed = (((uint64) pbKeyDerivationArea) + encID) ^ HashSeedMask
      - Compute 128-bit hash: hash128 = t1h2 (pbKeyDerivationArea,hashSeed). 
      - Decompose hash128 into two 64-bit integers: hash128 = hash128_1 || hash128_2
      - Create a 256-bit key for ChaCha12: chachaKey = hash128_1 || hash128_2 || (hash128_1 OR hash128_2) || (hash128_1 + hash128_2)
      - Encrypt chachaKey by itself using ChaCha12 using hashSeed as an IV: ChaCha256Encrypt (chachaKey, hashSeed, chachaKey)
      - Derive the 64-bit IV for ChaCha12: chachaIV = (((uint64) pbKeyDerivationArea) + encID) ^ CipherIVMask
      - Encrypt memory at pbData using ChaCha12: ChaCha256Encrypt (chachaKey, chachaIV, pbData)
      - Securely erase temporary values
    </code>
</pre>

<p>
    It's important to note that, due to ChaCha12 being a stream cipher, encryption and decryption processes are identical, and the <code>VcProtectMemory</code> function can be used for both.
</p>

<p>
    For a deeper understanding and a look into the codebase, one can visit the VeraCrypt repository and explore the mentioned functions in the <code>src/Common/Crypto.c</code> file.
</p>

<h3>Additional Security Measures</h3>

<p>
    Starting from version 1.24, VeraCrypt has integrated a mechanism that detects the insertion of new devices into the system when System Encryption is active. If a new device is inserted, master keys are immediately purged from memory, resulting in a Windows BSOD. This protects against attacks using specialized devices to extract memory from running systems. However, for maximum efficiency, this feature should be paired with RAM encryption.<br>
	To enable this feature, navigate to the menu System -> Settings and check the <b>"Clear encryption keys from memory if a new device is inserted"</b> option.
</p>

<h3>Technical Limitations with Hibernation and Fast Startup</h3>
<p>
The Windows Hibernate and Fast Startup features save the content of RAM to the hard drive. In the context of VeraCrypt's RAM Encryption, supporting these features presents a significant challenge, namely a chicken-egg problem.<br>
To maintain security, the large memory region used for key derivation in RAM Encryption would have to be stored in an encrypted format, separate from the usual VeraCrypt encryption applied to the current drive. This separate encrypted storage must also be unlockable using the same password as the one used for Pre-Boot Authentication. Moreover, this process must happen early in the boot sequence before filesystem access is available, necessitating the raw storage of encrypted data in specific sectors of a different disk.<br>
While this is technically feasible, the complexity and user-unfriendliness of such a solution make it impractical for standard deployments. Therefore, enabling RAM Encryption necessitates the disabling of the Windows Hibernate and Fast Startup features.<br>
</p>

</div><div class="ClearBoth"></div></body></html>
