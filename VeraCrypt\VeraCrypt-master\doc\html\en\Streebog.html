<!DOCTYPE html>
<html lang="en">
<head>
<meta http-equiv="content-type" content="text/html; charset=utf-8" />
<title>VeraCrypt - Free Open source disk encryption with strong security for the Paranoid</title>
<meta name="description" content="VeraCrypt is free open-source disk encryption software for Windows, Mac OS X and Linux. In case an attacker forces you to reveal the password, VeraCrypt provides plausible deniability. In contrast to file encryption, data encryption performed by VeraCrypt is real-time (on-the-fly), automatic, transparent, needs very little memory, and does not involve temporary unencrypted files."/>
<meta name="keywords" content="encryption, security"/>
<link href="styles.css" rel="stylesheet" type="text/css" />
</head>
<body>

<div>
<a href="Documentation.html"><img src="VeraCrypt128x128.png" alt="VeraCrypt"/></a>
</div>

<div id="menu">
	<ul>
	  <li><a href="Home.html">Home</a></li>
	  <li><a href="Code.html">Source Code</a></li>
	  <li><a href="Downloads.html">Downloads</a></li>
	  <li><a class="active" href="Documentation.html">Documentation</a></li>
	  <li><a href="Donation.html">Donate</a></li>
	  <li><a href="https://sourceforge.net/p/veracrypt/discussion/" target="_blank">Forums</a></li>
	</ul>
</div>

<div>
<p>
<a href="Documentation.html">Documentation</a>
<img src="arrow_right.gif" alt=">>" style="margin-top: 5px">
<a href="Hash%20Algorithms.html">Hash Algorithms</a>
<img src="arrow_right.gif" alt=">>" style="margin-top: 5px">
<a href="Streebog.html">Streebog</a>
</p></div>

<div class="wikidoc">
<h1>Streebog</h1>
<p>Streebog is a family of two hash algorithms, Streebog-256 and Streebog-512, defined in the Russian national standard&nbsp;<a href="https://www.tc26.ru/research/polozhenie/GOST_R_34_11-2012_eng.pdf">GOST R 34.11-2012</a> Information Technology - Cryptographic
 Information Security - Hash Function. It is also described in <a href="https://tools.ietf.org/html/rfc6986">
RFC 6986</a>. It is the competitor of NIST SHA-3 standard.</p>
<p>VeraCrypt uses only Streebog-512 which has an output size of 512 bits.</p>
</div>
</body></html>
