<!DOCTYPE html>
<html lang="en">
<head>
<meta http-equiv="content-type" content="text/html; charset=utf-8" />
<title>VeraCrypt - Free Open source disk encryption with strong security for the Paranoid</title>
<meta name="description" content="VeraCrypt is free open-source disk encryption software for Windows, Mac OS X and Linux. In case an attacker forces you to reveal the password, VeraCrypt provides plausible deniability. In contrast to file encryption, data encryption performed by VeraCrypt is real-time (on-the-fly), automatic, transparent, needs very little memory, and does not involve temporary unencrypted files."/>
<meta name="keywords" content="encryption, security"/>
<link href="styles.css" rel="stylesheet" type="text/css" />
</head>
<body>

<div>
<a href="Documentation.html"><img src="VeraCrypt128x128.png" alt="VeraCrypt"/></a>
</div>

<div id="menu">
	<ul>
	  <li><a href="Home.html">Home</a></li>
	  <li><a href="Code.html">Source Code</a></li>
	  <li><a href="Downloads.html">Downloads</a></li>
	  <li><a class="active" href="Documentation.html">Documentation</a></li>
	  <li><a href="Donation.html">Donate</a></li>
	  <li><a href="https://sourceforge.net/p/veracrypt/discussion/" target="_blank">Forums</a></li>
	</ul>
</div>

<div>
<p>
<a href="Documentation.html">Documentation</a>
<img src="arrow_right.gif" alt=">>" style="margin-top: 5px" />
<a href="Encryption%20Algorithms.html">Encryption Algorithms</a>
<img src="arrow_right.gif" alt=">>" style="margin-top: 5px" />
<a href="SM4.html">SM4</a>
</p>
</div>

<div class="wikidoc">
  <h1>SM4</h1>

  <div style="text-align:left; margin-top:19px; margin-bottom:19px; padding-top:0px; padding-bottom:0px">
    SM4 is a block cipher standard published by the Chinese National Cryptography Administration in 2006 as part of the GB/T 32907-2016 standard. 
    It is the national encryption standard for securing wireless communications (e.g., Wi-Fi) and other secure communication systems within China. 
    SM4 operates on 128-bit data blocks and uses a 128-bit encryption key.
  </div>

  <div style="text-align:left; margin-top:19px; margin-bottom:19px; padding-top:0px; padding-bottom:0px">
    The SM4 algorithm uses 32 rounds of encryption operations based on a combination of non-linear substitution, linear transformation, and key addition. 
    Its structure is similar in some respects to other modern block ciphers, utilizing an S-box for substitution and a linear transformation for diffusion.
  </div>

  <div style="text-align:left; margin-top:19px; margin-bottom:19px; padding-top:0px; padding-bottom:0px">
    SM4 has been officially standardized by ISO/IEC as part of ISO/IEC 18033-3:2010 through Amendment 1, published in 2021. 
    It is widely deployed in government, banking, and commercial applications across China. 
    It is optimized for both software and hardware implementations, providing strong security with relatively high performance.
  </div>

  <div style="text-align:left; margin-top:19px; margin-bottom:19px; padding-top:0px; padding-bottom:0px">
    VeraCrypt uses SM4 with 32 rounds and a 128-bit key operating in 
    <a href="Modes%20of%20Operation.html" style="text-align:left; color:#0080c0; text-decoration:none">XTS mode</a>
    (see the section 
    <a href="Modes%20of%20Operation.html" style="text-align:left; color:#0080c0; text-decoration:none">Modes of Operation</a>).
  </div>

  <div style="text-align:left; margin-top:19px; margin-bottom:19px; padding-top:0px; padding-bottom:0px">
    <a href="Twofish.html" style="text-align:left; color:#0080c0; text-decoration:none; font-weight:bold">Next Section &gt;&gt;</a>
  </div>
</div>

<div class="ClearBoth"></div>
</body>
</html>
