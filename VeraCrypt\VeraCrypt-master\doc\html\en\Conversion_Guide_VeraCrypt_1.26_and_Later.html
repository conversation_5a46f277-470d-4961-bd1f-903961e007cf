<!DOCTYPE html>
<html lang="en">
<head>
<meta http-equiv="content-type" content="text/html; charset=utf-8" />
<title>VeraCrypt - Conversion Guide for Versions 1.26 and Later</title>
<meta name="description" content="Guide on how to handle deprecated features and conversion of TrueCrypt volumes in VeraCrypt versions 1.26 and later."/>
<meta name="keywords" content="VeraCrypt, TrueCrypt, Conversion, Encryption, Security"/>
<link href="styles.css" rel="stylesheet" type="text/css" />
</head>
<body>

<div>
<a href="Documentation.html"><img src="VeraCrypt128x128.png" alt="VeraCrypt"/></a>
</div>

<div id="menu">
	<ul>
	  <li><a href="Home.html">Home</a></li>
	  <li><a href="Code.html">Source Code</a></li>
	  <li><a href="Downloads.html">Downloads</a></li>
	  <li><a class="active" href="Documentation.html">Documentation</a></li>
	  <li><a href="Donation.html">Donate</a></li>
	  <li><a href="https://sourceforge.net/p/veracrypt/discussion/" target="_blank">Forums</a></li>
	</ul>
</div>

<div>
<p>
<a href="Documentation.html">Documentation</a>
<img src="arrow_right.gif" alt=">>" style="margin-top: 5px">
<a href="Conversion_Guide_VeraCrypt_1.26_and_Later.html">Conversion Guide for Versions 1.26 and Later</a>
</p></div>

<div class="wikidoc">
<h1>Conversion Guide for VeraCrypt 1.26 and Later</h1>

<h2>1. Introduction</h2>
<p>Version 1.26 and newer of VeraCrypt have introduced significant changes by removing support for certain features. If you encounter issues while mounting volumes, this guide will help you understand and resolve them.</p>

<h2>2. Deprecated Features in VeraCrypt 1.26 and Later</h2>
<p>The following features have been deprecated:</p>
<ul>
<li>TrueCrypt Mode</li>
<li>HMAC-RIPEMD-160 Hash Algorithm</li>
<li>GOST89 Encryption Algorithm</li>
</ul>
<p>If you experience mounting errors with volumes created in VeraCrypt 1.25.9 or older, use VeraCrypt 1.25.9 to check if these deprecated features are in use. Highlight the volume and click on "Volume Properties" in the GUI to check.</p>

<h2>3. Remediation Procedures Based on Version</h2>

<h3>3.1 Scenario 1: Using VeraCrypt 1.25.9 or Older</h3>
<p>If you are using or can upgrade to VeraCrypt 1.25.9, follow these steps:</p>
<ul>
<li>Convert TrueCrypt Volumes to VeraCrypt Volumes</li>
<li>Change from Deprecated HMAC-RIPEMD-160 Hash Algorithm</li>
<li>Recreate VeraCrypt Volume if Using GOST89 Encryption Algorithm</li>
</ul>
<p>Download the 1.25.9 version <a href="https://veracrypt.jp/en/Downloads_1.25.9.html">here</a>.</p>

<h3>3.2 Scenario 2: Upgraded to VeraCrypt 1.26 or Newer</h3>
<p>If you have already upgraded to VeraCrypt 1.26 or newer, follow these steps:</p>
<ul>
<li>Convert TrueCrypt Volumes to VeraCrypt Volumes</li>
<li>Change from Deprecated HMAC-RIPEMD-160 Hash Algorithm</li>
</ul>
<p>If you are on Linux or Mac, temporarily downgrade to VeraCrypt 1.25.9. Windows users can use the VCPassChanger tool <a href="https://launchpad.net/veracrypt/trunk/1.25.9/+download/VCPassChanger_%28TrueCrypt_Convertion%29.zip">that can be downloaded from here</a>.</p>
<ul>
<li>Recreate VeraCrypt Volume if Using GOST89 Encryption Algorithm</li>
</ul>
All OSes temporarily downgrade to 1.25.9 version.
<h2>4. Conversion and Remediation Procedures</h2>

<h3>4.1 Converting TrueCrypt Volumes to VeraCrypt</h3>
<p>TrueCrypt file containers and partitions created with TrueCrypt versions 6.x or 7.x can be converted to VeraCrypt using VeraCrypt 1.25.9 or the VCPassChanger tool on Windows. For more details, refer to the <a href="Converting%20TrueCrypt%20volumes%20and%20partitions.html">documentation</a>.</p>
<p>After conversion, the file extension will remain as <code>.tc</code>. Manually change it to <code>.hc</code> if you want VeraCrypt 1.26 or newer to automatically recognize it.</p>

<h3>4.2 Changing Deprecated HMAC-RIPEMD-160 Hash Algorithm</h3>
<p>Use the "Set Header Key Derivation Algorithm" feature to change the HMAC-RIPEMD-160 hash algorithm to one supported in VeraCrypt 1.26. Refer to the <a href="Hash%20Algorithms.html">documentation</a> for more details.</p>

<h3>4.3 Recreating VeraCrypt Volume if Using GOST89 Encryption Algorithm</h3>
<p>If your volume uses the GOST89 encryption algorithm, you will need to copy your data elsewhere and recreate the volume using a supported encryption algorithm. More details are available in the <a href="Encryption%20Algorithms.html">encryption algorithm documentation</a>.</p>

<h2>5. Important Notes</h2>
<p><strong>Note to users who created volumes with VeraCrypt 1.17 or earlier:</strong></p>
<blockquote>
<p>To avoid revealing whether your volumes contain a hidden volume or not, or if you rely on plausible deniability, you must recreate both the outer and hidden volumes, including system encryption and hidden OS. Discard existing volumes created prior to VeraCrypt 1.18a.</p>
</blockquote>

<p>For more information, visit:</p>
<ul>
<li><a href="TrueCrypt%20Support.html">TrueCrypt Support</a></li>
<li><a href="Converting%20TrueCrypt%20volumes%20and%20partitions.html">Converting TrueCrypt Volumes and Partitions</a></li>
</ul>

</div>

<div class="ClearBoth"></div>
</body>
</html>
