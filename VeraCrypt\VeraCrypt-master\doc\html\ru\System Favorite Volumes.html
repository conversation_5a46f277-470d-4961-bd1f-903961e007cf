﻿<!DOCTYPE html>
<html lang="ru">
<head>
<meta http-equiv="content-type" content="text/html; charset=utf-8" />
<title>VeraCrypt - Бесплатное надёжное шифрование дисков с открытым исходным кодом</title>
<meta name="description" content="VeraCrypt это бесплатное программное обеспечение для шифрования дисков с открытым исходным кодом для Windows, Mac OS X (macOS) и Linux. В случае, если злоумышленник вынуждает вас раскрыть пароль, VeraCrypt обеспечивает правдоподобное отрицание наличия шифрования. В отличие от пофайлового шифрования, VeraCrypt шифрует данные в реальном времени (на лету), автоматически, прозрачно, требует очень мало памяти и не использует временные незашифрованные файлы."/>
<meta name="keywords" content="encryption, security, шифрование, безопасность"/>
<link href="styles.css" rel="stylesheet" type="text/css" />
</head>
<body>

<div>
<a href="Documentation.html"><img src="VeraCrypt128x128.png" alt="VeraCrypt"/></a>
</div>

<div id="menu">
	<ul>
	  <li><a href="Home.html">Начало</a></li>
	  <li><a href="Code.html">Исходный код</a></li>
	  <li><a href="Downloads.html">Загрузить</a></li>
	  <li><a class="active" href="Documentation.html">Документация</a></li>
	  <li><a href="Donation.html">Поддержать разработку</a></li>
	  <li><a href="https://sourceforge.net/p/veracrypt/discussion/" target="_blank">Форум</a></li>
	</ul>
</div>

<div>
<p>
<a href="Documentation.html">Документация</a>
<img src="arrow_right.gif" alt=">>" style="margin-top: 5px">
<a href="VeraCrypt%20Volume.html">Том VeraCrypt</a>
<img src="arrow_right.gif" alt=">>" style="margin-top: 5px">
<a href="System%20Favorite%20Volumes.html">Системные избранные тома</a>
</p></div>

<div class="wikidoc">
<h1>Системные избранные тома</h1>
<div style="text-align:left; margin-top:19px; margin-bottom:19px; padding-top:0px; padding-bottom:0px">
<p>Системные избранные тома удобны, например, в следующих ситуациях:</p>
<ul>
<li>у вас есть тома, которые нужно <strong>монтировать до загрузки системы и служб приложений и
перед входом пользователей в систему</strong>;</li>
<li>у вас есть общие сетевые папки, расположенные в томах VeraCrypt; настроив такие тома как системные
избранные, вы тем самым гарантируете, что <strong>общие сетевые ресурсы будут автоматически восстанавливаться</strong>
операционной системой при каждой перезагрузке;</li>
<li>вам нужно, чтобы такой том монтировался на <strong>одну и ту же букву диска</strong>
при каждой загрузке операционной системы. </li></ul>
<p>Обратите внимание, что в отличие от обычных (несистемных) избранных томов, <strong>системные избранные
тома используют пароль предзагрузочной аутентификации</strong>, поэтому необходимо, чтобы системный
раздел/диск был зашифрован (также примите к сведению, что включать кэширование пароля предзагрузочной
аутентификации не требуется). Кроме того, поскольку предзагрузочный пароль набирается с использованием
<strong>американской раскладки клавиатуры</strong> (это требование BIOS), при создании системного избранного
тома пароль необходимо ввести, используя <strong>американскую раскладку клавиатуры</strong>, нажимая
те же клавиши, что и при вводе пароля предзагрузочной аутентификации. Если пароль системного избранного
тома не идентичен паролю предзагрузочной аутентификации при американской раскладке клавиатуры, то
<strong>он не будет смонтирован</strong>.</p>
<p>При создании тома, который вы хотите позже сделать системным избранным, необходимо явно установить
раскладку клавиатуры, связанную с VeraCrypt, на раскладку США, и нажимать те же клавиши, что и при вводе
пароля предзагрузочной аутентификации.<br>
<br>
Системные избранные тома можно настроить так, чтобы они были <strong>доступны из VeraCrypt только
пользователям с правами администратора</strong> (выберите <em>Настройки</em> &gt; <em>Системные избранные
тома</em> &gt; <em>Просматривать/размонтировать системные избранные тома могут лишь администраторы</em>).
Этот параметр следует включать на серверах, чтобы предотвратить возможность размонтирования системных
избранных томов пользователями без административных привилегий. На не серверных системах этот параметр
можно использовать для того, чтобы системные избранные тома не влияли на действия с обычными томами
(<em>Размонтировать все</em>, авторазмонтирование и т. д.). Кроме того, при запуске VeraCrypt без прав
администратора (а это стандартное поведение в Vista и более новых версиях Windows), системные избранные
тома не отображаются в списке букв дисков в главном окне VeraCrypt.</p>
<h3>Чтобы сконфигурировать том VeraCrypt как системный избранный, сделайте следующее:</h3>
<ol>
<li>Смонтируйте том (на ту букву, на которую нужно, чтобы он монтировался всегда).</li>
<li>В главном окне VeraCrypt щёлкните правой кнопкой мыши на смонтированном томе и выберите команду
<em>Добавить в системные избранные</em>.</li>
<li>В появившемся окне упорядочивания системных избранных томов включите параметр <em>Монтировать системные
избранные тома при старте Windows</em> и нажмите <em>OK</em>.</li></ol>
<p>Системные избранные тома отображаются в окне упорядочивания избранного (<em>Избранное</em> &gt;
<em>Упорядочить системные избранные тома</em>) в <strong>порядке их монтирования</strong>. Порядок
отображения томов изменяется кнопками <em>Выше</em> и <em>Ниже</em>.</p>
<p id="Label">Каждому системному избранному тому можно присвоить особую метку. Эта метка – не то же самое,
что метка тома в файловой системе, она отображается внутри интерфейса VeraCrypt вместо пути тома. Чтобы
присвоить тому метку, сделайте следующее:</p>
<ol>
<li>Выберите <em>Избранное</em> &gt; <em>Упорядочить системные избранные тома</em>.</li>
<li>В появившемся окне упорядочивания системных избранных томов выберите том, метку которого вы хотите
отредактировать.</li>
<li>Введите метку в поле <em>Метка выбранного избранного тома</em> и нажмите OK.
</li></ol>
<p>Обратите внимание, что в окне упорядочивания системных избранных томов (<em>Избранное</em> &gt;
<em>Упорядочить системные избранные тома</em>) можно <strong>настроить ряд других параметров для каждого
системного избранного тома</strong>. Например, любой том можно монтировать как доступный только для
чтения или как сменный носитель.<br>
<br>
ВНИМАНИЕ: Если присвоенная системному избранному тому (сохранённая в конфигурационном файле) буква диска
занята, данный том не будет смонтирован, при этом сообщение об ошибке не выводится.<br>
<br>
Обратите внимание, что Windows требуется использовать ряд файлов (например, файлы подкачки, файлы Active
Directory и др.) до того, как будут смонтированы системные избранные тома. Поэтому такие файлы нельзя
хранить на системных избранных томах. Тем не менее их <em>можно</em> хранить на любом разделе, который
входит в область шифрования системы (например, на системном разделе или на любом разделе системного диска,
целиком зашифрованного с помощью VeraCrypt).<br>
<br>
Чтобы <strong>удалить том из списка системных избранных</strong>, выберите <em>Избранное</em> &gt;
<em>Упорядочить системные избранные тома</em>, выделите нужный том, нажмите кнопку
<em>Убрать</em> и затем нажмите OK.</p>
</div>
</div>

</body></html>
