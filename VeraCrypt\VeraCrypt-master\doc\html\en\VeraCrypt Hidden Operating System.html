<!DOCTYPE html>
<html lang="en">
<head>
<meta http-equiv="content-type" content="text/html; charset=utf-8" />
<title>VeraCrypt - Free Open source disk encryption with strong security for the Paranoid</title>
<meta name="description" content="VeraCrypt is free open-source disk encryption software for Windows, Mac OS X and Linux. In case an attacker forces you to reveal the password, VeraCrypt provides plausible deniability. In contrast to file encryption, data encryption performed by VeraCrypt is real-time (on-the-fly), automatic, transparent, needs very little memory, and does not involve temporary unencrypted files."/>
<meta name="keywords" content="encryption, security"/>
<link href="styles.css" rel="stylesheet" type="text/css" />
</head>
<body>

<div>
<a href="Documentation.html"><img src="VeraCrypt128x128.png" alt="VeraCrypt"/></a>
</div>

<div id="menu">
	<ul>
	  <li><a href="Home.html">Home</a></li>
	  <li><a href="Code.html">Source Code</a></li>
	  <li><a href="Downloads.html">Downloads</a></li>
	  <li><a class="active" href="Documentation.html">Documentation</a></li>
	  <li><a href="Donation.html">Donate</a></li>
	  <li><a href="https://sourceforge.net/p/veracrypt/discussion/" target="_blank">Forums</a></li>
	</ul>
</div>

<div>
<p>
<a href="Documentation.html">Documentation</a>
<img src="arrow_right.gif" alt=">>" style="margin-top: 5px">
<a href="Plausible%20Deniability.html">Plausible Deniability</a>
<img src="arrow_right.gif" alt=">>" style="margin-top: 5px">
<a href="VeraCrypt%20Hidden%20Operating%20System.html">Hidden Operating System</a>
</p></div>

<div class="wikidoc">
<h1>Hidden Operating System</h1>
<div style="text-align:left; margin-top:19px; margin-bottom:19px; padding-top:0px; padding-bottom:0px">
If your system partition or system drive is encrypted using VeraCrypt, you need to enter your
<a href="System%20Encryption.html" style="text-align:left; color:#0080c0; text-decoration:none">
pre-boot authentication</a> password in the VeraCrypt Boot Loader screen after you turn on or restart your computer. It may happen that you are forced by somebody to decrypt the operating system or to reveal the pre-boot authentication password. There are many
 situations where you cannot refuse to do so (for example, due to extortion). VeraCrypt allows you to create a hidden operating system whose existence should be impossible to prove (provided that certain guidelines are followed &mdash; see below). Thus, you
 will not have to decrypt or reveal the password for the hidden operating system.</div>
<div style="text-align:left; margin-top:19px; margin-bottom:19px; padding-top:0px; padding-bottom:0px">
Before you continue reading this section, make sure you have read the section <a href="Hidden%20Volume.html" style="text-align:left; color:#0080c0; text-decoration:none">
<strong style="text-align:left">Hidden Volume</strong></a> and that you understand what a
<a href="Hidden%20Volume.html" style="text-align:left; color:#0080c0; text-decoration:none">
hidden VeraCrypt volume</a> is.</div>
<div style="text-align:left; margin-top:19px; margin-bottom:19px; padding-top:0px; padding-bottom:0px">
A <strong style="text-align:left">hidden operating system</strong> is a system (for example, Windows 7 or Windows XP) that is installed in a
<a href="Hidden%20Volume.html" style="text-align:left; color:#0080c0; text-decoration:none">
hidden VeraCrypt volume</a>. It should be impossible to prove that a <a href="Hidden%20Volume.html" style="text-align:left; color:#0080c0; text-decoration:none">
hidden VeraCrypt volume</a> exists (provided that certain guidelines are followed; for more information, see the section
<a href="Hidden%20Volume.html" style="text-align:left; color:#0080c0; text-decoration:none">
Hidden Volume</a>) and, therefore, it should be impossible to prove that a hidden operating system exists.</div>
<div style="text-align:left; margin-top:19px; margin-bottom:19px; padding-top:0px; padding-bottom:0px">
However, in order to boot a system encrypted by VeraCrypt, an unencrypted copy of the
<a href="System%20Encryption.html" style="text-align:left; color:#0080c0; text-decoration:none">
VeraCrypt Boot Loader</a> has to be stored on the system drive or on a <a href="VeraCrypt%20Rescue%20Disk.html" style="text-align:left; color:#0080c0; text-decoration:none">
VeraCrypt Rescue Disk</a>. Hence, the mere presence of the VeraCrypt Boot Loader can indicate that there is a system encrypted by VeraCrypt on the computer. Therefore, to provide a plausible explanation for the presence of the VeraCrypt Boot Loader, the VeraCrypt
 wizard helps you create a second encrypted operating system, so-called <strong style="text-align:left">
decoy operating system</strong>, during the process of creation of a hidden operating system. A decoy operating system must not contain any sensitive files. Its existence is not secret (it is
<em style="text-align:left">not</em> installed in a <a href="Hidden%20Volume.html" style="text-align:left; color:#0080c0; text-decoration:none">
hidden volume</a>). The password for the decoy operating system can be safely revealed to anyone forcing you to disclose your pre-boot authentication password.*</div>
<div style="text-align:left; margin-top:19px; margin-bottom:19px; padding-top:0px; padding-bottom:0px">
You should use the decoy operating system as frequently as you use your computer. Ideally, you should use it for all activities that do not involve sensitive data. Otherwise, plausible deniability of the hidden operating system might be adversely affected (if
 you revealed the password for the decoy operating system to an adversary, he could find out that the system is not used very often, which might indicate the existence of a hidden operating system on your computer). Note that you can save data to the decoy
 system partition anytime without any risk that the hidden volume will get damaged (because the decoy system is
<em style="text-align:left">not</em> installed in the outer volume &mdash; see below).</div>
<div style="text-align:left; margin-top:19px; margin-bottom:19px; padding-top:0px; padding-bottom:0px">
There will be two pre-boot authentication passwords &mdash; one for the hidden system and the other for the decoy system. If you want to start the hidden system, you simply enter the password for the hidden system in the VeraCrypt Boot Loader screen (which
 appears after you turn on or restart your computer). Likewise, if you want to start the decoy system (for example, when asked to do so by an adversary), you just enter the password for the decoy system in the VeraCrypt Boot Loader screen.</div>
<div style="text-align:left; margin-top:19px; margin-bottom:19px; padding-top:0px; padding-bottom:0px">
Note: When you enter a pre-boot authentication password, the VeraCrypt Boot Loader first attempts to decrypt (using the entered password) the last 512 bytes of the first logical track of the system drive (where encrypted master key data for non-hidden encrypted
 system partitions/drives are normally stored). If it fails and if there is a partition behind the active partition, the VeraCrypt Boot Loader (even if there is actually no hidden volume on the drive) automatically tries to decrypt (using the same entered password
 again) the area of the first partition behind the active partition where the encrypted header of a possible hidden volume might be stored (however, if the size of the active partition is less than 256 MB, then the data is read from the
<em style="text-align:left">second</em> partition behind the active one, because Windows 7 and later, by default, do not boot from the partition on which they are installed). Note that VeraCrypt never knows if there is a hidden volume in advance (the hidden
 volume header cannot be identified, as it appears to consist entirely of random data). If the header is successfully decrypted (for information on how VeraCrypt determines that it was successfully decrypted, see the section
<a href="Encryption%20Scheme.html" style="text-align:left; color:#0080c0; text-decoration:none">
Encryption Scheme</a>), the information about the size of the hidden volume is retrieved from the decrypted header (which is still stored in RAM), and the hidden volume is mounted (its size also determines its offset). For further technical details, see the
 section <a href="Encryption%20Scheme.html" style="text-align:left; color:#0080c0; text-decoration:none">
Encryption Scheme</a> in the chapter <a href="Technical%20Details.html" style="text-align:left; color:#0080c0; text-decoration:none">
Technical Details</a>.</div>
<div style="text-align:left; margin-top:19px; margin-bottom:19px; padding-top:0px; padding-bottom:0px">
When running, the hidden operating system appears to be installed on the same partition as the original operating system (the decoy system). However, in reality, it is installed within the partition behind it (in a hidden volume). All read/write operations
 are transparently redirected from the system partition to the hidden volume. Neither the operating system nor applications will know that data written to and read from the system partition is actually written to and read from the partition behind it (from/to
 a hidden volume). Any such data is encrypted and decrypted on the fly as usual (with an encryption key different from the one that is used for the decoy operating system).</div>
<div style="text-align:left; margin-top:19px; margin-bottom:19px; padding-top:0px; padding-bottom:0px">
Note that there will also be a third password &mdash; the one for the <a href="Hidden%20Volume.html" style="text-align:left; color:#0080c0; text-decoration:none">
<strong style="text-align:left">outer volume</strong></a>. It is not a pre-boot authentication password, but a regular VeraCrypt volume password. It can be safely disclosed to anyone forcing you to reveal the password for the encrypted partition where the hidden
 volume (containing the hidden operating system) resides. Thus, the existence of the hidden volume (and of the hidden operating system) will remain secret. If you are not sure you understand how this is possible, or what an outer volume is, please read the
 section <a href="Hidden%20Volume.html" style="text-align:left; color:#0080c0; text-decoration:none">
Hidden Volume</a>. The outer volume should contain some sensitive-looking files that you actually do
<em style="text-align:left">not</em> want to hide.</div>
<div style="text-align:left; margin-top:19px; margin-bottom:19px; padding-top:0px; padding-bottom:0px">
To summarize, there will be three passwords in total. Two of them can be revealed to an attacker (for the decoy system and for the outer volume). The third password, for the hidden system, must remain secret.</div>
<div style="text-align:left; margin-top:19px; margin-bottom:19px; padding-top:0px; padding-bottom:0px">
<img src="Beginner's Tutorial_Image_034.png" alt="Example Layout of System Drive Containing Hidden Operating System"></div>
<div style="text-align:left; margin-top:19px; margin-bottom:19px; padding-top:0px; padding-bottom:0px">
<em style="text-align:left">Example Layout of System Drive Containing Hidden Operating System</em></div>
<p>&nbsp;</p>
<h4 id="CreationProcess" style="text-align:left; font-family:Arial,Helvetica,Verdana,sans-serif; font-weight:bold; margin-top:0px; font-size:12px; margin-bottom:1px">
Process of Creation of Hidden Operating System</h4>
<div style="text-align:left; margin-top:19px; margin-bottom:19px; padding-top:0px; padding-bottom:0px">
To start the process of creation of a hidden operating system, select <em style="text-align:left">
System</em> &gt; <em style="text-align:left">Create Hidden Operating System</em> and then follow the instructions in the wizard.</div>
<div style="text-align:left; margin-top:19px; margin-bottom:19px; padding-top:0px; padding-bottom:0px">
Initially, the wizard verifies that there is a suitable partition for a hidden operating system on the system drive. Note that before you can create a hidden operating system, you need to create a partition for it on the system drive. It must be the first partition
 behind the system partition and it must be at least 5% larger than the system partition (the system partition is the one where the currently running operating system is installed). However, if the outer volume (not to be confused with the system partition)
 is formatted as NTFS, the partition for the hidden operating system must be at least 110% (2.1 times) larger than the system partition (the reason is that the NTFS file system always stores internal data exactly in the middle of the volume and, therefore,
 the hidden volume, which is to contain a clone of the system partition, can reside only in the second half of the partition).</div>
<div style="text-align:left; margin-top:19px; margin-bottom:19px; padding-top:0px; padding-bottom:0px">
In the next steps, the wizard will create two VeraCrypt volumes (<a href="Hidden%20Volume.html" style="text-align:left; color:#0080c0; text-decoration:none">outer and hidden</a>) within the first partition behind the
 system partition. The <a href="Hidden%20Volume.html" style="text-align:left; color:#0080c0; text-decoration:none">
hidden volume</a> will contain the hidden operating system. The size of the hidden volume is always the same as the size of the system partition. The reason is that the hidden volume will need to contain a clone of the content of the system partition (see below).
 Note that the clone will be encrypted using a different encryption key than the original. Before you start copying some sensitive-looking files to the outer volume, the wizard tells you the maximum recommended size of space that the files should occupy, so
 that there is enough free space on the outer volume for the hidden volume.</div>
<div style="text-align:left; margin-top:19px; margin-bottom:19px; padding-top:0px; padding-bottom:0px">
Remark: After you copy some sensitive-looking files to the outer volume, the cluster bitmap of the volume will be scanned in order to determine the size of uninterrupted area of free space whose end is aligned with the end of the outer volume. This area will
 accommodate the hidden volume, so it limits its maximum possible size. The maximum possible size of the hidden volume will be determined and it will be verified that it is greater than the size of the system partition (which is required, because the entire
 content of the system partition will need to be copied to the hidden volume &mdash; see below). This ensures that no data stored on the outer volume will be overwritten by data written to the area of the hidden volume (e.g. when the system is being copied
 to it). The size of the hidden volume is always the same as the size of the system partition.</div>
<div style="text-align:left; margin-top:19px; margin-bottom:19px; padding-top:0px; padding-bottom:0px">
Then, VeraCrypt will create the hidden operating system by copying the content of the system partition to the hidden volume. Data being copied will be encrypted on the fly with an encryption key different from the one that will be used for the decoy operating
 system. The process of copying the system is performed in the pre-boot environment (before Windows starts) and it may take a long time to complete; several hours or even several days (depending on the size of the system partition and on the performance of
 the computer). You will be able to interrupt the process, shut down your computer, start the operating system and then resume the process. However, if you interrupt it, the entire process of copying the system will have to start from the beginning (because
 the content of the system partition must not change during cloning). The hidden operating system will initially be a clone of the operating system under which you started the wizard.</div>
<div id="SecureEraseOS" style="text-align:left; margin-top:19px; margin-bottom:19px; padding-top:0px; padding-bottom:0px">
Windows creates (typically, without your knowledge or consent) various log files, temporary files, etc., on the system partition. It also saves the content of RAM to hibernation and paging files located on the system partition. Therefore, if an adversary analyzed
 files stored on the partition where the original system (of which the hidden system is a clone) resides, he might find out, for example, that you used the VeraCrypt wizard in the hidden-system-creation mode (which might indicate the existence of a hidden operating
 system on your computer). To prevent such issues, VeraCrypt will securely erase the entire content of the partition where the original system resides after the hidden system has been created. Afterwards, in order to achieve plausible deniability, VeraCrypt
 will prompt you to install a new system on the partition and encrypt it using VeraCrypt. Thus, you will create the decoy system and the whole process of creation of the hidden operating system will be completed.</div>
<div style="text-align:left; margin-top:19px; margin-bottom:19px; padding-top:0px; padding-bottom:0px">
Note: VeraCrypt will erase the content of the partition where the original system resides by filling it with random data entirely. If you revealed the password for the decoy system to an adversary and he asked you why the free space of the (decoy) system partition
 contains random data, you could answer, for example: &quot;The partition previously contained a system encrypted by VeraCrypt, but I forgot the pre-boot authentication password (or the system was damaged and stopped booting), so I had to reinstall Windows and encrypt
 the partition again.&quot;</div>
<h4 id="data_leak_protection" style="text-align:left; font-family:Arial,Helvetica,Verdana,sans-serif; font-weight:bold; margin-top:0px; font-size:12px; margin-bottom:1px">
Plausible Deniability and Data Leak Protection</h4>
<div style="text-align:left; margin-top:19px; margin-bottom:19px; padding-top:0px; padding-bottom:0px">
For security reasons, when a hidden operating system is running, VeraCrypt ensures that all local unencrypted filesystems and non-hidden VeraCrypt volumes are read-only (i.e. no files can be written to such filesystems or VeraCrypt volumes).&dagger; Data is
 allowed to be written to any filesystem that resides within a <a href="Hidden%20Volume.html" style="text-align:left; color:#0080c0; text-decoration:none">
hidden VeraCrypt volume</a> (provided that the hidden volume is not located in a container stored on an unencrypted filesystem or on any other read-only filesystem).</div>
<div style="text-align:left; margin-top:19px; margin-bottom:19px; padding-top:0px; padding-bottom:0px">
There are three main reasons why such countermeasures have been implemented:</div>
<ol style="text-align:left; margin-top:18px; margin-bottom:19px; padding-top:0px; padding-bottom:0px">
<li style="text-align:left; margin-top:0px; margin-bottom:0px; padding-top:0px; padding-bottom:0px">
It enables the creation of a secure platform for mounting of hidden VeraCrypt volumes. Note that we officially recommend that hidden volumes are mounted only when a hidden operating system is running. For more information, see the subsection
<a href="Security%20Requirements%20for%20Hidden%20Volumes.html" style="text-align:left; color:#0080c0; text-decoration:none">
Security Requirements and Precautions Pertaining to Hidden Volumes</a>. </li><li style="text-align:left; margin-top:0px; margin-bottom:0px; padding-top:0px; padding-bottom:0px">
In some cases, it is possible to determine that, at a certain time, a particular filesystem was not mounted under (or that a particular file on the filesystem was not saved or accessed from within) a particular instance of an operating system (e.g. by analyzing
 and comparing filesystem journals, file timestamps, application logs, error logs, etc). This might indicate that a hidden operating system is installed on the computer. The countermeasures prevent these issues.
</li><li style="text-align:left; margin-top:0px; margin-bottom:0px; padding-top:0px; padding-bottom:0px">
It prevents data corruption and allows safe hibernation. When Windows resumes from hibernation, it assumes that all mounted filesystems are in the same state as when the system entered hibernation. VeraCrypt ensures this by write-protecting any filesystem accessible
 both from within the decoy and hidden systems. Without such protection, the filesystem could become corrupted when mounted by one system while the other system is hibernated.
</li></ol>
<p>If you need to securely transfer files from the decoy system to the hidden system, follow these steps:</p>
<ol style="text-align:left; margin-top:18px; margin-bottom:19px; padding-top:0px; padding-bottom:0px">
<li style="text-align:left; margin-top:0px; margin-bottom:0px; padding-top:0px; padding-bottom:0px">
Start the decoy system. </li><li style="text-align:left; margin-top:0px; margin-bottom:0px; padding-top:0px; padding-bottom:0px">
Save the files to an unencrypted volume or to an outer/normal VeraCrypt volume. </li><li style="text-align:left; margin-top:0px; margin-bottom:0px; padding-top:0px; padding-bottom:0px">
Start the hidden system </li><li style="text-align:left; margin-top:0px; margin-bottom:0px; padding-top:0px; padding-bottom:0px">
If you saved the files to a VeraCrypt volume, mount it (it will be automatically mounted as read-only).
</li><li style="text-align:left; margin-top:0px; margin-bottom:0px; padding-top:0px; padding-bottom:0px">
Copy the files to the hidden system partition or to another hidden volume. </li></ol>
<p>&nbsp;</p>
<h4 style="text-align:left; font-family:Arial,Helvetica,Verdana,sans-serif; font-weight:bold; margin-top:0px; font-size:12px; margin-bottom:1px">
Possible Explanations for Existence of Two VeraCrypt Partitions on Single Drive</h4>
<div style="text-align:left; margin-top:19px; margin-bottom:19px; padding-top:0px; padding-bottom:0px">
An adversary might ask why you created two VeraCrypt-encrypted partitions on a single drive (a system partition and a non-system partition) rather than encrypting the entire disk with a single encryption key. There are many possible reasons to do that. However,
 if you do not know any (other than creating a hidden operating system), you can provide, for example, one of the following explanations:</div>
<ul style="text-align:left; margin-top:18px; margin-bottom:19px; padding-top:0px; padding-bottom:0px">
<li style="text-align:left; margin-top:0px; margin-bottom:0px; padding-top:0px; padding-bottom:0px">
If there are more than two partitions on a system drive and you want to encrypt only two of them (the system partition and the one behind it) and to leave the other partitions unencrypted (for example, to achieve the best possible performance when reading and
 writing data, which is not sensitive, to such unencrypted partitions), the only way to do that is to encrypt both partitions separately (note that, with a single encryption key, VeraCrypt could encrypt the entire system drive and
<em style="text-align:left">all</em> partitions on it, but it cannot encrypt only two of them &mdash; only one or all of the partitions can be encrypted with a single key). As a result, there will be two adjacent VeraCrypt partitions on the system drive (the
 first will be a system partition, the second will be a non-system one), each encrypted with a different key (which is also the case when you create a hidden operating system, and therefore it can be explained this way).<br style="text-align:left">
<br style="text-align:left">
If you do not know any good reason why there should be more than one partition on a system drive at all:<br style="text-align:left">
<br style="text-align:left">
It is generally recommended to separate non-system files (documents) from system files. One of the easiest and most reliable ways to do that is to create two partitions on the system drive; one for the operating system and the other for documents (non-system
 files). The reasons why this practice is recommended include:
<ul style="text-align:left; margin-top:18px; margin-bottom:19px; padding-top:0px; padding-bottom:0px">
<li style="text-align:left; margin-top:0px; margin-bottom:0px; padding-top:0px; padding-bottom:0px">
If the filesystem on one of the partitions is damaged, files on the partition may get corrupted or lost, whereas files on the other partition are not affected.
</li><li style="text-align:left; margin-top:0px; margin-bottom:0px; padding-top:0px; padding-bottom:0px">
It is easier to reinstall the system without losing your documents (reinstallation of an operating system involves formatting the system partition, after which all files stored on it are lost). If the system is damaged, full reinstallation is often the only
 option. </li></ul>
</li><li style="text-align:left; margin-top:0px; margin-bottom:0px; padding-top:0px; padding-bottom:0px">
A <a href="Cascades.html" style="text-align:left; color:#0080c0; text-decoration:none">
cascade encryption algorithm</a> (e.g. AES-Twofish-Serpent) can be many times slower than a non-cascade one (e.g.
<a href="AES.html" style="text-align:left; color:#0080c0; text-decoration:none">
AES</a>). However, a cascade encryption algorithm may be more secure than a non-cascade one (for example, the probability that three distinct encryption algorithms will be broken, e.g. due to advances in cryptanalysis, is significantly lower than the probability
 that only one of them will be broken). Therefore, if you encrypt the outer volume with a cascade encryption algorithm and the decoy system with a non-cascade encryption algorithm, you can answer that you wanted the best performance (and adequate security)
 for the system partition, and the highest possible security (but worse performance) for the non-system partition (i.e. the outer volume), where you store the most sensitive data, which you do not need to access very often (unlike the operating system, which
 you use very often, and therefore you need it to have the best possible performance). On the system partition, you store data that is less sensitive (but which you need to access very often) than data you store on the non-system partition (i.e. on the outer
 volume). </li><li style="text-align:left; margin-top:0px; margin-bottom:0px; padding-top:0px; padding-bottom:0px">
Provided that you encrypt the outer volume with a cascade encryption algorithm (e.g. AES-Twofish-Serpent) and the decoy system with a non-cascade encryption algorithm (e.g. AES), you can also answer that you wanted to prevent the problems about which VeraCrypt
 warns when the user attempts to choose a cascade encryption algorithm for system encryption (see below for a list of the problems). Therefore, to prevent those problems, you decided to encrypt the system partition with a non-cascade encryption algorithm. However,
 you still wanted to use a cascade encryption algorithm (because it is more secure than a non-cascade encryption algorithm) for the most sensitive data, so you decided to create a second partition, which those problems do
<em style="text-align:left">not</em> affect (because it is non-system) and to encrypt it with a cascade encryption algorithm. On the system partition, you store data that is less sensitive than data you store on the non-system partition (i.e. on the outer volume).
<br style="text-align:left">
<br style="text-align:left">
Note: When the user attempts to encrypt the system partition with a cascade encryption algorithm, VeraCrypt warns him or her that it can cause the following problems (and implicitly recommends to choose a non-cascade encryption algorithm instead):
<ul style="text-align:left; margin-top:18px; margin-bottom:19px; padding-top:0px; padding-bottom:0px">
<li style="text-align:left; margin-top:0px; margin-bottom:0px; padding-top:0px; padding-bottom:0px; font-size:10px; line-height:12px">
For cascade encryption algorithms, the VeraCrypt Boot Loader is larger than normal and, therefore, there is not enough space in the first drive track for a backup of the VeraCrypt Boot Loader. Hence,
<em style="text-align:left">whenever</em> it gets damaged (which often happens, for example, during inappropriately designed anti-piracy activation procedures of certain programs), the user must use the VeraCrypt Rescue Disk to repair the VeraCrypt Boot Loader
 or to boot. </li><li style="text-align:left; margin-top:0px; margin-bottom:0px; padding-top:0px; padding-bottom:0px; font-size:10px; line-height:12px">
On some computers, resuming from hibernation takes longer. </li></ul>
</li><li style="text-align:left; margin-top:0px; margin-bottom:0px; padding-top:0px; padding-bottom:0px">
In contrast to a password for a non-system VeraCrypt volume, a pre-boot authentication password needs to be typed each time the computer is turned on or restarted. Therefore, if the pre-boot authentication password is long (which is required for security purposes),
 it may be very tiresome to type it so frequently. Hence, you can answer that it was more convenient for you to use a short (and therefore weaker) password for the system partition (i.e. the decoy system) and that it is more convenient for you to store the
 most sensitive data (which you do not need to access as often) in the non-system VeraCrypt partition (i.e. in the outer volume) for which you chose a very long password.
<br style="text-align:left">
<br style="text-align:left">
As the password for the system partition is not very strong (because it is short), you do not intentionally store sensitive data on the system partition. However, you still prefer the system partition to be encrypted, because potentially sensitive or mildly
 sensitive data is stored on it as a result of your everyday use of the computer (for example, passwords to online forums you visit, which can be automatically remembered by your browser, browsing history, applications you run, etc.)
</li><li style="text-align:left; margin-top:0px; margin-bottom:0px; padding-top:0px; padding-bottom:0px">
When an attacker gets hold of your computer when a VeraCrypt volume is mounted (for example, when you use a laptop outside), he can, in most cases, read any data stored on the volume (data is decrypted on the fly as he reads it). Therefore, it may be wise to
 limit the time the volume is mounted to a minimum. Obviously, this may be impossible or difficult if the sensitive data is stored on an encrypted system partition or on an entirely encrypted system drive (because you would also have to limit the time you work
 with the computer to a minimum). Hence, you can answer that you created a separate partition (encrypted with a different key than your system partition) for your most sensitive data and that you mount it only when necessary and unmount it as soon as possible
 (so as to limit the time the volume is mounted to a minimum). On the system partition, you store data that is less sensitive (but which you need to access often) than data you store on the non-system partition (i.e. on the outer volume).
</li></ul>
<div style="text-align:left; margin-top:19px; margin-bottom:19px; padding-top:0px; padding-bottom:0px">
&nbsp;</div>
<h4 style="text-align:left; font-family:Arial,Helvetica,Verdana,sans-serif; font-weight:bold; margin-top:0px; font-size:12px; margin-bottom:1px">
Safety/Security Precautions and Requirements Pertaining to Hidden Operating Systems</h4>
<div style="text-align:left; margin-top:19px; margin-bottom:19px; padding-top:0px; padding-bottom:0px">
As a hidden operating system resides in a hidden VeraCrypt volume, a user of a hidden operating system must follow all of the security requirements and precautions that apply to normal hidden VeraCrypt volumes. These requirements and precautions, as well as
 additional requirements and precautions pertaining specifically to hidden operating systems, are listed in the subsection
<a href="Security%20Requirements%20for%20Hidden%20Volumes.html" style="text-align:left; color:#0080c0; text-decoration:none">
Security Requirements and Precautions Pertaining to Hidden Volumes</a>.</div>
<div style="text-align:left; margin-top:19px; margin-bottom:19px; padding-top:0px; padding-bottom:0px">
WARNING: If you do not protect the hidden volume (for information on how to do so, refer to the section
<a href="Protection%20of%20Hidden%20Volumes.html" style="text-align:left; color:#0080c0; text-decoration:none">
Protection of Hidden Volumes Against Damage</a>), do <em style="text-align:left">
not</em> write to the outer volume (note that the decoy operating system is <em style="text-align:left">
not</em> installed in the outer volume). Otherwise, you may overwrite and damage the hidden volume (and the hidden operating system within it)!</div>
<div style="text-align:left; margin-top:19px; margin-bottom:19px; padding-top:0px; padding-bottom:0px">
If all the instructions in the wizard have been followed and if the security requirements and precautions listed in the subsection
<a href="Security%20Requirements%20for%20Hidden%20Volumes.html" style="text-align:left; color:#0080c0; text-decoration:none">
Security Requirements and Precautions Pertaining to Hidden Volumes</a> are followed, it should be impossible to prove that the hidden volume and hidden operating system exist, even when the outer volume is mounted or when the decoy operating system is decrypted
 or started.</div>
<p>&nbsp;</p>
<hr align="left" size="1" width="189" style="text-align:left; height:0px; border-width:0px 1px 1px; border-style:solid; border-color:#000000">
<p><span style="text-align:left; font-size:10px; line-height:12px">* It is not practical (and therefore is not supported) to install operating systems in two VeraCrypt volumes that are embedded within a single partition, because using the outer operating system
 would often require data to be written to the area of the hidden operating system (and if such write operations were prevented using the
<a href="Protection%20of%20Hidden%20Volumes.html" style="text-align:left; color:#0080c0; text-decoration:none">
hidden volume protection</a> feature, it would inherently cause system crashes, i.e. 'Blue Screen' errors).<br style="text-align:left">
&dagger; This does not apply to filesystems on CD/DVD-like media and on custom, atypical, or non-standard devices/media.</span><br style="text-align:left">
<br style="text-align:left">
<br style="text-align:left">
<br style="text-align:left">
<br style="text-align:left">
&nbsp;&nbsp;See also: <strong style="text-align:left"><a href="System%20Encryption.html" style="text-align:left; color:#0080c0; text-decoration:none">System Encryption</a></strong>, &nbsp;<strong style="text-align:left"><a href="Hidden%20Volume.html" style="text-align:left; color:#0080c0; text-decoration:none">Hidden
 Volume</a></strong></p>
</div>
</body></html>
