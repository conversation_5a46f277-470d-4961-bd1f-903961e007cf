<!DOCTYPE html>
<html lang="en">
<head>
<meta http-equiv="content-type" content="text/html; charset=utf-8" />
<title>VeraCrypt - Free Open source disk encryption with strong security for the Paranoid</title>
<meta name="description" content="VeraCrypt is free open-source disk encryption software for Windows, Mac OS X and Linux. In case an attacker forces you to reveal the password, VeraCrypt provides plausible deniability. In contrast to file encryption, data encryption performed by VeraCrypt is real-time (on-the-fly), automatic, transparent, needs very little memory, and does not involve temporary unencrypted files."/>
<meta name="keywords" content="encryption, security"/>
<link href="styles.css" rel="stylesheet" type="text/css" />
</head>
<body>

<div>
<a href="Documentation.html"><img src="VeraCrypt128x128.png" alt="VeraCrypt"/></a>
</div>

<div id="menu">
	<ul>
	  <li><a href="Home.html">Home</a></li>
	  <li><a href="Code.html">Source Code</a></li>
	  <li><a href="Downloads.html">Downloads</a></li>
	  <li><a class="active" href="Documentation.html">Documentation</a></li>
	  <li><a href="Donation.html">Donate</a></li>
	  <li><a href="https://sourceforge.net/p/veracrypt/discussion/" target="_blank">Forums</a></li>
	</ul>
</div>

<div>
<p>
<a href="Documentation.html">Documentation</a>
<img src="arrow_right.gif" alt=">>" style="margin-top: 5px">
<a href="Security%20Requirements%20and%20Precautions.html">Security Requirements and Precautions</a>
<img src="arrow_right.gif" alt=">>" style="margin-top: 5px">
<a href="Authenticity%20and%20Integrity.html">Authenticity and Integrity</a>
</p></div>

<div class="wikidoc">
<div>
<h1>Authenticity and Integrity</h1>
<p>VeraCrypt uses encryption to preserve the <em>confidentiality</em> of data it encrypts. VeraCrypt neither preserves nor verifies the integrity or authenticity of data it encrypts or decrypts. Hence, if you allow an adversary to modify data encrypted by VeraCrypt,
 he can set the value of any 16-byte block of the data to a random value or to a previous value, which he was able to obtain in the past. Note that the adversary cannot choose the value that you will obtain when VeraCrypt decrypts the modified block &mdash;
 the value will be random &mdash; unless the attacker restores an older version of the encrypted block, which he was able to obtain in the past. It is your responsibility to verify the integrity and authenticity of data encrypted or decrypted by VeraCrypt (for
 example, by using appropriate third-party software).<br>
<br>
See also: <a href="Physical%20Security.html">
<em>Physical Security</em></a>, <a href="Security%20Model.html">
<em>Security Model</em></a></p>
</div>
</div><div class="ClearBoth"></div></body></html>
