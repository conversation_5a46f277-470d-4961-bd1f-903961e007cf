﻿<!DOCTYPE html>
<html lang="ru">
<head>
<meta http-equiv="content-type" content="text/html; charset=utf-8" />
<title>VeraCrypt - Бесплатное надёжное шифрование дисков с открытым исходным кодом</title>
<meta name="description" content="VeraCrypt это бесплатное программное обеспечение для шифрования дисков с открытым исходным кодом для Windows, Mac OS X (macOS) и Linux. В случае, если злоумышленник вынуждает вас раскрыть пароль, VeraCrypt обеспечивает правдоподобное отрицание наличия шифрования. В отличие от пофайлового шифрования, VeraCrypt шифрует данные в реальном времени (на лету), автоматически, прозрачно, требует очень мало памяти и не использует временные незашифрованные файлы."/>
<meta name="keywords" content="encryption, security, шифрование, безопасность"/>
<link href="styles.css" rel="stylesheet" type="text/css" />
</head>
<body>

<div>
<a href="Documentation.html"><img src="VeraCrypt128x128.png" alt="VeraCrypt"/></a>
</div>

<div id="menu">
	<ul>
	  <li><a href="Home.html">Начало</a></li>
	  <li><a href="Code.html">Исходный код</a></li>
	  <li><a href="Downloads.html">Загрузить</a></li>
	  <li><a class="active" href="Documentation.html">Документация</a></li>
	  <li><a href="Donation.html">Поддержать разработку</a></li>
	  <li><a href="https://sourceforge.net/p/veracrypt/discussion/" target="_blank">Форум</a></li>
	</ul>
</div>

<div>
<p>
<a href="Documentation.html">Документация</a>
<img src="arrow_right.gif" alt=">>" style="margin-top: 5px">
<a href="Acknowledgements.html">Благодарности</a>
</p></div>
<div class="wikidoc">
<div>
<h1>Благодарности</h1>
<p>Выражаем благодарность следующим людям:</p>
<p>Разработчикам программы TrueCrypt, проделавшим потрясающую работу в течение 10 лет. Без их напряжённого труда VeraCrypt сегодня не существовало бы.</p>
<p><i>Paul Le Roux</i> за предоставление его исходного кода E4M; TrueCrypt 1.0 ведёт своё происхождение от E4M, а некоторые части исходного кода E4M и поныне входят в исходный код текущей версии TrueCrypt.</p>
<p><i>Brian Gladman</i>, автору превосходных подпрограмм AES, Twofish и SHA-512.</p>
<p><i>Peter Gutmann</i> за его документ о случайных числах и за создание библиотеки cryptlib, послужившей источником части исходного кода генератора случайных чисел.</p>
<p><i>Wei Dai</i>, автору подпрограмм Serpent и RIPEMD-160.</p>
<p><i>Tom St Denis</i>, автору LibTomCrypt, включающей компактные подпрограммы SHA-256.</p>
<p><i>Mark Adler</i> и <i>Jean-loup Gailly</i>, написавшим библиотеку zlib.</p>
<p>Разработчикам алгоритмов шифрования, хеширования и режима работы:</p>
<p><i>Horst Feistel, Don Coppersmith, Walt Tuchmann, Lars Knudsen, Ross Anderson, Eli Biham, Bruce Schneier, David Wagner, John Kelsey, Niels Ferguson, Doug Whiting, Chris Hall, Joan Daemen, Vincent Rijmen, Carlisle Adams, Stafford Tavares, Phillip Rogaway, Hans
 Dobbertin, Antoon Bosselaers, Bart Preneel, Paulo S. L. M. Barreto.</i></p>
<p><i>Andreas Becker</i> за создание логотипа и значков VeraCrypt.</p>
<p><i>Xavier de Carn&eacute; de Carnavalet</i>, предложившему оптимизацию скорости для PBKDF2, которая вдвое сократила время монтирования/загрузки.</p>
<p><i>kerukuro</i> за библиотеку cppcrypto (http://cppcrypto.sourceforge.net/), откуда взята реализация шифра «Кузнечик».</p>
<p><br>
<i>Dieter Baron</i> и <i>Thomas Klausner</i>, написавшим библиотеку libzip.</p>
<p><br>
<i>Jack Lloyd</i>, написавшему оптимизированную для SIMD реализацию Serpent.</p>
<p>Всем остальным, благодаря кому стал возможен этот проект, кто нас морально поддерживал, а также тем, кто присылал нам сообщения об ошибках и предложения по улучшению программы.</p>
<p>Большое вам спасибо!</p>
<br><br>
<p>Перевод программы и документации на русский язык выполнил <em>Дмитрий Ерохин</em>.</p>
</div>
</div><div class="ClearBoth"></div></body></html>
