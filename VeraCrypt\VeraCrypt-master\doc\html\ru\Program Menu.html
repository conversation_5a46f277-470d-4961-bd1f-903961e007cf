﻿<!DOCTYPE html>
<html lang="ru">
<head>
<meta http-equiv="content-type" content="text/html; charset=utf-8" />
<title>VeraCrypt - Бесплатное надёжное шифрование дисков с открытым исходным кодом</title>
<meta name="description" content="VeraCrypt это бесплатное программное обеспечение для шифрования дисков с открытым исходным кодом для Windows, Mac OS X (macOS) и Linux. В случае, если злоумышленник вынуждает вас раскрыть пароль, VeraCrypt обеспечивает правдоподобное отрицание наличия шифрования. В отличие от пофайлового шифрования, VeraCrypt шифрует данные в реальном времени (на лету), автоматически, прозрачно, требует очень мало памяти и не использует временные незашифрованные файлы."/>
<meta name="keywords" content="encryption, security, шифрование, безопасность"/>
<link href="styles.css" rel="stylesheet" type="text/css" />
</head>
<body>

<div>
<a href="Documentation.html"><img src="VeraCrypt128x128.png" alt="VeraCrypt"/></a>
</div>

<div id="menu">
	<ul>
	  <li><a href="Home.html">Начало</a></li>
	  <li><a href="Code.html">Исходный код</a></li>
	  <li><a href="Downloads.html">Загрузить</a></li>
	  <li><a class="active" href="Documentation.html">Документация</a></li>
	  <li><a href="Donation.html">Поддержать разработку</a></li>
	  <li><a href="https://sourceforge.net/p/veracrypt/discussion/" target="_blank">Форум</a></li>
	</ul>
</div>

<div>
<p>
<a href="Documentation.html">Документация</a>
<img src="arrow_right.gif" alt=">>" style="margin-top: 5px">
<a href="Main%20Program%20Window.html">Главное окно программы</a>
<img src="arrow_right.gif" alt=">>" style="margin-top: 5px">
<a href="Program%20Menu.html">Меню программы</a>
</p></div>

<div class="wikidoc">
<h2>Меню программы</h2>
<div style="text-align:left; margin-top:19px; margin-bottom:19px; padding-top:0px; padding-bottom:0px">
<p>Примечание. Для экономии места в этой документации описаны только те пункты меню, которые требуют пояснений,
а описание очевидных пунктов опущено.</p>
<h3>Тома &gt; Автомонтирование всех томов на основе устройств</h3>
<p>См. раздел <a href="Main%20Program%20Window.html">
<em>Автомонтирование</em></a>.</p>
<h3>Тома &gt; Размонтировать все смонтированные тома</h3>
<p>См. раздел <a href="Main%20Program%20Window.html">
<em>Размонтировать все</em></a>.</p>
<h3>Тома &gt; Изменить пароль тома</h3>
<p>Позволяет изменить пароль выбранного в данный момент тома VeraCrypt (неважно, скрытого или обычного). Изменяются
только ключ заголовка и вторичный ключ заголовка (режим XTS) – мастер-ключ остаётся неизменным. Эта функция
выполняет перешифровку заголовка тома с использованием ключа шифрования, полученного из нового пароля. Обратите
внимание, что в заголовке тома содержится мастер-ключ шифрования, с помощью которого зашифрован этот том.
Поэтому после применения этой функции хранящиеся в томе данные <i>не потеряются</i> (смена пароля длится несколько секунд).<br>
<br>
Чтобы изменить пароль тома VeraCrypt, нажмите кнопку <em>Выбрать файл</em> или <em>Выбрать устройство</em>, затем выберите
том и в меню <em>Тома</em> выберите команду <em>Изменить пароль тома</em>.<br>
<br>
Примечание. Об изменении пароля для предзагрузочной аутентификации см. раздел
<em>Система &gt; Изменить пароль</em>.<br>
<br>
См. также главу <a href="Security%20Requirements%20and%20Precautions.html">
<em>Требования безопасности и меры предосторожности</em></a>.</p>
<div style="margin-left:50px">
<h4>PKCS-5 PRF</h4>
<p>В этом поле можно выбрать алгоритм, который будет использоваться для формирования (деривации) новых ключей
заголовка тома (см. подробности в разделе
<a href="Header%20Key%20Derivation.html">
<em>Формирование ключа заголовка, соль и количество итераций</em></a>) и генерирования новой соли (см. подробности в разделе
<a href="Random%20Number%20Generator.html">
<em>Генератор случайных чисел</em></a>).<br>
<br>
Примечание. Когда VeraCrypt выполняет перешифрование заголовка тома, исходный заголовок сначала перезаписывается
256 раз случайными данными с целью не дать возможности неприятелю воспользоваться такими технологическими способами,
как магнитно-силовая микроскопия или магнитно-силовая сканирующая туннельная микроскопия [17] для восстановления
перезаписанного заголовка (тем не менее см. также главу
<a href="Security%20Requirements%20and%20Precautions.html">
<em>Требования безопасности и меры предосторожности</em></a>).</p>
</div>
<h3>Тома &gt; Установить алгоритм формирования ключа заголовка</h3>
<p>Эта функция позволяет перешифровать заголовок тома с другим ключом заголовка, сформированным с помощью иной
PRF-функции (например, вместо HMAC-BLAKE2S-256 можно воспользоваться HMAC-Whirlpool). Обратите внимание, что в
заголовке тома содержится мастер-ключ шифрования, с помощью которого зашифрован этот том. Поэтому после применения
этой функции хранящиеся в томе данные <i>не потеряются</i>. См. более подробные сведения в разделе
<a href="Header%20Key%20Derivation.html">
<em>Формирование ключа заголовка, соль и количество итераций</em></a>.<br>
<br>
Примечание. Когда VeraCrypt выполняет перешифрование заголовка тома, исходный заголовок сначала многократно
перезаписывается (3, 7, 35 или 256 раз в зависимости от выбора пользователя) раз случайными данными с целью
не дать возможности неприятелю воспользоваться такими технологическими способами, как магнитно-силовая
микроскопия или магнитно-силовая сканирующая туннельная микроскопия [17] для восстановления перезаписанного
заголовка (тем не менее см. также главу
<a href="Security%20Requirements%20and%20Precautions.html">
<em>Требования безопасности и меры предосторожности</em></a>).</p>
<h3>Тома &gt; Добавить/удалить ключевые файлы в/из том(а)</h3>
<h3>Тома &gt; Удалить все ключевые файлы из тома</h3>
<p>См. главу <a href="Keyfiles.html">
<em>Ключевые файлы</em></a>.</p>
<h3>Избранное &gt; Добавить смонтированный том в список избранных томов</h3>
<h3>Избранное &gt; Упорядочить избранные тома</h3>
<h3>Избранное &gt; Смонтировать избранные тома</h3>
<p>См. главу <a href="Favorite%20Volumes.html">
<em>Избранные тома</em></a>.</p>
<h3>Избранное &gt; Добавить смонтированный том в список системных избранных томов</h3>
<h3>Избранное &gt; Упорядочить системные избранные тома</h3>
<p>См. главу <a href="System%20Favorite%20Volumes.html">
<em>Системные избранные тома</em></a>.</p>
<h3>Система &gt; Изменить пароль</h3>
<p>Изменяет пароль предзагрузочной аутентификации (см. главу <em>Шифрование системы</em>). ВНИМАНИЕ: Если
ключевые данные окажутся повреждёнными, их можно восстановить с помощью Диска восстановления VeraCrypt (Rescue Disk).
При этом также будет восстановлен пароль, который был актуальным на момент создания Диска восстановления.
Поэтому при каждой смене пароля следует уничтожать прежний Диск восстановления и создавать новый (выбрав
<em>Система</em> &gt; <em>Создать Диск восстановления</em>). В противном случае неприятель сможет расшифровать
ваш системный раздел/диск с помощью старого пароля (если к нему в руки попадёт старый Диск восстановления
VeraCrypt, и он им воспользуется, чтобы восстановить ключевые данные. См. также главу
<a href="Security%20Requirements%20and%20Precautions.html">
<em>Требования безопасности и меры предосторожности</em></a>.<br>
<br>
Более подробную информацию о смене пароля см. выше в разделе <em>Тома &gt; Изменить пароль тома</em>.</p>
<h3>Система &gt; Смонтировать без предзагрузочной аутентификации</h3>
<p>Выберите эту опцию, если вам нужно смонтировать раздел, находящийся в области действия шифрования системы,
без предзагрузочной аутентификации. Например, вы хотите смонтировать раздел, расположенный на зашифрованном
системном диске с другой ОС, которая сейчас не запущена. Это может пригодиться, скажем, когда требуется создать
резервную копию или восстановить операционную систему, зашифрованную с помощью VeraCrypt (из другой операционной системы).</p>
<p>Примечание. если нужно смонтировать сразу несколько разделов, нажмите кнопку <em>Автомонтирование</em>, затем
нажмите <em>Параметры</em> и включите опцию <em>Монтировать раздел с шифрованием ОС без предзагрузочной аутентификации</em>.<br>
<br>
Учтите, что эту функцию нельзя использовать для монтирования расширенных (логических) разделов, расположенных
на полностью зашифрованном системном диске.</p>
<h3>Сервис &gt; Очистить историю томов</h3>
<p>Очищает список с именами файлов (если использовались тома на основе файлов) и путями последних 20-ти успешно смонтированных томов.</p>
<h3>Сервис &gt; Настройка Переносного диска</h3>
<p>См. главу <a href="Portable%20Mode.html">
<em>Портативный (переносной) режим</em></a>.</p>
<h3>Сервис &gt; Генератор ключевых файлов</h3>
<p>См. раздел <em>Сервис &gt; Генератор ключевых файлов</em> в главе <a href="Keyfiles.html">
<em>Ключевые файлы</em></a>.</p>
<h3 id="tools-backup-volume-header">Сервис &gt; Создать резервную копию заголовка тома</h3>
<h3 id="tools-restore-volume-header">Сервис &gt; Восстановить заголовок тома</h3>
<p>Если повреждён заголовок тома VeraCrypt, такой том в большинстве случаев смонтировать невозможно. Поэтому
каждый том, созданный с помощью VeraCrypt (за исключением системных разделов) содержит встроенную резервную копию
заголовка, расположенную в конце тома. Для дополнительной безопасности вы также можете создавать внешние резервные
копии заголовков томов. Для этого нажмите кнопку <em>Выбрать устройство</em> или <em>Выбрать файл</em>, укажите
нужный вам том, выберите <em>Сервис</em> &gt; <em>Создать резервную копию заголовка тома</em> и следуйте инструкциям.</p>
<p>Примечание. Если зашифрована система, то резервной копии заголовка в конце тома нет. Для несистемных томов
сначала выполняется операция сжатия, чтобы все данные находились в начале тома, а всё свободное пространство
осталось в конце, где будет место под резервную копию заголовка. Для системных разделов выполнить эту операцию
сжатия во время работы Windows невозможно, потому и нельзя создать резервную копию заголовка в конце раздела.
Альтернативный способ в случае шифрования системы – использование
<a href="VeraCrypt%20Rescue%20Disk.html">
Диска восстановления</a>.</p>
<p>Примечание. Резервная копия заголовка тома (встроенная или внешняя) это <em>не</em> копия исходного заголовка
тома, так как тот зашифрован другим ключом заголовка, сформированным с помощью другой соли (см. раздел
<a href="Header%20Key%20Derivation.html">
<em>Формирование ключа заголовка, соль и количество итераций</em></a>). При изменении пароля и/или ключевых файлов
или при восстановлении заголовка из встроенной (или внешней) резервной копии выполняется повторное шифрование как
заголовка тома, так и его резервной копии (встроенной в том) с помощью ключей заголовка, сформированных посредством
вновь сгенерированной соли (соль для заголовка тома отличается от соли для его резервной копии). VeraCrypt создаёт
каждую соль с помощью генератора случайных чисел (см. раздел
<a href="Random%20Number%20Generator.html">
<em>Генератор случайных чисел</em></a>).</p>
<p>Для восстановления повреждённого заголовка тома можно использовать резервные копии обоих типов (встроенную и внешнюю).
Для этого нажмите кнопку <em>Выбрать устройство</em> или <em>Выбрать файл</em>, укажите нужный вам том, выберите
<em>Сервис</em> &gt; <em>Восстановить заголовок тома</em> и следуйте инструкциям.<br>
<br>
ВНИМАНИЕ: При восстановлении заголовка тома также восстанавливаются пароль тома и PIM, которые были актуальны на момент
создания резервной копии. Более того, если на момент создания резервной копии для монтирования тома требовались
ключевые файлы, то после восстановления заголовка для монтирования тома снова потребуются те же ключевые файлы.
Более подробную информацию см. в разделе
<a href="Encryption%20Scheme.html"><em>Схема шифрования</em></a>, глава
<a href="Technical%20Details.html"><em>Технические подробности</em></a>.<br>
<br>
После создания резервной копии заголовка тома создавать новую копию может потребоваться только при изменении
пароля тома и/или ключевых файлов, либо когда вы изменяете значение PIM. В противном случае заголовок тома
не изменяется, поэтому резервная копия заголовка тома остаётся актуальной.</p>
<p>Примечание. Помимо соли (последовательности случайных чисел), внешние файлы с резервными копиями заголовка
тома не содержат никакой незашифрованной информации, и их нельзя расшифровать, не зная правильный пароль и/или
не предоставив правильные ключевые файлы. Более подробную информацию см. в главе
<a href="Technical%20Details.html">
<em>Технические подробности</em></a>.</p>
<p>При создании внешней резервной копии заголовка в неё помещаются как заголовок обычного тома, так и область,
в которой может храниться заголовок скрытого тома, даже если внутри этого тома нет скрытого тома (чтобы можно
было правдоподобно отрицать наличие скрытых томов). Если в томе нет скрытого тома, область, зарезервированная
под заголовок скрытого тома, будет заполнена в файле с резервной копией случайными данными (чтобы оставалась
возможность правдоподобного отрицания).<br>
<br>
При <i>восстановлении</i> заголовка тома потребуется выбрать тип тома, заголовок которого вы хотите восстановить
(обычный том или скрытый). За одну операцию можно восстановить только один заголовок тома. Чтобы восстановить
оба заголовка, нужно выполнить операцию дважды (<i>Сервис</i> -> <i>Восстановить заголовок тома</i>). Вам будет
нужно ввести правильный пароль (и/или предоставить правильные ключевые файлы), а также нестандартное значение PIM
(если это применимо), актуальные на момент создания резервной копии заголовка тома. Паролем (и/или ключевыми файлами)
и PIM будет также автоматически определяться тип заголовка тома для восстановления, то есть обычный он или скрытый
(обратите внимание, что VeraCrypt определяет тип методом проб и ошибок).
<br>
<br>
Примечание. Если при монтировании тома пользователь два раза подряд неправильно укажет пароль (и/или ключевые файлы)
и/или нестандартное значение PIM, то VeraCrypt будет автоматически пытаться смонтировать том, используя встроенную
резервную копию заголовка (вдобавок к попытке монтирования с помощью основного заголовка), при каждой последующей
попытке пользователя смонтировать том (пока не будет нажата кнопка <i>Отмена</i>). Если VeraCrypt не удастся
расшифровать основной заголовок, но в то же время получится расшифровать встроенную резервную копию заголовка, том
будет смонтирован с предупреждением, что заголовок тома повреждён (и выводом информации, как его восстановить).
</p>
<h3 id="Settings-Performance">Настройки &gt; Производительность и драйвер</h3>
<p>Вызывает окно "Настройки производительности VeraCrypt", в котором можно включить или отключить аппаратное
ускорение AES и распараллеливание на основе потоков. Также здесь можно изменить следующий параметр драйвера:</p>
<h4>Включить поддержку расширенных кодов управления дисками</h4>
<p>Если включено, драйвер VeraCrypt будет поддерживать возврат расширенной технической информации о подключённых
томах с помощью управляющего кода IOCTL_STORAGE_QUERY_PROPERTY. Этот управляющий код всегда поддерживается
физическими дисками и может потребоваться некоторым приложениям для получения технической информации о диске
(например, Windows-программа fsutil использует этот управляющий код для получения размера физического сектора диска).<br>
Включение этой опции приближает поведение томов VeraCrypt к поведению физических дисков, и если она отключена,
приложения могут легко различать физические диски и тома VeraCrypt, поскольку отправка этого управляющего кода
на том VeraCrypt приведёт к ошибке.<br>
Отключите эту опцию, если возникли проблемы со стабильностью (например, проблемы с доступом к тому или системный BSOD),
которые могут быть вызваны плохо написанным ПО и драйверами.</p>
<h3>Настройки &gt; Параметры</h3>
<p>Вызывает диалоговое окно настроек программы, в котором помимо прочего можно изменить следующие параметры:</p>
<h4>Очищать кэш паролей при выходе</h4>
<p>Если включено, пароли (а также содержимое обработанных ключевых файлов) и значения PIM, кэшированные
(сохранённые) в памяти драйвера, будут удалены при выходе из VeraCrypt.</p>
<h4>Кэшировать пароли в памяти драйвера</h4>
<p>Если включено, пароли и/или содержимое обработанных ключевых файлов для четырёх последних успешно
смонтированных томов VeraCrypt будут кэшироваться (временно запоминаться). Если в настройках включён параметр
<em>Кэшировать PIM вместе с паролем</em>, значения PIM, отличные от стандартного, кэшируются вместе с паролями.
Это позволяет монтировать тома без необходимости то и дело вводить их пароли (и выбирать ключевые файлы).
VeraCrypt никогда не сохраняет никаких паролей и значений PIM на диске (тем не менее см. главу
<a href="Security%20Requirements%20and%20Precautions.html">
<em>Требования безопасности и меры предосторожности</em></a>). Кэширование паролей включается/отключается
в настройках программы (<em>Настройки</em> &gt; <em>Параметры</em>) и в окне запроса пароля. В случае
шифрования системного раздела/диска, кэширование пароля предзагрузочной аутентификации можно включить или
выключить в настройках шифрования системы (<em>Настройки</em> &gt; <em>Шифрование системы</em>).</p>
<h4>Временно кэшировать пароль при монтировании избранных томов</h4>
<p>Если эта опция не включена (а по умолчанию она не включена), VeraCrypt будет отображать окно с запросом
пароля для каждого избранного тома во время выполнения операции <em>Монтировать избранные тома</em>, и каждый
пароль стирается после монтирования тома (если не включено кэширование паролей).<br>
<br>
Если эта опция включена и есть два или более избранных тома, то при операции <em>Монтировать избранные тома</em>
VeraCrypt сначала попробует пароль предыдущего избранного тома, и, если он не сработает, отобразит окно
с запросом пароля. Эта логика применяется, начиная со второго избранного тома и далее.
После обработки всех избранных томов пароль стирается из памяти.</p>
<p>Эта опция полезна, когда избранные тома используют один и тот же пароль, поскольку окно с запросом
пароля будет отображаться только один раз для первого избранного тома, а все последующие избранные тома
VeraCrypt смонтирует автоматически.</p>
<p>Обратите внимание, что поскольку нельзя предположить, что все избранные тома используют один и тот же
PRF (хеш) и один и тот же режим TrueCrypt, VeraCrypt определяет PRF последующих избранных томов автоматически
и пробует оба значения режима TrueCrypt (false, true), то есть общее время монтирования будет больше, чем
при индивидуальном монтировании каждого тома с ручным выбором правильного PRF и состояния режима TrueCrypt.</p>
<h4>Открывать Проводник для успешно смонтированного тома</h4>
<p>Если включено, то после успешного монтирования тома VeraCrypt будет автоматически открываться окно
Проводника с содержимым корневой папки этого тома (например, <code>T:\</code>).</p>
<h4>Менять значок в области уведомлений, если есть смонтированные тома</h4>
<p>Если включено, то когда смонтирован том, в области уведомлений на панели задач (рядом с часами) отображается
другой значок VeraCrypt.<br><br> Исключения:</p>
<ul>
<li>разделы/диски внутри области действия ключа шифрования активной системы (например, системный раздел,
зашифрованный VeraCrypt, или несистемный раздел на системном диске, зашифрованном VeraCrypt, смонтированный
во время работы зашифрованной операционной системы);</li>
<li>тома VeraCrypt, не полностью доступные из-под учётной записи пользователя (например, том, смонтированный
из-под другой учётной записи);</li>
<li>тома, не отображаемые в окне VeraCrypt, например системные избранные тома, которые пытались размонтировать
с помощью экземпляра VeraCrypt без прав администратора при включённой опции <em>Просматривать/размонтировать
системные избранные тома могут лишь администраторы</em>. </li></ul>
<h4>Работа VeraCrypt в фоновом режиме – Включено</h4>
<p>См. главу <a href="VeraCrypt%20Background%20Task.html">
<em>Работа VeraCrypt в фоновом режиме</em></a>.</p>
<h4>Работа VeraCrypt в фоновом режиме – Выход, если нет смонтированных томов</h4>
<p>Если включено, работа VeraCrypt в фоновом режиме автоматически и без выдачи сообщений прекращается, как
только в системе не будет смонтированных томов VeraCrypt. См. подробности в главе
<a href="VeraCrypt%20Background%20Task.html">
<em>Работа VeraCrypt в фоновом режиме</em></a>. Обратите внимание, что данный параметр нельзя отключить, если
VeraCrypt выполняется в переносном (portable) режиме.</p>
<h4>Автоматически размонтировать тома при неактивности в течение…</h4>
<p>По прошествии <i>n</i> минут, в течение которых с томом VeraCrypt не выполнялось никаких операций по
записи/чтению данных, этот том будет автоматически размонтирован.</p>
<h4>Автоматически размонтировать тома даже при открытых файлах или папках</h4>
<p>Этот параметр применим только к авторазмонтированию (не к обычному размонтированию). Он форсирует
размонтирование (без выдачи запроса) автоматически размонтируемого тома в случае, если тот содержит открытые
в данный момент файлы или папки (то есть файлы/папки, используемые системой или приложениями).</p>
<p>&nbsp;</p>
<p><a href="Mounting%20VeraCrypt%20Volumes.html" style="text-align:left; color:#0080c0; text-decoration:none; font-weight:bold">Следующий раздел &gt;&gt;</a></p>
</div>
</div><div class="ClearBoth"></div></body></html>
