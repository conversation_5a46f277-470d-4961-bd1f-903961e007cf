<!DOCTYPE html>
<html lang="en">
<head>
<meta http-equiv="content-type" content="text/html; charset=utf-8" />
<title>VeraCrypt - Free Open source disk encryption with strong security for the Paranoid</title>
<meta name="description" content="VeraCrypt is free open-source disk encryption software for Windows, Mac OS X and Linux. In case an attacker forces you to reveal the password, VeraCrypt provides plausible deniability. In contrast to file encryption, data encryption performed by VeraCrypt is real-time (on-the-fly), automatic, transparent, needs very little memory, and does not involve temporary unencrypted files."/>
<meta name="keywords" content="encryption, security"/>
<link href="styles.css" rel="stylesheet" type="text/css" />
</head>
<body>

<div>
<a href="Documentation.html"><img src="VeraCrypt128x128.png" alt="VeraCrypt"/></a>
</div>

<div id="menu">
	<ul>
	  <li><a href="Home.html">Home</a></li>
	  <li><a href="Code.html">Source Code</a></li>
	  <li><a href="Downloads.html">Downloads</a></li>
	  <li><a class="active" href="Documentation.html">Documentation</a></li>
	  <li><a href="Donation.html">Donate</a></li>
	  <li><a href="https://sourceforge.net/p/veracrypt/discussion/" target="_blank">Forums</a></li>
	</ul>
</div>

<div>
<p>
<a href="Documentation.html">Documentation</a>
<img src="arrow_right.gif" alt=">>" style="margin-top: 5px">
<a href="Security%20Requirements%20and%20Precautions.html">Security Requirements and Precautions</a>
<img src="arrow_right.gif" alt=">>" style="margin-top: 5px">
<a href="Changing%20Passwords%20and%20Keyfiles.html">Changing Passwords and Keyfiles</a>
</p></div>

<div class="wikidoc">
<h1>Changing Passwords and Keyfiles</h1>
<p>Note that the volume header (which is encrypted with a header key derived from a password/keyfile) contains the master key (not to be confused with the password) with which the volume is encrypted. If an adversary is allowed to make a copy of your volume
 before you change the volume password and/or keyfile(s), he may be able to use his copy or fragment (the old header) of the VeraCrypt volume to mount your volume using a compromised password and/or compromised keyfiles that were necessary to mount the volume
 before you changed the volume password and/or keyfile(s).<br>
<br>
If you are not sure whether an adversary knows your password (or has your keyfiles) and whether he has a copy of your volume when you need to change its password and/or keyfiles, it is strongly recommended that you create a new VeraCrypt volume and move files
 from the old volume to the new volume (the new volume will have a different master key).<br>
<br>
Also note that if an adversary knows your password (or has your keyfiles) and has access to your volume, he may be able to retrieve and keep its master key. If he does, he may be able to decrypt your volume even after you change its password and/or keyfile(s)
 (because the master key does not change when you change the volume password and/or keyfiles). In such a case, create a new VeraCrypt volume and move all files from the old volume to this new one.<br>
<br>
The following sections of this chapter contain additional information pertaining to possible security issues connected with changing passwords and/or keyfiles:</p>
<ul>
<li><a href="Security%20Requirements%20and%20Precautions.html"><em>Security Requirements and Precautions</em></a>
</li><li><a href="Journaling%20File%20Systems.html"><em>Journaling File Systems</em></a>
</li><li><a href="Defragmenting.html"><em>Defragmenting</em></a>
</li><li><a href="Reallocated%20Sectors.html"><em>Reallocated Sectors</em></a>
</li></ul>
</div><div class="ClearBoth"></div></body></html>
