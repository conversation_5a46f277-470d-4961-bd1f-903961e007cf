﻿<!DOCTYPE html>
<html lang="ru">
<head>
<meta http-equiv="content-type" content="text/html; charset=utf-8" />
<title>VeraCrypt - Бесплатное надёжное шифрование дисков с открытым исходным кодом</title>
<meta name="description" content="VeraCrypt это бесплатное программное обеспечение для шифрования дисков с открытым исходным кодом для Windows, Mac OS X (macOS) и Linux. В случае, если злоумышленник вынуждает вас раскрыть пароль, VeraCrypt обеспечивает правдоподобное отрицание наличия шифрования. В отличие от пофайлового шифрования, VeraCrypt шифрует данные в реальном времени (на лету), автоматически, прозрачно, требует очень мало памяти и не использует временные незашифрованные файлы."/>
<meta name="keywords" content="encryption, security, шифрование, безопасность"/>
<link href="styles.css" rel="stylesheet" type="text/css" />
</head>
<body>

<div>
<a href="Documentation.html"><img src="VeraCrypt128x128.png" alt="VeraCrypt"/></a>
</div>

<div id="menu">
	<ul>
	  <li><a href="Home.html">Начало</a></li>
	  <li><a href="Code.html">Исходный код</a></li>
	  <li><a href="Downloads.html">Загрузить</a></li>
	  <li><a class="active" href="Documentation.html">Документация</a></li>
	  <li><a href="Donation.html">Поддержать разработку</a></li>
	  <li><a href="https://sourceforge.net/p/veracrypt/discussion/" target="_blank">Форум</a></li>
	</ul>
</div>

<div>
<p>
<a href="Documentation.html">Документация</a>
<img src="arrow_right.gif" alt=">>" style="margin-top: 5px">
<a href="Encryption%20Algorithms.html">Алгоритмы шифрования</a>
<img src="arrow_right.gif" alt=">>" style="margin-top: 5px">
<a href="Kuznyechik.html">Kuznyechik («Кузнечик»)</a>
</p></div>
<div class="wikidoc">
<h1>Алгоритм шифрования Kuznyechik</h1>
<p>Kuznyechik («Кузнечик») – это алгоритм блочного шифрования с размером блока 128 бит. Впервые опубликован
в 2015 году и определён в Национальном стандарте Российской Федерации&nbsp;<a href="http://tc26.ru/en/standard/gost/GOST_R_34_12_2015_ENG.pdf">ГОСТ Р 34.12-2015</a>,
а также <a href="https://tools.ietf.org/html/rfc7801">здесь</a>. Он заменяет старый блочный шифр ГОСТ-89, хотя и не делает его устаревшим.</p>
<p>В VeraCrypt используется «Кузнечик» с 10 раундами и 256-битовым ключом, работающий в <a href="Modes%20of%20Operation.html" style="text-align:left; color:#0080c0; text-decoration:none">
режиме XTS</a> (см. раздел <a href="Modes%20of%20Operation.html" style="text-align:left; color:#0080c0; text-decoration:none">
Режимы работы</a>).</p>
<p><a href="Serpent.html" style="text-align:left; color:#0080c0; text-decoration:none; font-weight:bold">Следующий раздел &gt;&gt;</a></p>
</div><div class="ClearBoth"></div></body></html>
