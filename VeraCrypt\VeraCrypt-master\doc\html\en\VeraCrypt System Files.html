<!DOCTYPE html>
<html lang="en">
<head>
<meta http-equiv="content-type" content="text/html; charset=utf-8" />
<title>VeraCrypt - Free Open source disk encryption with strong security for the Paranoid</title>
<meta name="description" content="VeraCrypt is free open-source disk encryption software for Windows, Mac OS X and Linux. In case an attacker forces you to reveal the password, VeraCrypt provides plausible deniability. In contrast to file encryption, data encryption performed by VeraCrypt is real-time (on-the-fly), automatic, transparent, needs very little memory, and does not involve temporary unencrypted files."/>
<meta name="keywords" content="encryption, security"/>
<link href="styles.css" rel="stylesheet" type="text/css" />
</head>
<body>

<div>
<a href="Documentation.html"><img src="VeraCrypt128x128.png" alt="VeraCrypt"/></a>
</div>

<div id="menu">
	<ul>
	  <li><a href="Home.html">Home</a></li>
	  <li><a href="Code.html">Source Code</a></li>
	  <li><a href="Downloads.html">Downloads</a></li>
	  <li><a class="active" href="Documentation.html">Documentation</a></li>
	  <li><a href="Donation.html">Donate</a></li>
	  <li><a href="https://sourceforge.net/p/veracrypt/discussion/" target="_blank">Forums</a></li>
	</ul>
</div>

<div>
<p>
<a href="Documentation.html">Documentation</a>
<img src="arrow_right.gif" alt=">>" style="margin-top: 5px">
<a href="Miscellaneous.html">Miscellaneous</a>
<img src="arrow_right.gif" alt=">>" style="margin-top: 5px">
<a href="VeraCrypt%20System%20Files.html">VeraCrypt System Files</a>
</p></div>

<div class="wikidoc">
<div>
<h1>VeraCrypt System Files &amp; Application Data</h1>
<p>Note: %windir% is the main Windows installation path (e.g., C:\WINDOWS)</p>
<h4>VeraCrypt Driver</h4>
<p>%windir%\SYSTEM32\DRIVERS\veracrypt.sys</p>
<p>Note: This file is not present when VeraCrypt is run in portable mode.</p>
<h4>VeraCrypt Settings, Application Data, and Other System Files</h4>
<p>WARNING: Note that VeraCrypt does <em>not</em> encrypt any of the files listed in this section (unless it encrypts the system partition/drive).<br>
<br>
The following files are saved in the folder %APPDATA%\VeraCrypt\. In portable mode, these files are saved to the folder from which you run the file Vera<em>Crypt.exe</em> (i.e., the folder in which Vera<em>Crypt.exe</em> resides):</p>
<ul>
<li>&quot;Configuration.xml&quot; (the main configuration file). </li></ul>
<ul>
<li>&quot;System Encryption.xml&quot; (temporary configuration file used during the initial process of in-place encryption/decryption of the system partition/drive).
</li></ul>
<ul>
<li>&quot;Default Keyfiles.xml&quot;
<ul>
<li>Note: This file may be absent if the corresponding VeraCrypt feature is not used.
</li></ul>
</li></ul>
<ul>
<li>&quot;Favorite Volumes.xml&quot;
<ul>
<li>Note: This file may be absent if the corresponding VeraCrypt feature is not used.
</li></ul>
</li></ul>
<ul>
<li>&quot;History.xml&quot; (the list of last twenty files/devices attempted to be mounted as VeraCrypt volumes or attempted to be used as hosts for VeraCrypt volumes; this feature can be disabled &ndash; for more information, see the section
<em>Never Save History</em>)
<ul>
<li>Note: This file may be absent if the corresponding VeraCrypt feature is not used.
</li></ul>
</li></ul>
<ul>
<li>&quot;In-Place Encryption&quot; (temporary configuration file used during the initial process of in-place encryption/decryption of a non-system volume).
</li></ul>
<ul>
<li>&quot;In-Place Encryption Wipe Algo&quot; (temporary configuration file used during the initial process of in-place encryption/decryption of a non-system volume).
</li></ul>
<ul>
<li>&quot;Post-Install Task - Tutorial&quot; (temporary configuration file used during the process of installation or upgrade of VeraCrypt).
</li></ul>
<ul>
<li>&quot;Post-Install Task - Release Notes&quot; (temporary configuration file used during the process of installation or upgrade of VeraCrypt).
</li></ul>
<p>The following files are saved in the folder %ALLUSERSPROFILE%\VeraCrypt\:</p>
<ul>
<li>&quot;Original System Loader&quot; (a backup of the original content of the first drive track made before the VeraCrypt Boot Loader was written to it).
<ul>
<li>Note: This file is absent if the system partition/drive has not been encrypted.
</li></ul>
</li></ul>
<p>The following files are saved in the folder %windir%\system32 (32-bit systems) or %windir%\SysWOW64 (64-bit systems):</p>
<ul>
<li>&quot;VeraCrypt System Favorite Volumes.xml&quot;
<ul>
<li>Note: This file may be absent if the corresponding VeraCrypt feature is not used.
</li></ul>
</li></ul>
<ul>
<li>VeraCrypt.exe
<ul>
<li>Note: A copy of this file is located in this folder only when mounting of system favorite volumes is enabled.
</li></ul>
</li></ul>
<p>&nbsp;</p>
</div>
</div><div class="ClearBoth"></div></body></html>
