<!DOCTYPE html>
<html lang="en">
<head>
<meta http-equiv="content-type" content="text/html; charset=utf-8" />
<title>VeraCrypt - Free Open source disk encryption with strong security for the Paranoid</title>
<meta name="description" content="VeraCrypt is free open-source disk encryption software for Windows, Mac OS X and Linux. In case an attacker forces you to reveal the password, VeraCrypt provides plausible deniability. In contrast to file encryption, data encryption performed by VeraCrypt is real-time (on-the-fly), automatic, transparent, needs very little memory, and does not involve temporary unencrypted files."/>
<meta name="keywords" content="encryption, security"/>
<link href="styles.css" rel="stylesheet" type="text/css" />
</head>
<body>

<div>
<a href="Documentation.html"><img src="VeraCrypt128x128.png" alt="VeraCrypt"/></a>
</div>

<div id="menu">
	<ul>
	  <li><a href="Home.html">Home</a></li>
	  <li><a href="Code.html">Source Code</a></li>
	  <li><a href="Downloads.html">Downloads</a></li>
	  <li><a class="active" href="Documentation.html">Documentation</a></li>
	  <li><a href="Donation.html">Donate</a></li>
	  <li><a href="https://sourceforge.net/p/veracrypt/discussion/" target="_blank">Forums</a></li>
	</ul>
</div>

<div>
<p>
<a href="Documentation.html">Documentation</a>
<img src="arrow_right.gif" alt=">>" style="margin-top: 5px">
<a href="Encryption%20Algorithms.html">Encryption Algorithms</a>
<img src="arrow_right.gif" alt=">>" style="margin-top: 5px">
<a href="Kuznyechik.html">Kuznyechik</a>
</p></div>
<div class="wikidoc">
<h1>Kuznyechik</h1>
<p>Kuznyechik is a 128-bit block cipher first published in 2015 and defined in the National Standard of the Russian Federation&nbsp;<a href="http://tc26.ru/en/standard/gost/GOST_R_34_12_2015_ENG.pdf">GOST R 34.12-2015</a> and also in
<a href="https://tools.ietf.org/html/rfc7801">RFC 7801</a>. It supersedes the old GOST-89 block cipher although it doesn't obsolete it.</p>
<p>VeraCrypt uses Kuznyechik with 10 rounds and a 256-bit key operating in <a href="Modes%20of%20Operation.html" style="text-align:left; color:#0080c0; text-decoration:none">
XTS mode</a> (see the section <a href="Modes%20of%20Operation.html" style="text-align:left; color:#0080c0; text-decoration:none">
Modes of Operation</a>).</p>
<p><a href="Serpent.html" style="text-align:left; color:#0080c0; text-decoration:none; font-weight:bold">Next Section &gt;&gt;</a></p>
</div><div class="ClearBoth"></div></body></html>
