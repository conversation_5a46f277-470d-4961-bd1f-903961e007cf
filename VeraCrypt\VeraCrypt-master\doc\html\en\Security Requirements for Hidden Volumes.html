<!DOCTYPE html>
<html lang="en">
<head>
<meta http-equiv="content-type" content="text/html; charset=utf-8" />
<title>VeraCrypt - Free Open source disk encryption with strong security for the Paranoid</title>
<meta name="description" content="VeraCrypt is free open-source disk encryption software for Windows, Mac OS X and Linux. In case an attacker forces you to reveal the password, VeraCrypt provides plausible deniability. In contrast to file encryption, data encryption performed by VeraCrypt is real-time (on-the-fly), automatic, transparent, needs very little memory, and does not involve temporary unencrypted files."/>
<meta name="keywords" content="encryption, security"/>
<link href="styles.css" rel="stylesheet" type="text/css" />
</head>
<body>

<div>
<a href="Documentation.html"><img src="VeraCrypt128x128.png" alt="VeraCrypt"/></a>
</div>

<div id="menu">
	<ul>
	  <li><a href="Home.html">Home</a></li>
	  <li><a href="Code.html">Source Code</a></li>
	  <li><a href="Downloads.html">Downloads</a></li>
	  <li><a class="active" href="Documentation.html">Documentation</a></li>
	  <li><a href="Donation.html">Donate</a></li>
	  <li><a href="https://sourceforge.net/p/veracrypt/discussion/" target="_blank">Forums</a></li>
	</ul>
</div>

<div>
<p>
<a href="Documentation.html">Documentation</a>
<img src="arrow_right.gif" alt=">>" style="margin-top: 5px">
<a href="Plausible%20Deniability.html">Plausible Deniability</a>
<img src="arrow_right.gif" alt=">>" style="margin-top: 5px">
<a href="Hidden%20Volume.html">Hidden Volume</a>
<img src="arrow_right.gif" alt=">>" style="margin-top: 5px">
<a href="Security%20Requirements%20for%20Hidden%20Volumes.html">Security Requirements for Hidden Volumes</a>
</p></div>

<div class="wikidoc">
<h1>Security Requirements and Precautions Pertaining to Hidden Volumes</h1>
<div style="text-align:left; margin-top:19px; margin-bottom:19px; padding-top:0px; padding-bottom:0px">
If you use a <a href="Hidden%20Volume.html" style="text-align:left; color:#0080c0; text-decoration:none">
hidden VeraCrypt volume</a>, you must follow the security requirements and precautions listed below in this section. Disclaimer: This section is not guaranteed to contain a list of
<em style="text-align:left">all</em> security issues and attacks that might adversely affect or limit the ability of VeraCrypt to secure data stored in a hidden VeraCrypt volume and the ability to provide plausible deniability.</div>
<ul style="text-align:left; margin-top:18px; margin-bottom:19px; padding-top:0px; padding-bottom:0px">
<li style="text-align:left; margin-top:0px; margin-bottom:0px; padding-top:0px; padding-bottom:0px">
If an adversary has access to a (unmounted) VeraCrypt volume at several points over time, he may be able to determine which sectors of the volume are changing. If you change the contents of a
<a href="Hidden%20Volume.html" style="text-align:left; color:#0080c0; text-decoration:none">
hidden volume</a> (e.g., create/copy new files to the hidden volume or modify/delete/rename/move files stored on the hidden volume, etc.), the contents of sectors (ciphertext) in the hidden volume area will change. After being given the password to the outer
 volume, the adversary might demand an explanation why these sectors changed. Your failure to provide a plausible explanation might indicate the existence of a hidden volume within the outer volume.<br style="text-align:left">
<br style="text-align:left">
Note that issues similar to the one described above may also arise, for example, in the following cases:<br style="text-align:left">
<ul style="text-align:left; margin-top:18px; margin-bottom:19px; padding-top:0px; padding-bottom:0px">
<li style="text-align:left; margin-top:0px; margin-bottom:0px; padding-top:0px; padding-bottom:0px">
The file system in which you store a file-hosted VeraCrypt container has been defragmented and a copy of the VeraCrypt container (or of its fragment) remains in the free space on the host volume (in the defragmented file system). To prevent this, do one of
 the following:
<ul style="text-align:left; margin-top:18px; margin-bottom:19px; padding-top:0px; padding-bottom:0px">
<li style="text-align:left; margin-top:0px; margin-bottom:0px; padding-top:0px; padding-bottom:0px">
Use a partition/device-hosted VeraCrypt volume instead of file-hosted. </li><li style="text-align:left; margin-top:0px; margin-bottom:0px; padding-top:0px; padding-bottom:0px">
Securely erase free space on the host volume (in the defragmented file system) after defragmenting. On Windows, this can be done using the Microsoft
<a href="https://technet.microsoft.com/en-us/sysinternals/bb897443.aspx">free utility SDelete</a>. On Linux, the
<em>shred</em> utility from GNU coreutils package can be used for this purpose. </li><li style="text-align:left; margin-top:0px; margin-bottom:0px; padding-top:0px; padding-bottom:0px">
Do not defragment file systems in which you store VeraCrypt volumes. </li></ul>
</li><li style="text-align:left; margin-top:0px; margin-bottom:0px; padding-top:0px; padding-bottom:0px">
A file-hosted VeraCrypt container is stored in a journaling file system (such as NTFS). A&nbsp;copy of the VeraCrypt container (or of its fragment) may remain on the host volume. To prevent this, do one the following:
<ul style="text-align:left; margin-top:18px; margin-bottom:19px; padding-top:0px; padding-bottom:0px">
<li style="text-align:left; margin-top:0px; margin-bottom:0px; padding-top:0px; padding-bottom:0px">
Use a partition/device-hosted VeraCrypt volume instead of file-hosted. </li><li style="text-align:left; margin-top:0px; margin-bottom:0px; padding-top:0px; padding-bottom:0px">
Store the container in a non-journaling file system (for example, FAT32). </li></ul>
</li><li style="text-align:left; margin-top:0px; margin-bottom:0px; padding-top:0px; padding-bottom:0px">
A VeraCrypt volume resides on a device/filesystem that utilizes a wear-leveling mechanism (e.g. a flash-memory SSD or USB flash drive). A copy of (a fragment of) the VeraCrypt volume may remain on the device. Therefore, do not store hidden volumes on such devices/filesystems.
 For more information on wear-leveling, see the section <a href="Wear-Leveling.html" style="text-align:left; color:#0080c0; text-decoration:none">
Wear-Leveling</a> in the chapter <a href="Security%20Requirements%20and%20Precautions.html" style="text-align:left; color:#0080c0; text-decoration:none">
Security Requirements and Precautions</a>. </li><li style="text-align:left; margin-top:0px; margin-bottom:0px; padding-top:0px; padding-bottom:0px">
A VeraCrypt volume resides on a device/filesystem that saves data (or on a device/filesystem that is controlled or monitored by a system/device that saves data) (e.g. the value of a timer or counter) that can be used to determine that a block had been written
 earlier than another block and/or to determine how many times a block has been written/read. Therefore, do not store hidden volumes on such devices/filesystems. To find out whether a device/system saves such data, please refer to documentation supplied with
 the device/system or contact the vendor/manufacturer. </li><li style="text-align:left; margin-top:0px; margin-bottom:0px; padding-top:0px; padding-bottom:0px">
A VeraCrypt volume resides on a device that is prone to wear (it is possible to determine that a block has been written/read more times than another block). Therefore, do not store hidden volumes on such devices/filesystems. To find out whether a device is
 prone to such wear, please refer to documentation supplied with the device or contact the vendor/manufacturer.
</li><li style="text-align:left; margin-top:0px; margin-bottom:0px; padding-top:0px; padding-bottom:0px">
You back up content of a hidden volume by cloning its host volume or create a new hidden volume by cloning its host volume. Therefore, you must not do so. Follow the instructions in the chapter
<a href="How%20to%20Back%20Up%20Securely.html" style="text-align:left; color:#0080c0; text-decoration:none">
How to Back Up Securely</a> and in the section <a href="Volume%20Clones.html" style="text-align:left; color:#0080c0; text-decoration:none">
Volume Clones</a>. </li></ul>
</li></ul>
<ul style="text-align:left; margin-top:18px; margin-bottom:19px; padding-top:0px; padding-bottom:0px">
<li style="text-align:left; margin-top:0px; margin-bottom:0px; padding-top:0px; padding-bottom:0px">
Make sure that <em style="text-align:left">Quick Format</em> is disabled when encrypting a partition/device within which you intend to create a hidden volume.
</li><li style="text-align:left; margin-top:0px; margin-bottom:0px; padding-top:0px; padding-bottom:0px">
On Windows, make sure you have not deleted any files within a volume within which you intend to create a hidden volume (the cluster bitmap scanner does not detect deleted files).
</li><li style="text-align:left; margin-top:0px; margin-bottom:0px; padding-top:0px; padding-bottom:0px">
On Linux or Mac OS X, if you intend to create a hidden volume within a file-hosted VeraCrypt volume, make sure that the volume is not sparse-file-hosted (the Windows version of VeraCrypt verifies this and disallows creation of hidden volumes within sparse files).
</li><li style="text-align:left; margin-top:0px; margin-bottom:0px; padding-top:0px; padding-bottom:0px">
When a hidden volume is mounted, the operating system and third-party applications may write to non-hidden volumes (typically, to the unencrypted system volume) unencrypted information about the data stored in the hidden volume (e.g. filenames and locations
 of recently accessed files, databases created by file indexing tools, etc.), the data itself in an unencrypted form (temporary files, etc.), unencrypted information about the filesystem residing in the hidden volume (which might be used e.g. to identify the
 filesystem and to determine whether it is the filesystem residing in the outer volume), the password/key for the hidden volume, or other types of sensitive data. Therefore, the following security requirements and precautions must be followed:
<br style="text-align:left">
<ul style="text-align:left; margin-top:18px; margin-bottom:19px; padding-top:0px; padding-bottom:0px">
<li style="text-align:left; margin-top:0px; margin-bottom:0px; padding-top:0px; padding-bottom:0px">
<em style="text-align:left">Windows</em>: Create a hidden operating system (for information on how to do so, see the section
<a href="Hidden%20Operating%20System.html" style="text-align:left; color:#0080c0; text-decoration:none">
Hidden Operating System</a>) and mount hidden volumes only when the hidden operating system is running.
<span style="text-align:left; font-size:10px; line-height:12px">Note: When a hidden operating system is running, VeraCrypt ensures that all local unencrypted filesystems and non-hidden VeraCrypt volumes are read-only (i.e. no files can be written to such filesystems
 or VeraCrypt volumes).<a href="#hidden_os_exception">*</a> Data is allowed to be written to filesystems within
<a href="Hidden%20Volume.html" style="text-align:left; color:#0080c0; text-decoration:none">
hidden VeraCrypt volumes</a>.</span> Alternatively, if a hidden operating system cannot be used, use a &quot;live-CD&quot; Windows PE system (entirely stored on and booted from a CD/DVD) that ensures that any data written to the system volume is written to a RAM disk.
 Mount hidden volumes only when such a &quot;live-CD&quot; system is running (if a hidden operating system cannot be used). In addition, during such a &quot;live-CD&quot; session, only filesystems that reside in hidden VeraCrypt volumes may be mounted in read-write mode (outer
 or unencrypted volumes/filesystems must be mounted as read-only or must not be mounted/accessible at all); otherwise, you must ensure that applications and the operating system do not write any sensitive data (see above) to non-hidden volumes/filesystems during
 the &quot;live-CD&quot; session. </li><li style="text-align:left; margin-top:0px; margin-bottom:0px; padding-top:0px; padding-bottom:0px">
<em style="text-align:left">Linux</em>: Download or create a &quot;live-CD&quot; version of your operating system (i.e. a &quot;live&quot; Linux system entirely stored on and booted from a CD/DVD) that ensures that any data written to the system volume is written to a RAM disk.
 Mount hidden volumes only when such a &quot;live-CD&quot; system is running. During the session, only filesystems that reside in hidden VeraCrypt volumes may be mounted in read-write mode (outer or unencrypted volumes/filesystems must be mounted as read-only or must
 not be mounted/accessible at all). If you cannot comply with this requirement and you are not able to ensure that applications and the operating system do not write any sensitive data (see above) to non-hidden volumes/filesystems, you must not mount or create
 hidden VeraCrypt volumes under Linux. </li><li style="text-align:left; margin-top:0px; margin-bottom:0px; padding-top:0px; padding-bottom:0px">
<em style="text-align:left">Mac OS X</em>: If you are not able to ensure that applications and the operating system do not write any sensitive data (see above) to non-hidden volumes/filesystems, you must not mount or create hidden VeraCrypt volumes under Mac
 OS X. </li></ul>
</li><li style="text-align:left; margin-top:0px; margin-bottom:0px; padding-top:0px; padding-bottom:0px">
When an outer volume is mounted with <a href="Protection%20of%20Hidden%20Volumes.html" style="text-align:left; color:#0080c0; text-decoration:none">
hidden volume protection</a> enabled (see section <a href="Protection%20of%20Hidden%20Volumes.html">
Protection of Hidden Volumes Against Damage</a>), you must follow the same security requirements and precautions that you are required to follow when a hidden volume is mounted (see above). The reason is that the operating system might leak the password/key
 for the hidden volume to a non-hidden or unencrypted volume. </li><li style="text-align:left; margin-top:0px; margin-bottom:0px; padding-top:0px; padding-bottom:0px">
If you use an <strong style="text-align:left">operating system residing within a hidden volume</strong> (see the section
<a href="Hidden%20Operating%20System.html" style="text-align:left; color:#0080c0; text-decoration:none">
Hidden Operating System</a>), then, in addition to the above, you must follow these security requirements and precautions:
<br style="text-align:left">
<ul style="text-align:left; margin-top:18px; margin-bottom:19px; padding-top:0px; padding-bottom:0px">
<li style="text-align:left; margin-top:0px; margin-bottom:0px; padding-top:0px; padding-bottom:0px">
You should use the decoy operating system as frequently as you use your computer. Ideally, you should use it for all activities that do not involve sensitive data. Otherwise, plausible deniability of the hidden operating system might be adversely affected (if
 you revealed the password for the decoy operating system to an adversary, he could find out that the system is not used very often, which might indicate the existence of a hidden operating system on your computer). Note that you can save data to the decoy
 system partition anytime without any risk that the hidden volume will get damaged (because the decoy system is
<em style="text-align:left">not</em> installed in the outer volume). </li><li style="text-align:left; margin-top:0px; margin-bottom:0px; padding-top:0px; padding-bottom:0px">
If the operating system requires activation, it must be activated before it is cloned (cloning is part of the process of creation of a hidden operating system &mdash; see the section
<a href="Hidden%20Operating%20System.html" style="text-align:left; color:#0080c0; text-decoration:none">
Hidden Operating System</a>) and the hidden operating system (i.e. the clone) must never be reactivated. The reason is that the hidden operating system is created by copying the content of the system partition to a hidden volume (so if the operating system
 is not activated, the hidden operating system will not be activated either). If you activated or reactivated a hidden operating system, the date and time of the activation (and other data) might be logged on a Microsoft server (and on the hidden operating
 system) but not on the <a href="Hidden%20Operating%20System.html" style="text-align:left; color:#0080c0; text-decoration:none">
decoy operating system</a>. Therefore, if an adversary had access to the data stored on the server or intercepted your request to the server (and if you revealed the password for the decoy operating system to him), he might find out that the decoy operating
 system was activated (or reactivated) at a different time, which might indicate the existence of a hidden operating system on your computer.<br style="text-align:left">
<br style="text-align:left">
For similar reasons, any software that requires activation must be installed and activated before you start creating the hidden operating system.
</li><li style="text-align:left; margin-top:0px; margin-bottom:0px; padding-top:0px; padding-bottom:0px">
When you need to shut down the hidden system and start the decoy system, do <em style="text-align:left">
not</em> restart the computer. Instead, shut it down or hibernate it and then leave it powered off for at least several minutes (the longer, the better) before turning the computer on and booting the decoy system. This is required to clear the memory, which
 may contain sensitive data. For more information, see the section <a href="Unencrypted%20Data%20in%20RAM.html" style="text-align:left; color:#0080c0; text-decoration:none">
Unencrypted Data in RAM</a> in the chapter <a href="Security%20Requirements%20and%20Precautions.html" style="text-align:left; color:#0080c0; text-decoration:none">
Security Requirements and Precautions</a>. </li><li style="text-align:left; margin-top:0px; margin-bottom:0px; padding-top:0px; padding-bottom:0px">
The computer may be connected to a network (including the internet) only when the decoy operating system is running. When the hidden operating system is running, the computer should not be connected to any network, including the internet (one of the most reliable
 ways to ensure it is to unplug the network cable, if there is one). Note that if data is downloaded from or uploaded to a remote server, the date and time of the connection, and other data, are typically logged on the server. Various kinds of data are also
 logged on the operating system (e.g. Windows auto-update data, application logs, error logs, etc.) Therefore, if an adversary had access to the data stored on the server or intercepted your request to the server (and if you revealed the password for the decoy
 operating system to him), he might find out that the connection was not made from within the decoy operating system, which might indicate the existence of a hidden operating system on your computer.
<br style="text-align:left">
<br style="text-align:left">
Also note that similar issues would affect you if there were any filesystem shared over a network under the hidden operating system (regardless of whether the filesystem is remote or local). Therefore, when the hidden operating system is running, there must
 be no filesystem shared over a network (in any direction). </li><li style="text-align:left; margin-top:0px; margin-bottom:0px; padding-top:0px; padding-bottom:0px">
Any actions that can be detected by an adversary (or any actions that modify any data outside mounted hidden volumes) must be performed only when the decoy operating system is running (unless you have a plausible alternative explanation, such as using a &quot;live-CD&quot;
 system to perform such actions). For example, the option '<em style="text-align:left">Auto-adjust for daylight saving time</em>' option may be enabled only on the decoy system.
</li><li style="text-align:left; margin-top:0px; margin-bottom:0px; padding-top:0px; padding-bottom:0px">
If the BIOS, EFI, or any other component logs power-down events or any other events that could indicate a hidden volume/system is used (e.g. by comparing such events with the events in the Windows event log), you must either disable such logging or ensure that
 the log is securely erased after each session (or otherwise avoid such an issue in an appropriate way).
</li></ul>
</li></ul>
<div style="text-align:left; margin-top:19px; margin-bottom:19px; padding-top:0px; padding-bottom:0px">
<br style="text-align:left">
In addition to the above, you must follow the security requirements and precautions listed in the following chapters:</div>
<ul style="text-align:left; margin-top:18px; margin-bottom:19px; padding-top:0px; padding-bottom:0px">
<li style="text-align:left; margin-top:0px; margin-bottom:0px; padding-top:0px; padding-bottom:0px">
<a href="Security%20Requirements%20and%20Precautions.html" style="text-align:left; color:#0080c0; text-decoration:none; font-weight:bold">Security Requirements and Precautions</a>
</li><li style="text-align:left; margin-top:0px; margin-bottom:0px; padding-top:0px; padding-bottom:0px">
<strong style="text-align:left"><a href="How%20to%20Back%20Up%20Securely.html" style="text-align:left; color:#0080c0; text-decoration:none">How to Back Up Securely</a></strong>
</li></ul>
<p><a href="VeraCrypt%20Hidden%20Operating%20System.html" style="text-align:left; color:#0080c0; text-decoration:none; font-weight:bold">Next Section &gt;&gt;</a></p>
<hr align="left" size="1" width="189" style="text-align:left; height:0px; border-width:0px 1px 1px; border-style:solid; border-color:#000000">
<p id="hidden_os_exception"><span style="text-align:left; font-size:10px; line-height:12px">* This does not apply to filesystems on CD/DVD-like media and on custom, untypical, or non-standard devices/media.</span></p>
</div><div class="ClearBoth"></div></body></html>
