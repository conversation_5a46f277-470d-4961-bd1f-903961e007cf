<!DOCTYPE html>
<html lang="en">
<head>
<meta http-equiv="content-type" content="text/html; charset=utf-8" />
<title>VeraCrypt - Free Open source disk encryption with strong security for the Paranoid</title>
<meta name="description" content="VeraCrypt is free open-source disk encryption software for Windows, Mac OS X and Linux. In case an attacker forces you to reveal the password, VeraCrypt provides plausible deniability. In contrast to file encryption, data encryption performed by VeraCrypt is real-time (on-the-fly), automatic, transparent, needs very little memory, and does not involve temporary unencrypted files."/>
<meta name="keywords" content="encryption, security"/>
<link href="styles.css" rel="stylesheet" type="text/css" />
</head>
<body>

<div>
<a href="Documentation.html"><img src="VeraCrypt128x128.png" alt="VeraCrypt"/></a>
</div>

<div id="menu">
	<ul>
	  <li><a href="Home.html">Home</a></li>
	  <li><a href="Code.html">Source Code</a></li>
	  <li><a href="Downloads.html">Downloads</a></li>
	  <li><a class="active" href="Documentation.html">Documentation</a></li>
	  <li><a href="Donation.html">Donate</a></li>
	  <li><a href="https://sourceforge.net/p/veracrypt/discussion/" target="_blank">Forums</a></li>
	</ul>
</div>

<div>
<p>
<a href="Documentation.html">Documentation</a>
<img src="arrow_right.gif" alt=">>" style="margin-top: 5px">
<a href="Security%20Requirements%20and%20Precautions.html">Security Requirements and Precautions</a>
<img src="arrow_right.gif" alt=">>" style="margin-top: 5px">
<a href="Multi-User%20Environment.html">Multi-User Environment</a>
</p></div>

<div class="wikidoc">
<div>
<h1>Multi-User Environment</h1>
<p>Keep in mind, that the content of a mounted VeraCrypt volume is visible (accessible) to all logged on users. NTFS file/folder permissions can be set to prevent this, unless the volume is mounted as removable medium (see section
<a href="Removable%20Medium%20Volume.html">
<em>Volume Mounted as Removable Medium</em></a>) under a desktop edition of Windows Vista or later (sectors of a volume mounted as removable medium may be accessible at the volume level to users without administrator privileges, regardless of whether it is
 accessible to them at the file-system level).<br>
<br>
Moreover, on Windows, the password cache is shared by all logged on users (for more information, please see the section
<em>Settings -&gt; Preferences</em>, subsection <em>Cache passwords in driver memory</em>).<br>
<br>
Also note that switching users in Windows XP or later (<em>Fast User Switching</em> functionality) does
<em>not</em> unmount a successfully mounted VeraCrypt volume (unlike system restart, which unmounts all mounted VeraCrypt volumes).<br>
<br>
On Windows 2000, the container file permissions are ignored when a file-hosted VeraCrypt volume is to be mounted. On all supported versions of Windows, users without administrator privileges can mount any partition/device-hosted VeraCrypt volume (provided that
 they supply the correct password and/or keyfiles). A user without administrator privileges can unmount only volumes that he or she mounted. However, this does not apply to system favorite volumes unless you enable the option (disabled by default)
<em>Settings</em> &gt; &lsquo;<em>System Favorite Volumes</em>&rsquo; &gt; &lsquo;<em>Allow only administrators to view and unmount system favorite volumes in VeraCrypt</em>&rsquo;.</p>
</div>
</div><div class="ClearBoth"></div></body></html>
