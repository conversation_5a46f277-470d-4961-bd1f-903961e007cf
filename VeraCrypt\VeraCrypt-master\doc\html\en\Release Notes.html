<!DOCTYPE html>
<html lang="en">
<head>
<meta http-equiv="content-type" content="text/html; charset=utf-8" />
<title>VeraCrypt - Free Open source disk encryption with strong security for the Paranoid</title>
<meta name="description" content="VeraCrypt is free open-source disk encryption software for Windows, Mac OS X and Linux. In case an attacker forces you to reveal the password, VeraCrypt provides plausible deniability. In contrast to file encryption, data encryption performed by VeraCrypt is real-time (on-the-fly), automatic, transparent, needs very little memory, and does not involve temporary unencrypted files."/>
<meta name="keywords" content="encryption, security"/>
<link href="styles.css" rel="stylesheet" type="text/css" />
</head>
<body>

<div>
<a href="Documentation.html"><img src="VeraCrypt128x128.png" alt="VeraCrypt"/></a>
</div>

<div id="menu">
	<ul>
	  <li><a href="Home.html">Home</a></li>
	  <li><a href="Code.html">Source Code</a></li>
	  <li><a href="Downloads.html">Downloads</a></li>
	  <li><a class="active" href="Documentation.html">Documentation</a></li>
	  <li><a href="Donation.html">Donate</a></li>
	  <li><a href="https://sourceforge.net/p/veracrypt/discussion/" target="_blank">Forums</a></li>
	</ul>
</div>

<div>
<p>
<a href="Documentation.html">Documentation</a>
<img src="arrow_right.gif" alt=">>" style="margin-top: 5px">
<a href="Release%20Notes.html">Version History</a>
</p></div>

<div class="wikidoc">
<h1>Release Notes</h1>

<p>
<strong>Note to users who created volumes with 1.17 version of VeraCrypt or earlier: </strong><br/>
<span style="color:#ff0000;">To avoid hinting whether your volumes contain a hidden volume or not, or if you depend on plausible deniability when using hidden volumes/OS, then you must recreate both the outer and hidden volumes including system encryption and hidden OS, discarding existing volumes created prior to 1.18a version of VeraCrypt.</span></li>
</p>

<p><strong style="text-align:left">1.26.26</strong> (June 29<sup>th</sup>, 2025):</p>
<ul>
<li><strong>All OSes:</strong>
	<ul>
	<li>Update logo icons with a simplified ones without extra label text.</li>
	<li>Update documentation.</li>
	<li>Update translations.</li>
	</ul>
</li>
<li><strong>Windows:</strong>
<ul>
<li>Add support of Argon2id password hashing algorithm.</li>
<li>Speedup mounting when PRF autodetection is selected.</li>
<li>Add CLI switch /protectScreen to allow disabling screen protection in portable mode (cf documentation)</li>
<li>Add argument to CLI switch /protectMemory to allow disabling memory protection in portable mode (cf documentation)</li>
<li>Provide VeraCrypt C/C++ SDK for creating volumes (https://github.com/veracrypt/VeraCrypt-SDK)</li>
</ul>
</li>
<li><strong>Linux:</strong>
	<ul>
	<li>Update Ubuntu 25.04 dependency to require libwxgtk3.2-1t64 package</li>
	<li>Allow AppImage file to start with "veracrypt" in any case</li>
	<li>Fix initial width of columns in main UI.</li>
	</ul>
</li>
<li><strong>macOS:</strong>
	<ul>
		<li>Fix initial width of columns in main UI.</li>
	</ul>
</li>
</ul>

<p><strong style="text-align:left">1.26.24</strong> (May 30<sup>th</sup>, 2025):</p>
<ul>
<li><strong>All OSes:</strong>
	<ul>
	<li>Fix whirlpool implementation for big-endian platforms (GH #1529).</li>
	<li>Rename "Dismount All" to "Unmount All" in UI. Use ALT+u as new accelerator key instead of ALT+s</li>
	<li>Add contributed Chinese and Russian CHM documentation.</li>
	<li>Update translations.</li>
	</ul>
</li>
<li><strong>Windows:</strong>
	<ul>
		<li>Implemented screen protection against screenshots and screen recording, enabled by default.
			<ul>
				<li>This feature can be disabled during installation or via the Performance/Driver Configuration settings.</li>
			</ul>
		</li>
		<li>Added checkboxes to the MSI installer to control memory protection and screen protection features.
			<ul>
				<li>Introduced command-line options <code>DISABLEMEMORYPROTECTION</code> and <code>DISABLESCREENPROTECTION</code> for the MSI installer to manage these features.</li>
				<li>Both options accept values <code>0</code> (enabled) or <code>1</code> (disabled).</li>
				<li>Example usage:<br>
					<code>msiexec /i VeraCrypt_Setup_x64_1.26.24.msi DISABLESCREENPROTECTION=1 /qn REBOOT=ReallySuppress MSIRESTARTMANAGERCONTROL=Disable ACCEPTLICENSE=YES</code>
				</li>
			</ul>
		</li>
		<li>Fix race conditions when multiple instances of veracrypt.exe are started simultaneously.</li>
		<li>Updated <code>libzip</code> to version 1.11.3.</li>
	</ul>
</li>
<li><strong>Linux:</strong>
	<ul>
	<li>Add support for AppImage packaging and usage.</li>
	<li>Fix absolute path of 'true' command not being used when checking if sudo session is active.</li>
	<li>Fix failure to use Hungarian language translations.</li>
	<li>Improve generic installer scripts (GH #1514).</li>
	<li>Add support for /run/media/veracrypt as default mount prefix when /media is not available(GH #1524).</li>
	<li>Remove pcsclite dependency from .deb/.rpm packages since it is detected/loaded dynamically at runtime.</li>
	</ul>
</li>
<li><strong>macOS:</strong>
	<ul>
	<li>Fix absolute path of 'true' command not being used when checking if sudo session is active.</li>
	<li>Create simlink of VeraCrypt in <code>/usr/local/bin</code> to allow using it from command line.</li>
	<li>Fix failure to use Hungarian language translations.</li>
	</ul>
</li>
</ul>

<p><strong style="text-align:left">1.26.20</strong> (February 3<sup>rd</sup>, 2025):</p>
<ul>
<li><strong>All OSes:</strong>
	<ul>
	<li>Implement SHA-256 acceleration on ARM64 platforms using CPU instructions.</li>
	<li>Update translations.</li>
	<li>Replace "Dismount" with "Unmount" across the UI and documentation to align with IT standards.</li>
	</ul>
</li>
<li><strong>Windows:</strong>
	<ul>
		<li>Fix regression in driver that always allowed defragmentation and caused other side effects.</li>
		<li>Revert to the previous method of gathering system entropy due to stability issues reported by users.</li>
	</ul>
</li>
<li><strong>Linux:</strong>
	<ul>
	<li>Fix a regression in Linux Mint affecting administrator password authentication (GH #1473).
	<ul>
		<li>/usr/bin/true is now used instead of /usr/bin/uptime to detect active sudo sessions.</li>
		<li>If NOPASSWD used in sudoers with veracrypt and uptime, it should be changed to use /usr/bin/true instead of uptime.</li>
	</ul>
	</li>
	</ul>
</li>
<li><strong>macOS:</strong>
<ul>
<li>Fix a regression that prevented volume unmounting (GH #1467).</li>
<li>Resolve a wxWidgets 3.2.6 assertion error related to the undefined switch <code>use-dummy-sudo-password</code> (GH #1470).</li>
</ul>
</li>
</ul>

<p><strong style="text-align:left">1.26.18</strong> (January 20<sup>th</sup>, 2025):</p>
<ul>
<li><strong>All OSes:</strong>
<ul>
<li>Added support for SHA-256 x86 intrinsic to enhance the performance of PBKDF2-HMAC-SHA256.</li>
<li>Added support for AES hardware on ARM64 platforms (e.g. Windows ARM64, macOS on Apple Silicon Mx).</li>
<li>Updated translations</li>
</ul>
</li>
<li><strong>Windows:</strong>
	<ul>
	<li>Dropped support for Windows 32-bit.</li>
	<li>Set Windows 10 October 2018 Update (version 1809) as the minimum supported version.</li>
	<li>Reduce driver deadlock occurences under low-memory scenarios caused by re-entrant IRP completions.</li>
	<li>Fixed failed EFI detection on some PCs where the BootOrder variable is not defined (proposed by @kriegste, GH #360).</li>
	<li>Fixed "Access Denied" error when updating VeraCrypt using EXE setup following a Windows upgrade.</li>
	<li>Fixed various issues affecting the EFI system encryption configuration editor.</li>
	<li>Fixed regression in Traveler Disk creation (GH #886)</li>
	<li>Replaced the deprecated CryptGenRandom with BCryptGenRandom for generating secure random bytes.</li>
	<li>Use modern API to gather system entropy for random generation instead of obsolete ones.</li>
	<li> Update LZMA SDK to version 24.09</li>
	<li>Update libzip to version 1.11.2</li>
	</ul>
</li>
<li><strong>Linux:</strong>
	<ul>
	<li>CVE-2024-54187: Added absolute paths when executing system binaries to prevent path hijacking (collaboration with SivertPL @__tfr)</li>
	<li>CVE-2025-23021: Prevent mounting volumes on system directories and PATH (reported by SivertPL @__tfr)</li>
	<li>Fixed an assertion issue with the wxWidgets library included in Ubuntu.</li>
	<li>Improved directory-opening logic by prioritizing xdg-open and adding fallback mechanisms.</li>
	<li>Ensure that volume exists before starting the mount operation.</li>
	<li>Fix "Password too long" error message not expanded to include max length (GH #1456)</li>
	<li>Simplify sudo session detection logic.</li>
	</ul>
</li>
<li><strong>macOS:</strong>
	<ul>
	<li>CVE-2024-54187: Added absolute paths when executing system binaries to prevent path hijacking (collaboration with SivertPL @__tfr)</li>
	<li>CVE-2025-23021: Prevent mounting volumes on system directories and PATH (reported by SivertPL @__tfr)</li>
	<li>Disabled screen capture by default. Added the --allow-screencapture CLI switch to enable it if needed.</li>
	<li>Ensure that volume exists before starting the mount operation.</li>
	<li>Implement sudo session detection logic</li>
	</ul>
</li>
</ul>

<p><strong style="text-align:left">1.26.15</strong> (September 2<sup>nd</sup>, 2024):</p>
<ul>
<li><strong>Windows:</strong>
<ul>
	<li>Fix MSI install/uninstall issues:
		<ul>
			<li>Fixed error 1603 returned by MSI silent install when REBOOT=ReallySuppress is specified and a reboot is required.</li>
			<li>Fixed missing documentation and language files from the MSI package.</li>
			<li>Fixed MSI not installing new documentation and language files when upgrading from an EXE-based installation.</li>
			<li>Fixed installation folder not being removed after MSI uninstall in some cases.</li>
		</ul>
	</li>
	<li>Fix regression during UEFI system decryption that caused the bootloader to persist.</li>
</ul>
</li>
</ul>

<p><strong style="text-align:left">1.26.14</strong> (August 25<sup>th</sup>, 2024):</p>
<ul>
<li><strong>All OSes:</strong>
<ul>
<li>Update translations and documentation</li>
<li>Implement language selection settings in non-Windows versions.</li>
<li>Make codebase compatible with wxWidgets 3.3 in non-Windows versions.</li>
<li>Implement detection of volumes affected by XTS master key vulnerability and warn user about it.</li>
<li>Update mount failure error messages to mention removal of TrueCrypt support and old algorithms.</li>
</ul>
</li>
<li><strong>Windows:</strong>
	<ul>
	<li>Better fix for Secure Desktop issues under Windows 11 22H2
		<ul>
			<li>IME is now disabled in Secure Desktop because it is known to cause issues</li>
		</ul>
	</li>
	<li>VeraCrypt Expander: Fix expansion of volumes on disks with a sector size different from 512 (by skl0n6)</li>
	<li>Fix writing wrong EFI System Encryption Advanced Options to registry</li>
	<li>Don't close Setup when exiting VeraCrypt process through system tray Exit menu</li>
	<li>Fix failure to format some disks (e.g. VHDX) caused by virtual partition offset not 4K aligned</li>
	<li>Fallback to absolute positioning when accessing disks if relative positioning fails</li>
	<li>Update zlib to version 1.3.1</li>
	</ul>
</li>
<li><strong>Linux:</strong>
	<ul>
	<li>Focus PIM field when selected (#1239)</li>
	<li>Fix generic installation script on Konsole in Wayland (#1244)</li>
	<li>Added the ability to build using wolfCrypt as the cryptographic backend. Disabled by default. (Contributed by wolfSSL, GH PR #1227)</li>
	<li>Allows GUI to launch in a Wayland-only environment (GH #1264)</li>
	<li>CLI: Don't initially re-ask PIM if it was already specified (GH #1288)</li>
	<li>CLI: Fix incorrect max hidden volume size for file containers (GH #1338))</li>
	<li>Enhance ASLR security of generic installer binaries by adding linked flag for old GCC version (reported by @morton-f on Sourceforge)</li>
	</ul>
</li>
<li><strong>macOS:</strong>
	<ul>
	<li>Fix corrupted disk icon in main UI (GH #1218)</li>
	<li>Fix near zero width PIM input box and simplify wxTextValidator logic (GH #1274)</li>
	<li>Use correct Disk Utility location when "check filesystem" is ran (GH #1273)</li>
	<li>Add support for FUSE-T as an alternative to MacFUSE (GH #1055)</li>
	</ul>
</li>
<li><strong>FreeBSD:</strong>
	<ul>
	<li>Fix privilege escalation prompts not showing up (GH #1349)</li>
	<li>Support automatic detection and mounting of ext2/3/4, exFAT, NTFS filesystems (GH #1350)</li>
	<li>Use correct Disk Utility location when "check filesystem" is ran (GH #1273)</li>
	</ul>
</li>
</ul>

<p><strong style="text-align:left">1.26.7</strong> (October 1<sup>st</sup>, 2023):</p>
<ul>
<li><strong>All OSes:</strong>
<ul>
<li>Security: Ensure that XTS primary key is different from the secondary key when creating volumes
	<ul>
		<li>Issue unlikely to happen thanks to random generator properties but this check must be added to prevent attacks</li>
		<li>Reference: CCSS,NSA comment at page 3: <a href="https://csrc.nist.gov/csrc/media/Projects/crypto-publication-review-project/documents/initial-comments/sp800-38e-initial-public-comments-2021.pdf">https://csrc.nist.gov/csrc/media/Projects/crypto-publication-review-project/documents/initial-comments/sp800-38e-initial-public-comments-2021.pdf</a></li>
	</ul>
</li>
<li>Remove TrueCrypt Mode support. Version 1.25.9 can be used to mount or convert TrueCrypt volumes.</li>
<li>Complete removal of RIPEMD160 and GOST89 algorithms. Legacy volumes using any of them cannot be mounted by VeraCrypt anymore.</li>
<li>Add support for BLAKE2s as new PRF algorithm for both system encryption and standard volumes.</li>
<li>Introducing support for EMV banking smart cards as keyfiles for non-system volumes.
	<ul>
		<li>No need for a separate PKCS#11 module configuration.</li>
		<li>Card PIN isn't required.</li>
		<li>Generates secure keyfile content from unique, encoded data present on the banking card.</li>
		<li>Supports all EMV standard-compliant banking cards.</li>
		<li>Can be enabled in settings (go to Settings->Security Tokens).</li>
		<li>Developed by a team of students from the <a href="https://www.insa-rennes.fr">Institut national des sciences appliquées de Rennes</a>.</li>
		<li>More details about the team and the project are available at <a href="https://projets-info.insa-rennes.fr/projets/2022/VeraCrypt/index_en.html">https://projets-info.insa-rennes.fr/projets/2022/VeraCrypt/index_en.html</a>.</li>
	</ul>
</li>
<li>When overwriting an existing file container during volume creation, add its current size to the available free space</li>
<li>Add Corsican language support. Update several translations. </li>
<li>Update documentation</li>
</ul>
</li>
<li><strong>Windows:</strong>
<ul>
<li>Officially, the minimum supported version is now <strong>Windows 10</strong>. VeraCrypt may still run on Windows 7 and Windows 8/8.1, but no active tests are done on these platforms.</li>
<li>EFI Bootloader:
<ul>
<li>Fix bug in PasswordTimeout value handling that caused it to be limited to 255 seconds.</li>
<li>Rescue Disk: enhance "Boot Original Windows Loader" by using embedded backup of original Windows loader if it is missing from disk</li>
<li>Addition of Blake2s and removal of RIPEMD160 & GOST89</li>
</ul>
</li>
<li>Enable memory protection by default. Add option under Performance/Driver Configuration to disable it if needed.
<ul>
	<li>Memory protection blocks non-admin processes from reading VeraCrypt memory</li>
	<li>It may block Screen Readers (Accessibility support) from reading VeraCrypt UI, in which case it can be disabled</li>
	<li>It can be disabled by setting registry value "VeraCryptEnableMemoryProtection" to 0 under "HKEY_LOCAL_MACHINE\SYSTEM\CurrentControlSet\Services\veracrypt"</li>
</ul>
</li>
<li>Add process mitigation policy to prevent VeraCrypt from being injected by other processes</li>
<li>Minor enhancements to RAM Encryption implementation</li>
<li>Fix Secure Desktop issues under Windows 11 22H2</li>
<li>Implement support for mounting partially encrypted system partitions.</li>
<li>Fix false positive detection of new device insertion when Clear Encryption Keys option is enable (System Encryption case only)</li>
<li>Better implementation of Fast Create when creating file containers that uses UAC to request required privilege if not already held</li>
<li>Allow choosing Fast Create in Format Wizard UI when creating file containers</li>
<li>Fix formatting issues during volume creation on some machines.</li>
<li>Fix stall issue caused by Quick Format of large file containers</li>
<li>Add dropdown menu to Mount button to allow mounting without using the cache.</li>
<li>Possible workaround for logarithmic slowdown for Encrypt-In-Place on large volumes.</li>
<li>Make Expander first check file existence before proceeding further</li>
<li>Allow selecting size unit (KB/MB/GB) for generated keyfiles</li>
<li>Display full list of supported cluster sizes for NTFS, ReFS and exFAT filesystems when creating volumes</li>
<li>Support drag-n-drop of files and keyfiles in Expander.</li>
<li>Implement translation of Expander UI</li>
<li>Replace legacy file/dir selection APIs with modern IFileDialog interface for better Windows 11 compatibility</li>
<li>Enhancements to dependency dlls safe loading, including delay loading.</li>
<li>Remove recommendation of keyfiles files extensions and update documentation to mention risks of third-party file extensions.</li>
<li>Add support for more language in the setup installer</li>
<li>Update LZMA library to version 23.01</li>
<li>Update libzip to version 1.10.1 and zlib to version 1.3</li>
</ul>
</li>
<li><strong>Linux:</strong>
<ul>
<li>Fix bug in Random generator on Linux when used with Blake2s that was triggering a self test failure.</li>
<li>Modify Random Generator on Linux to exactly match official documentation and the Windows implementation.</li> 
<li>Fix compatibility issues with Ubuntu 23.04.</li>
<li>Fix assert messages displayed when using wxWidgets 3.1.6 and newer.</li>
<li>Fix issues launching fsck on Linux.</li>
<li>Fix privilege escalation prompts being ignored.</li>
<li>Fix wrong size for hidden volume when selecting the option to use all free space.</li>
<li>Fix failure to create hidden volume on a disk using CLI caused by wrong maximum size detection.</li>
<li>Fix various issues when running in Text mode:
<ul>
<li>Don't allow selecting exFAT/BTRFS filesytem if they are not present or not compatible with the created volume.</li>
<li>Fix wrong unmount message displayed when mounting a volume.</li>
<li>Hide PIM during entry and re-ask PIM when user entered a wrong value.</li>
<li>Fix printing error when checking free space during volume creation in path doesn't exist.</li>
</ul>
</li>
<li>Use wxWidgets ******* for static builds (e.g. console only version)</li>
<li>Fix compatibility of generic installers with old Linux distros</li>
<li>Update help message to indicate that when cascading algorithms they must be separated by dash</li>
<li>Better compatibility with building under Alpine Linux and musl libc</li>
</ul>
</li>
<li><strong>macOS:</strong>
	<ul>
	<li>Fix issue of VeraCrypt window becoming unusable in use cases involving multiple monitors and change in resolution.</li>
	</ul>
</li>
</ul>

<p><strong style="text-align:left">1.25.9</strong> (February 19<sup>th</sup>, 2022):</p>
<ul>
<li><strong>All OSes:</strong>
<ul>
<li>Update translations (Chinese, Dutch, French, German, Turkish).</li>
</ul>
</li>
<li><strong>Windows:</strong>
<ul>
<li>Make MSI installer compatible with system encryption.</li>
<li>Set minimum support for MSI installation to Windows 7.</li>
<li>Fix failure to create Traveler Disk when VeraCrypt is installed using MSI.</li>
<li>Don't cache the outer volume password when mounting with hidden volume protection if wrong hidden volume password was specified.</li> 
<li>Reduce the size of EXE installers by almost 50% by using LZMA compression instead of DEFLATE.</li>
<li>Fix double-clicking mounted drive in VeraCrypt UI not working in some special Windows configurations.</li>
<li>Add registry key to fix BSOD during shutdown/reboot on some machines when using system encryption.
<ul>
<li>Under "HKEY_LOCAL_MACHINE\SYSTEM\CurrentControlSet\Services\veracrypt", create a REG_DWORD value named "VeraCryptEraseKeysShutdown".</li>
<li>Setting this registry value to 0 disables erasing system encryption keys which is the cause of BSOD during shutdown on some machines.</li>
</ul>
</li>
</ul>
</li>
<li><strong>Linux:</strong>
<ul>
<li>Fix hidden volume settings not correctly displayed when enabling hidden volume protection in mount options window.</li>
<li>Fix generic Linux installer overwriting /usr/sbin if it is a symlink.</li>
<li>Fix crash when building with _GLIBCXX_ASSERTIONS defined.</li>
<li>Enable building from source without AES-NI support.</li>
</ul>
</li>
<li><strong>MacOSX:</strong>
<ul>
<li>Fix hidden volume settings not correctly displayed when enabling hidden volume protection in mount options window.</li>
</ul>
</li>
</ul>
<p><strong style="text-align:left">1.25.7</strong> (January 7<sup>th</sup>, 2022):</p>
<ul>
<li><strong>All OSes:</strong>
<ul>
<li>Update translations.</li>
</ul>
</li>
<li><strong>Windows:</strong>
<ul>
<li>Restore support of Windows Vista, Windows 7 and Windows 8/8.1.
<ul>
<li>Windows 7 support requires that either ********* or ********* is installed.</li>
<li>Windows Vista support requires that either ********* or ********* is installed.</li>
</ul>
</li>
<li>MSI installation only: Fix double-clicking .hc file container inserting %1 instead of volume name in path field.</li>
<li>Advanced users: Add registry settings to control driver internal encryption queue to allow tuning performance for SSD disks and having better stability under heavy load.
<ul>
<li>Under registry key HKEY_LOCAL_MACHINE\SYSTEM\CurrentControlSet\Services\veracrypt:
<ul>
<li>VeraCryptEncryptionFragmentSize (REG_DWORD): size of encryption data fragment in KiB. Default is 256. Maximum is 2048.</li>
<li>VeraCryptEncryptionIoRequestCount (REG_DWORD): maximum number of parallel I/O requests. Default is 16. Maximum is 8192.</li>
<li>VeraCryptEncryptionItemCount (REG_DWORD): maximum number of encryption queue items processed in parallel. Default as well as maximum is half of VeraCryptEncryptionIoRequestCount.</li>
</ul>
</li>
<li>The triplet (FragmentSize=512, IoRequestCount=128, ItemCount=64) is an example of parameters that enhance sequential read speed on some SSD NVMe systems.</li>
<li>Fix truncate text in installer for some languages.</li>
</ul>
</li>
</ul>
<li><strong>MacOSX:</strong>
<ul>
<li>Fix resource files inside VeraCrypt application bundle (e.g. HTML documentation, languages XML files) being world-writable. (Reported by Niall O'Reilly)</li>
</ul>
</li>
</ul>
<p><strong style="text-align:left">1.25.4</strong> (December 3<sup>rd</sup>, 2021):</p>
<ul>
<li><strong>All OSes:</strong>
<ul>
<li>Speed optimization of Streebog.</li>
<li>Update translations.</li>
</ul>
</li>
<li><strong>Windows:</strong>
<ul>
<li>Add support for Windows on ARM64 (e.g. Microsoft Surface Pro X) but system encryption not yet supported.</li>
<li>Add MSI installer for silent mode deployment (ACCEPTLICENSE=YES must be set in msiexec command line).
<ul>
<li>For now, MSI installer cannot be used if system partition is encrypted with VeraCrypt</li>
<li>MSI installer requires Windows 10 or newer</li>
</ul>
</li>
<li>Drop support of Windows Vista, Windows 7, Windows 8 and Windows 8.1 because of new requirement for driver code signing.</li>
<li>Reduce time of mount when PRF auto-detection is selected.</li>
<li>Fix potential memory corruption in driver caused by integer overflow in IOCTL_STORAGE_MANAGE_DATA_SET_ATTRIBUTES (reported by Ilja van Sprundel).</li>
<li>Replace insecure wcscpy/wcscat/strcpy runtime functions with secure equivalents.</li>
<li>Changes to EFI bootloader:
<ul>
<li>Fix memory leak in some cases caused by wrong check of pointer for calling MEM_FREE</li>
<li>Clear bootParams variable that may contain sensitive information when halting the system in case of fatal error</li>
<li>Add option "KeyboardInputDelay" in DcsProp to control the minimum delay supported between two key strokes</li>
</ul></li>
<li>Try to workaround Windows Feature Updates issues with system encryption by fixing of bootloader and SetupConfig.ini when system resumes or when session is opened/unlocked</li>
<li>Fix failure to load local HTML documentation if application running with administrative privileges</li>
<li>Fix freeze when password dialog displayed in secure desktop and try to access token keyfiles protected by PIN</li>
<li>Fix failure to launch keyfile generator in secure desktop mode</li>
<li>Block Windows from resizing system partition if it is encrypted</li>
<li>Add keyboard shortcut to "TrueCrypt mode" in the mount dialog.</li>

</ul>
</li>
<li><strong>MacOSX:</strong>
<ul>
<li>Native support of Apple Silicon M1.</li>
<li>Drop official support of Mac OS X 10.7 Lion and Mac OS X 10.8 Mountain Lion.</li>
<li>Add UI language support using installed XML files. Language is automatically detected using "LANG" environment variable</li>
<li>Add CLI switch (--size=max) and UI option to give a file container all available free space on the disk where it is created.</li>
<li>Return error if unknown filesystem value specified in CLI --filesystem switch instead of silently skipping filesystem creation.</li>
</ul>
</li>
<li><strong>Linux:</strong>
<ul>
<li>Add UI language support using installed XML files. Language is automatically detected using "LANG" environment variable</li>
<li>Compatiblity with with pam_tmpdir.</li>
<li>Display icon in notification area on Ubuntu 18.04 and newer (contibuted by https://unit193.net/).</li>
<li>Add CLI switch (--size=max) and UI option to give a file container all available free space on the disk where it is created.</li>
<li>Return error if unknown filesystem value specified in CLI --filesystem switch instead of silently skipping filesystem creation.</li>
</ul>
</li>
<li><strong>FreeBSD:</strong>
<ul>
<li>Make system devices work under FreeBSD</li>
<li>Add CLI switch (--size=max) and UI option to give a file container all available free space on the disk where it is created.</li>
<li>Return error if unknown filesystem value specified in CLI --filesystem switch instead of silently skipping filesystem creation.</li>
</ul>
</li>
<li><strong>OpenBSD:</strong>
<ul>
<li>Add basic support of OpenBSD</li>
<li>Add CLI switch (--size=max) and UI option to give a file container all available free space on the disk where it is created.</li>
<li>Return error if unknown filesystem value specified in CLI --filesystem switch instead of silently skipping filesystem creation.</li>
</ul>
</li>
</ul>

<p><strong style="text-align:left">1.24-Update8</strong> (November 28<sup>th</sup>, 2020):</p>
<ul>
<li><strong>MacOSX:</strong>
<ul>
<li>Fix compatibility issues with macOS Big Sur, especially on Apple Silicon M1 with macFUSE 4.0.x.</li>
</ul>
</li>
</ul>

<p><strong style="text-align:left">1.24-Update7</strong> (August 7<sup>th</sup>, 2020):</p>
<ul>
<li><strong>All OSes:</strong>
<ul>
<li>Don't allow Hidden volume to have the same password, PIM and keyfiles as Outer volume</li>
<li>Fix random crash in 32-bit builds when using Streebog.</li>
<li>Enable FIPS mode in JitterEntropy random generator.</li>
<li>Update Beginner's Tutorial in documentation to use "MyVolume.hc" instead of "My Volume" for file container name in order to avoid confusion about nature of file nature.</li>
<li>Minor code cleanup</li>
</ul>
</li>
<li><strong>Windows:</strong>
<ul>
<li>Fix wrong results in benchmark of encryption algorithms when RAM encryption is enabled</li>
<li>Fix issue when RAM encryption used, AES selected and AES-NI not supported by CPU that caused the free space of newly created volumes not filled with random data even if "quick format" is not selected.</li>
<li>Fix UI for blocking TRIM in system encryption not working in MBR boot mode.</li>
<li>Support password drag-n-drop from external applications (e.g. KeePass) to password UI fields which is more secure than using clipboard.</li>
<li>Implements compatibility with Windows 10 Modern Standby and Windows 8.1 Connected Standby power model. This makes detection of entring power saving mode more reliable.</li>
<li>Avoid displaying waiting dialog when /silent specified for "VeraCrypt Format" during creating of file container using /create switch and a filesystem other than FAT.</li>
<li>Use native Windows format program to perform formatting of volume since it is more reliable and only fallback to FormatEx function from fmifs.dll in case of issue.</li>
<li>Don't use API for Processor Groups support if there is only 1 CPU group in the system. This can fix slowness issue observed on some PCs with AMD CPUs.</li>
<li>Don't allow to encrypt the system drive if it is already encrypted by BitLocker.</li>
<li>Implement detection of Hibernate and Fast Startup and disable them if RAM encryption is activated.</li>
<li>Warn about Fast Startup if it is enabled during VeraCrypt installation/upgrade, when starting system encryption or when creating a volume, and propose to disable it.</li>
<li>Add UI options to control the behavior of automatic bootloader fixing when System Encryption used.</li>
<li>Don't allow a directory path to be entered for the file container to be created in Format wizard.</li>
<li>Don't try to use fix for CVE-2019-19501 if Windows Shell has been modified or is not running since there is no reliable way to fix it in such non standard configuation.</li>
<li>MBR bootloader: fix incorrect compressed data size passed to decompressor in boot sector.</li>
<li>Add warning message when typed password reaches maximum length during the system encryption wizard.</li>
<li>Fix wrong error message when UTF-8 encoding of entered password exceeds the maximum supported length.</li>
<li>Fix crash when using portable 32-bit "VeraCrypt Format.exe" to create hidden volume on a 64-bit machine where VeraCrypt is already installed.</li>
<li>Update libzip to latest version 1.7.3.</li>
<li>Update translations.</li>
</ul>
</li>
<li><strong>Linux/MacOSX:</strong>
<ul>
<li>Force reading of at least 32 bytes from /dev/random before allowing it to fail gracefully</li>
<li>Allow choosing a filesystem other than FAT for Outer volume but display warning about risks of such choice. Implement an estimation of maximum possible size of hidden volume in this case.</li>
<li>Erase sensitive memory explicitly instead of relying on the compiler not optimizing calls to method Memory::Erase.</li>
<li>Add support for Btrfs filesystem when creating volumes (Linux Only).</li>
<li>Update wxWidgets for static builds to version 3.0.5.</li>
</ul>
</li>
</ul>

<p><strong style="text-align:left">1.24-Update6 </strong>(March 10<sup>th</sup>, 2020):</p>
<ul>
<li><strong>Windows:</strong>
<ul>
<li>Fix PIM label text truncation in password dialog</li>
<li>Fix wrong language used in installer if user selects a language other than English and then selects English before clicking OK on language selection dialog.</li>
</ul>
</li>
</ul>

<p><strong style="text-align:left">1.24-Update5 </strong>(March 9<sup>th</sup>, 2020):</p>
<ul>
<li><strong>Windows:</strong>
<ul>
<li>Optimize performance for CPUs that have more than 64 logical processors (contributed by Sachin Keswani from AMD)</li>
<li>Support specifying keyfiles (both in tokens and in filesystem) when creating file containers using command line (switches /keyfile, /tokenlib and /tokenpin supported in VeraCrypt Format)</li>
<li>Fix leak of keyfiles path and name after VeraCrypt process exits.</li>
<li>Add CLI switch /secureDesktop to VeraCrypt Format.</li>
<li>Update libzip to version 1.6.1</li>
<li>Minor UI fixes</li>
</ul>
</li>
</ul>

<p><strong style="text-align:left">1.24-Update4 </strong>(January 23<sup>rd</sup>, 2020):</p>
<ul>
<li><strong>Windows:</strong>
<ul>
<li>Fix regression in Expander and Format when RAM encryption is enable that was causing volume headers to be corrupted.</li>
<li>Fix failure of Screen Readers (Accessibility support) to read UI by disabling newly introduced memory protection by default and adding a CLI switch (/protectMemory) to enable it when needed.</li>
<li>Fix side effects related to the fix for CVE-2019-19501 which caused links in UI not to open.</li>
<li>Add switch /signalExit to support notifying <a href="https://docs.microsoft.com/en-us/windows-server/administration/windows-commands/waitfor" target="_blank">WAITFOR</a> Windows command when VeraCrypt.exe exits if /q was specified in CLI (cf documentation for usage).</li>
<li>Don't display mount/unmount examples in help dialog for command line in Format and Expander.</li>
<li>Documentation and translation updates.</li>
</ul>
</li>
<li><strong>Linux:</strong>
<ul>
<li>Fix regression that limited the size available for hidden volumes created on disk or partition.</li>
</ul>
</li>
<li><strong>MacOSX:</strong>
<ul>
<li>Fix regression that limited the size available for hidden volumes created on disk or partition.</li>
</ul>
</li>
</ul>

<p><strong style="text-align:left">1.24-Update3 </strong>(December 21<sup>nd</sup>, 2019):</p>
<ul>
<li><strong>Linux:</strong>
<ul>
<li>Fix console-only build to remove dependency on GTK that is not wanted on headless servers.</li>
</ul>
</li>
</ul>

<p><strong style="text-align:left">1.24-Update2 </strong>(December 16<sup>th</sup>, 2019):</p>
<ul>
<li><strong>All OSes:</strong>
<ul>
<li>clear AES key from stack memory when using non-optimized implementation. Doesn't apply to VeraCrypt official build (Reported and fixed by Hanno Böck)</li>
<li>Update Jitterentropy RNG Library to version 2.2.0</li>
<li>Start following IEEE 1541 agreed naming of bytes (KiB, MiB, GiB, TiB, PiB).</li>
<li>Various documentation enhancements.</li>
</ul>
</li>
<li><strong>Windows:</strong>
<ul>
<li>Fix possible local privilege escalation vulnerability during execution of VeraCrypt Expander (CVE-2019-19501)</li>
<li>MBR bootloader:
<ul>
<li>workaround for SSD disks that don't allow write operations in BIOS mode with buffers less than 4096 bytes.</li>
<li>Don't restore MBR to VeraCrypt value if it is coming from a loader different from us or different from Microsoft one.</li>
</ul>
</li>
<li>EFI bootloader:
<ul>
<li>Fix "ActionFailed" not working and add "ActionCancelled" to customize handling of user hitting ESC on password prompt</li>
<li>Fix F5 showing previous password after failed authentication attempt. Ensure that even wrong password value are cleared from memory.</li>
</ul>
</li>
<li>Fix multi-OS boot compatibility by only setting VeraCrypt as first bootloader of the system if the current first bootloader is Windows one.</li>
<li>Add new registry flags for SystemFavoritesService to control updating of EFI BIOS boot menu on shutdown.</li>
<li>Allow system encrypted drive to be mounted in WindowsPE even if changing keyboard layout fails (reported and fixed by Sven Strickroth)</li>
<li>Enhancements to the mechanism preserving file timestamps, especially for keyfiles.</li>
<li>Fix RDRAND instruction not detected on AMD CPUs.</li>
<li>Detect cases where RDRAND is flawed (e.g. AMD Ryzen) to avoid using it if enabled by user.</li>
<li>Don't write extra 0x00 byte at the end of DcsProp file when modifying it through UI</li>
<li>Reduce memory usage of IOCTL_DISK_VERIFY handler used in disk verification by Windows.</li>
<li>Add switch /FastCreateFile for VeraCrypt Format.exe to speedup creation of large file container if quick format is selected.</li>
<li>Fix the checkbox for skipping verification of Rescue Disk not reflecting the value of /noisocheck switch specified in VeraCrypt Format command line.</li>
<li>check "TrueCrypt Mode" in password dialog when mounting a file container with .tc extension</li>
<li>Update XML languages files.</li>
</ul>
</li>
<li><strong>Linux:</strong>
<ul>
<li>Fix regression causing admin password to be requested too many times in some cases</li>
<li>Fix off by one buffer overflow in function Process::Execute (Reported and fixed by Hanno Böck)</li>
<li>Make sure password gets deleted in case of internal error when mounting volume (Reported and fixed by Hanno Böck)</li>
<li>Fix passwords using Unicode characters not recognized in text mode.</li>
<li>Fix failure to run VeraCrypt binary built for console mode on headless machines.</li>
<li>Add switch to force the use of legacy maximum password length (64 UTF8 bytes)</li>
<li>Add CLI switch (--use-dummy-sudo-password) to force use of old sudo behavior of sending a dummy password</li>
<li>During uninstall, output error message to STDERR instead of STDOUT for better compatibility with package managers.</li>
<li>Make sector size mismatch error when mounting disks more verbose.</li>
<li>Speedup SHA256 in 64-bit mode by using assembly code.</li>
</ul>
</li>
<li><strong>MacOSX:</strong>
<ul>
<li>Add switch to force the use of legacy maximum password length (64 UTF8 bytes)</li>
<li>Fix off by one buffer overflow in function Process::Execute (Reported and fixed by Hanno Böck)</li>
<li>Fix passwords using Unicode characters not recognized in text mode.</li>
<li>Make sector size mismatch error when mounting disks more verbose.</li>
<li>Speedup SHA256 in 64-bit mode by using assembly code.</li>
<li>Link against latest wxWidgets version 3.1.3</li>
</ul>
</li>
</ul>


<p><strong style="text-align:left">1.24-Hotfix1 </strong>(October 27<sup>rd</sup>, 2019):</p>
<ul>
<li><strong>Windows:</strong>
<ul>
<li>Fix 1.24 regression that caused system favorites not to mount at boot if VeraCrypt freshly installed.</li>
<li>Fix failure to encrypt system if the current Windows username contains a Unicode non-ASCII character.</li>
<li>Make VeraCrypt Expander able to resume expansion of volumes whose previous expansion was aborted before it finishes.</li>
<li>Add "Quick Expand" option to VeraCrypt Expander to accelarate the expansion of large file containers.</li>
<li>Add several robustness checks and validation in case of system encryption to better handle some corner cases.</li>
<li>Minor UI and documentation changes.</li>
</ul>
</li>
<li><strong>Linux:</strong>
<ul>
<li>Workaround gcc 4.4.7 bug under CentOS 6 that caused VeraCrypt built under CentOS 6 to crash when Whirlpool hash is used.</li>
<li>Fix "incorrect password attempt" written to /var/log/auth.log when mounting volumes.</li>
<li>Fix dropping file in UI not showing its correct path , specifically under GTK-3.</li>
<li>Add missing JitterEntropy implementation/</li>
</ul>
</li>
<li><strong>MacOSX:</strong>
<ul>
<li>Fix some devices and partitions not showing in the device selection dialog under OSX 10.13 and newer.</li>
<li>Fix keyboard tab navigation between password fields in "Volume Password" page of volume creation wizard.</li>
<li>Add missing JitterEntropy implementation/</li>
<li>Support APFS filesystem for creation volumes.</li>
<li>Support Dark Mode.</li>
</ul>
</li>
</ul>


<p><strong style="text-align:left">1.24 </strong>(October 6<sup>th</sup>, 2019):</p>
<ul>
<li><strong>All OSs:</strong>
<ul>
<li>Increase password maximum length to 128 bytes in UTF-8 encoding for non-system volumes.</li>
<ul>
<li>Add option to use legacy maximum password length (64) instead of new one for compatibility reasons.</li>
</ul>
<li>Use Hardware RNG based on CPU timing jitter "Jitterentropy" by Stephan Mueller as a good alternative to CPU RDRAND (<a href="http://www.chronox.de/jent.html" target="_blank">http://www.chronox.de/jent.html</a>)</li>
<li>Speed optimization of XTS mode on 64-bit machine using SSE2 (up to 10% faster).</li>
<li>Fix detection of CPU features AVX2/BMI2. Add detection of RDRAND/RDSEED CPU features. Detect Hygon CPU as AMD one.</li>
</ul>
</li>
<li><strong>Windows:</strong>
<ul>
<li>Implement RAM encryption for keys and passwords using ChaCha12 cipher, t1ha non-cryptographic fast hash and ChaCha20 based CSPRNG.</li>
<ul>
<li>Available only on 64-bit machines.</li>
<li>Disabled by default. Can be enabled using option in UI.</li>
<li>Less than 10% overhead on modern CPUs.</li>
<li>Side effect: Windows Hibernate is not possible if VeraCrypt System Encryption is also being used.</li>
</ul>
<li>Mitigate some memory attacks by making VeraCrypt applications memory inaccessible to non-admin users (based on KeePassXC implementation)</li>
<li>New security features:</li>
<ul>
<li>Erase system encryption keys from memory during shutdown/reboot to help mitigate some cold boot attacks</li>
<li>Add option when system encryption is used to erase all encryption keys from memory when a new device is connected to the system.</li>
<li>Add new driver entry point that can be called by applications to erase encryption keys from memory in case of emergency.</li>
</ul>
<li>MBR Bootloader: dynamically determine boot loader memory segment instead of hardcoded values (proposed by neos6464)</li>
<li>MBR Bootloader: workaround for issue affecting creation of hidden OS on some SSD drives.</li>
<li>Fix issue related to Windows Update breaking VeraCrypt UEFI bootloader.</li>
<li>Several enhancements and fixes for EFI bootloader:</li>
<ul>
<li>Implement timeout mechanism for password input. Set default timeout value to 3 minutes and default timeout action to "shutdown".</li>
<li>Implement new actions "shutdown" and "reboot" for EFI DcsProp config file.</li>
<li>Enhance Rescue Disk implementation of restoring VeraCrypt loader.</li>
<li>Fix ESC on password prompt during Pre-Test not starting Windows.</li>
<li>Add menu entry in Rescue Disk that enables starting original Windows loader.</li>
<li>Fix issue that was preventing Streebog hash from being selected manually during Pre-Boot authentication.</li>
<li>If "VeraCrypt" folder is missing from Rescue Disk, it will boot PC directly from bootloader stored on hard drive</li>
<ul>
<li>This makes it easy to create a bootable disk for VeraCrypt from Rescue Disk just by removing/renaming its "VeraCrypt" folder.</li>
</ul>
</ul>
<li>Add option (disabled by default) to use CPU RDRAND or RDSEED as an additional entropy source for our random generator when available.</li>
<li>Add mount option (both UI and command line) that allows mounting a volume without attaching it to the specified drive letter.</li>
<li>Update libzip to version 1.5.2</li>
<li>Do not create uninstall shortcut in startmenu when installing VeraCrypt. (by Sven Strickroth)</li>
<li>Enable selection of Quick Format for file containers creation. Separate Quick Format and Dynamic Volume options in the wizard UI.</li>
<li>Fix editor of EFI system encryption configuration file not accepting ENTER key to add new lines.</li>
<li>Avoid simultaneous calls of favorites mounting, for example if corresponding hotkey is pressed multiple times.</li>
<li>Ensure that only one thread at a time can create a secure desktop.</li>
<li>Resize some dialogs in Format and Mount Options to to fix some text truncation issues with non-English languages.</li>
<li>Fix high CPU usage when using favorites and add switch to disable periodic check on devices to reduce CPU load.</li>
<li>Minor UI changes.</li>
<li>Updates and corrections to translations and documentation.</li>
</ul>
</li>
<li><strong>MacOSX:</strong>
<ul>
<li>Add check on size of file container during creation to ensure it's smaller than available free disk space. Add CLI switch --no-size-check to disable this check.</li>
</ul>
</li>
<li><strong>Linux:</strong>
<ul>
<li>Make CLI switch --import-token-keyfiles compatible with Non-Interactive mode.</li>
<li>Add check on size of file container during creation to ensure it's smaller than available free disk space. Add CLI switch --no-size-check to disable this check.</li>
</ul>
</li>
</ul>

<p><strong style="text-align:left">1.23-Hotfix-2 </strong>(October 8<sup>th</sup>, 2018):</p>
<ul>
<li><strong>Windows:</strong>
<ul>
<li>Fix low severity vulnerability inherited from TrueCrypt that allowed reading 3 bytes of kernel stack memory (with a rare possibility of 25 additional bytes).
<ul>
<li>Reported by Tim Harrison.</li>
</ul>
</li>
<li>Disable quick format when creating file containers from command line. Add /quick switch to enable it in this case if needed.</li>
<li>Add /nosizecheck switch to disable checking container size against available free space during its creation.
<ul>
<li>This enables to workaround a bug in Microsoft Distributed File System (DFS).</li>
</ul>
</li>
</ul>
</li>
</ul>

<p><strong style="text-align:left">1.23 </strong>(September 12<sup>th</sup>, 2018):</p>
<ul>
<li><strong>Windows:</strong>
<ul>
<li>VeraCrypt is now compatible with default EFI SecureBoot configuration for system encryption.</li>
<li>Fix EFI system encryption issues on some machines (e.g. HP, Acer).</li>
<li>Support EFI system encryption on Windows LTSB.</li>
<li>Add compatibility of system encryption with Windows 10 upgrade using ReflectDrivers mechanism</li>
<li>Make EFI Rescue Disk decrypt partition correctly when Windows Repair overwrites first partition sector.</li>
<li>Add Driver option in the UI to explicitly allow Windows 8.1 and Windows 10 defragmenter to see VeraCrypt encrypted disks.</li>
<li>Add internal verification of binaries embedded signature to protect against some types to tampering attacks.</li>
<li>Fix Secure Desktop not working for favorites set to mount at logon on Windows 10 under some circumstances.</li>
<li>when Secure Desktop is enabled, use it for Mount Options dialog if it is displayed before password dialog.</li>
<li>when extracting files in Setup or Portable mode, decompress zip files docs.zip and Languages.zip in order to have ready to use configuration.</li>
<li>Display a balloon tip warning message when text pasted to password field is longer than maximum length and so it will be truncated.</li>
<li>Implement language selection mechanism at the start of the installer to make easier for international users.</li>
<li>Add check on size of file container during creation to ensure it's smaller than available free disk space.</li>
<li>Fix buttons at the bottom not shown when user sets a large system font under Window 7.</li>
<li>Fix compatibility issues with some disk drivers that don't support IOCTL_DISK_GET_DRIVE_GEOMETRY_EX ioctl.</li>
</ul>
</li>
<li><strong>MacOSX:</strong>
<ul>
<li>Support pasting values to password fields using keyboard (CMD+V and CMD+A now working properly).
<li>Add CheckBox in mount option dialog to force the use of embedded backup header during mount.</li>
<li>When performing backup of volume header, automatically try to use embedded backup header if using the main header fails.</li>
<li>Implement benchmarking UI for Hash and PKCS-5 PRF algorithms.</li>
</ul>
</li>
<li><strong>Linux:</strong>
<ul>
<li>Don't allow waiting dialog to be closed before the associated operation is finished. This fix a crash under Lubuntu 16.04.
<li>Add CheckBox in mount option dialog to force the use of embedded backup header during mount.</li>
<li>When performing backup of volume header, automatically try to use embedded backup header if using the main header fails.</li>
<li>Implement benchmarking UI for Hash and PKCS-5 PRF algorithms.</li>
<li>Remove limitation of hidden volume protection on disk with sector size larger than 512 bytes.</li>
</ul>
</li>
</ul>


<p><strong style="text-align:left">1.22 </strong>(March 30<sup>th</sup>, 2018):</p>
<ul>
<li><strong>All OSs:</strong>
<ul>
<li>SIMD speed optimization for Kuznyechik cipher implementation (up to 2x speedup).</li>
<li>Add 5 new cascades of cipher algorithms: Camellia-Kuznyechik, Camellia-Serpent, Kuznyechik-AES, Kuznyechik-Serpent-Camellia and Kuznyechik-Twofish.</li>
</ul>
</li>
<li><strong>Windows:</strong>
<ul>
<li>MBR Bootloader: Fix failure to boot hidden OS on some machines.</li>
<li>MBR Bootloader: Reduce CPU usage during password prompt.</li>
<li>Security enhancement: Add option to block TRIM command for system encryption on SSD drives.</li>
<li>Implement TRIM support for non-system SSD drives and add option to enable it (TRIM is disabled by default for non-system volumes).</li>
<li>Better fix for "Parameter Incorrect" issues during EFI system encryption in some machines.</li>
<li>Driver: remove unnecessary dependency to wcsstr which can cause issues on some machines.</li>
<li>Driver: Fix "Incorrect Parameter" error when mounting volumes on some machines.</li>
<li>Fix failure to mount system favorites during boot on some machines.</li>
<li>Fix current application losing focus when VeraCrypt is run in command line with /quit /silent switches.</li>
<li>Fix some cases of external applications freezing during mount/unmount.</li>
<li>Fix rare cases of secure desktop for password dialog not visible which caused UI to block.</li>
<li>Update libzip to version 1.5.0 that include fixes for some security issues.</li>
<li>Extend Secure Desktop feature to smart card PIN entry dialog.</li>
<li>Fix truncated license text in installer wizard.</li>
<li>Add portable package that allows extracting binaries without asking for admin privileges.</li>
<li>Simplify format of language XML files.</li>
<li>Workaround for cases where password dialog doesn't get keyboard focus if Secure Desktop is not enabled.</li>
</ul>
<li><strong>Linux:</strong>
<ul>
<li>Fix failure to install GUI version under recent versions of KDE.</li>
<li>Fix wxWidgets assertion failed when backing up/restoring volume header.</li>
</ul>
</li>
<li><strong>MacOSX:</strong>
<ul>
<li>Fix issue preventing some local help files from opening in the browser.</li>
</ul>
</li>
</ul>

<p><strong style="text-align:left">1.21 </strong>(July 9<sup>th</sup>, 2017):</p>
<ul>
<li><strong>All OSs:</strong>
<ul>
<li>Fix 1.20 regression crash when running on CPU not supporting extended features.</li>
</ul>
</li>
<li><strong>Windows:</strong>
<ul>
<li>Fix 1.20 regression that caused PIM value stored in favorites to be ignored during mount.</li>
<li>Fix 1.20 regression that causes system favorites not to mount in some cases.</li>
<li>Fix some cases of "Parameter Incorrect" error during EFI system encryption wizard.</li>
<li>Install PDF documents related to EFI system encryption configuration for advanced users:
<ul>
<li>disk_encryption_v1_2.pdf related to EFI hidden OS and full fisk encryption</li>
<li>dcs_tpm_owner_02.pdf related to TPM configuration for EFI system encryption.</li>
</ul>
</li>
</ul>
</li>
<li><strong>FreeBSD:</strong>
<ul>
<li>Add support for building on FreeBSD.</li>
</ul>
</li>
</ul>

<p><strong style="text-align:left">1.20 </strong>(June 29<sup>th</sup>, 2017):</p>
<ul>
<li><strong>All OSs:</strong>
<ul>
<li>Use 64-bit optimized assembly implementation of Twofish and Camellia by Jussi Kivilinna.
<ul>
<li>Camellia 2.5 faster when AES-NI supported by CPU. 30% faster without it.</li>
</ul>
</li>
<li>Use optimized implementation for SHA-512/SHA256.
<ul>
<li>33% speedup on 64-bit systems.</li>
</ul>
</li>
<li>Deploy local HTML documentation instead of User Guide PDF.</li>
<li>Change links in UI from ones on Codeplex to ones hosted at veracrypt.fr </li>
<li>Security: build binaries with support for Address Space Layout Randomization (ASLR).</li>
</ul>
</li>
<li><strong>Windows:</strong>
<ul>
<li>Several fixes and modifications for EFI System Encryption:
<ul>
<li>Fix bug in EFI system decryption using EFI Rescue Disk</li>
<li>Add support for TPM 1.2 and TPM 2.0 (experimental) through DCS low level configuration.
<ul>
<li><a href="https://dc5.sourceforge.io/docs/dcs_tpm_owner_02.pdf" target="_blank">https://dc5.sourceforge.io/docs/dcs_tpm_owner_02.pdf</a>
</li>
</ul>
<li>Add Support for EFI full disk encryption and hidden OS using manual procedure (not exposed in UI).
<ul>
<li><a href="https://dc5.sourceforge.io/docs/disk_encryption_v1_2.pdf" target="_blank">https://dc5.sourceforge.io/docs/disk_encryption_v1_2.pdf</a>
</li>
</ul>
</li>
</ul>
</li>

<li>Enable using Secure Desktop for password entry. Add preferences option and command line switch (/secureDesktop) to activate it.</li>
<li>Use default mount parameters when mounting multiple favorites with password caching.</li>
<li>Enable specifying PRF and TrueCryptMode for favorites.</li>
<li>Preliminary driver changes to support EFI hidden OS functionality.</li>
<li>Fix Streebog not recognized by /hash command line.</li>
<li>Add support for ReFS filesystem on Windows 10 when creating normal volumes</li>
<li>Fix high CPU usage when favorite configured to mount with VolumeID on arrival.</li>
<li>Use CHM file for User Guide instead of PDF.</li>
<li>Fix false warning in case of EFI system encryption about Windows not installed on boot drive.</li>
<li>Enhancements to driver handling of various disk IOCTL.</li>
<li>Enhancements to EFI bootloader. Add possibility to manually edit EFI configuration file.</li>
<li>Driver Security: Use enhanced protection of NX pool under Windows 8 and later.</li>
<li>Reduce performance impact of internal check for disconnected network drives.</li>
<li>Minor fixes.</li>
</ul>
</li>
<li><strong>MacOSX:</strong>
<ul>
<li>OSX 10.7 or newer is required to run VeraCrypt.</li>
<li>Make VeraCrypt default handler of .hc & .tc files.</li>
<li>Add custom VeraCrypt icon to .hc and .tc files in Finder.</li>
<li>Check TrueCryptMode in password dialog when opening container file with .tc extension.</li>
</ul>
</li>
<li><strong>Linux:</strong>
<ul>
<li>Check TrueCryptMode in password dialog when opening container file with .tc extension.</li>
<li>Fix executable stack in resulting binary which was caused by crypto assembly files missing the GNU-stack note.</li>
</ul>
</li>
</ul>

<p><strong style="text-align:left">1.19 </strong>(October 17<sup>th</sup>, 2016):</p>
<ul>
<li><strong>All OSs:</strong>
<ul>
<li>Fix issues raised by Quarkslab audit.
<ul>
<li>Remove GOST89 encryption algorithm. </li><li>Make PBKDF2 and HMAC code clearer and easier to analyze. </li><li>Add test vectors for Kuznyechik. </li><li>Update documentation to warn about risks of using command line switch &rdquo;tokenpin&rdquo;.
</li></ul>
</li><li>Use SSE2 optimized Serpent algorithm implementation from Botan project (2.5 times faster on 64-bit platforms).
</li></ul>
</li><li><strong>Windows:</strong>
<ul>
<li>Fix keyboard issues in EFI Boot Loader. </li><li>Fix crash on 32-bit machines when creating a volume that uses Streebog as PRF.
</li><li>Fix false positive detection of Evil-Maid attacks in some cases (e.g. hidden OS creation)
</li><li>Fix failure to access EFS data on VeraCrypt volumes under Windows 10. </li><li>Fix wrong password error in the process of copying hidden OS. </li><li>Fix issues raised by Quarkslab audit:
<ul>
<li>Fix leak of password length in MBR bootloader inherited from TrueCrypt. </li><li>EFI bootloader: Fix various leaks and erase keyboard buffer after password is typed.
</li><li>Use libzip library for handling zip Rescue Disk file instead of vulnerable XUnzip library.
</li></ul>
</li><li>Support EFI system encryption for 32-bit Windows. </li><li>Perform shutdown instead of reboot during Pre-Test of EFI system encryption to detect incompatible motherboards.
</li><li>Minor GUI and translations fixes. </li></ul>
</li><li><strong>MacOSX:</strong>
<ul>
<li>Remove dependency to MacFUSE compatibility layer in OSXFuse. </li></ul>
</li></ul>
<p>&nbsp;</p>
<p><strong style="text-align:left">1.18a </strong>(August 17<sup>th</sup>, 2016):</p>
<ul>
<li><strong>All OSs:</strong>
<ul>
<li>Support Japanese encryption standard Camellia, including for Windows system encryption (MBR &amp; EFI).
</li><li>Support Russian encryption and hash standards Kuznyechik, Magma and Streebog, including for Windows EFI system encryption.
</li><li>Fix TrueCrypt vulnerability allowing detection of hidden volumes presence (reported by Ivanov Aleksey Mikhailovich, alekc96 [at] mail dot ru)
<ul><li> <strong style="color:#ff0000;">To avoid hinting whether your volumes contain a hidden volume or not, or if you depend on plausible deniability when using hidden volumes/OS, then you must recreate both the outer and hidden volumes including system encryption and hidden OS, discarding existing volumes created prior to 1.18a version of VeraCrypt.</strong></li></ul>
</li></ul>
</li><li><strong>Windows:</strong>
<ul>
<li>Support EFI Windows system encryption (limitations: no hidden os, no boot custom message)
</li><li>Enhanced protection against dll hijacking attacks. </li><li>Fix boot issues on some machines by increasing required memory by 1 KiB </li><li>Add benchmarking of hash algorithms and PRF with PIM (including for pre-boot).
</li><li>Move build system to Visual C&#43;&#43; 2010 for better stability. </li><li>Workaround for AES-NI support under Hyper-V on Windows Server 2008 R2. </li><li>Correctly remove driver file veracrypt.sys during uninstall on Windows 64-bit.
</li><li>Implement passing smart card PIN as command line argument (/tokenpin) when explicitly mounting a volume.
</li><li>When no drive letter specified, choose A: or B: only when no other free drive letter is available.
</li><li>Reduce CPU usage caused by the option to disable use of disconnected network drives.
</li><li>Add new volume ID mechanism to be used to identify disks/partitions instead of their device name.
</li><li>Add option to avoid PIM prompt in pre-boot authentication by storing PIM value unencrypted in MBR.
</li><li>Add option and command line switch to hide waiting dialog when performing operations.
</li><li>Add checkbox in &quot;VeraCrypt Format&quot; wizard GUI to skip Rescue Disk verification during system encryption procedure.
</li><li>Allow files drag-n-drop when VeraCrypt is running as elevated process. </li><li>Minor GUI and translations fixes. </li></ul>
</li><li><strong>Linux:</strong>
<ul>
<li>Fix mount issue on Fedora 23. </li><li>Fix mount failure when compiling source code using gcc 5.x. </li><li>Adhere to XDG Desktop Specification by using XDG_CONFIG_HOME to determine location of configuration files.
</li></ul>
</li><li><strong>MacOSX:</strong>
<ul>
<li>Solve compatibility issue with newer versions of OSXFuse. </li></ul>
</li></ul>
<p>&nbsp;</p>
<p><strong style="text-align:left">1.17 </strong>(February 13<sup>th</sup>, 2016):</p>
<ul>
<li><strong>All OSs:</strong>
<ul>
<li>Support UNICODE passwords: all characters are now accepted in passwords (except Windows system encryption)
</li><li>Cut mount/boot time by half thanks to a clever optimization of key derivation (found by
<a href="https://madiba.encs.concordia.ca/~x_decarn/" target="_blank">Xavier de Carn&eacute; de Carnavalet</a>)
</li><li>Optimize Whirlpool PRF speed by using assembly (25% speed gain compared to previous code).
</li><li>Add support for creating exFAT volumes. </li><li>Add GUI indicator for the amount of randomness gathered using mouse movement.
</li><li>Include new icons and graphics contributed by <em>Andreas Becker</em> (<a href="http://www.andreasbecker.de" target="_blank">http://www.andreasbecker.de</a>)
</li></ul>
</li><li><strong>Windows:</strong>
<ul>
<li>Fix dll hijacking issue affecting installer that allows code execution with elevation of privilege (CVE-2016-1281). Reported by Stefan Kanthak (<a href="http://home.arcor.de/skanthak/" target="_blank">http://home.arcor.de/skanthak/</a>)
</li><li>Sign binaries using both SHA-1 and SHA-256 to follow new Microsoft recommendations.
</li><li>Solve issues under Comodo/Kaspersky when running an application from a VeraCrypt volume (Reported and fixed by Robert Geisler).
</li><li>Bootloader: Protect password/PIM length by filling the fields to maximum length with '*' after ENTER
</li><li>Solve issue with system favorites not being able to be mounted to drive A: </li><li>Solve lost focus issues for after displaying the waiting dialog </li><li>Solve rare issue where some partitions where asscoiated with wrong disk the &quot;Select Device&quot; dialog.
</li><li>Implement PIM caching, for both system encryption and normal volumes. Add option to activate it.
</li><li>Don't try mounting using cached passwords if password and/or keyfile are specified in the command line.
</li><li>Internal rewrite to make VeraCrypt native UNICODE application. </li><li>Workaround to avoid false positive detection by some anti-virus software. </li><li>Hide disconnected network drives in the list of available drives. Add option to make them available for mounting.
</li><li>Solve issue that caused in some cases configuration and history XML files to be updated even when not needed.
</li><li>Fix leak of path of selected keyfiles in RAM. </li><li>Fix TB unit can't be deselected in VeraCryptExpander. </li><li>Add Alt&#43;i keyboard shortcut for &quot;Use PIM&quot; checkbox in GUI. </li><li>Minor GUI and translations fixes. </li></ul>
</li><li><strong>Linux/MacOSX:</strong>
<ul>
<li>Fix issue of --stdin option not handling correctly passwords that contain a space character (reported and fixed by Codeplex user horsley1953).
</li><li>Fix issue creating volumes using command line with a filesystem other than FAT.
</li><li>Support K/M/G/T suffixes for --size switch to indicate unit to use for size value.
</li></ul>
</li></ul>
<p id="116"><strong style="text-align:left">1.16 (October 7<sup>th</sup>, 2015):</strong></p>
<ul>
<li><strong><strong>Windows:</strong></strong>
<ul>
<li>Modify patch for CVE-2015-7358 vulnerability to solve side effects on Windows while still making it very hard to abuse drive letter handling.
</li><li>Fix failure to restore volume header from an external file in some configurations.
</li><li>Add option to disable &ldquo;Evil Maid&rdquo; attack detection for those encountering false positive cases (e.g. FLEXnet/Adobe issue).
</li><li>By default, don&rsquo;t try to mount using empty password when default keyfile configured or keyfile specified in command line. Add option to restore the old behavior.
<ul>
<li>If mounting using empty password is needed, explicitly specify so in the command line using: /p &quot;&quot;
</li></ul>
</li></ul>
</li></ul>
<p><strong style="text-align:left">1.15 </strong>(September 26<sup>th</sup>, 2015):</p>
<ul>
<li><strong>Windows:</strong>
<ul>
<li>Fix two TrueCrypt vulnerabilities reported by James Forshaw (Google Project<br>
Zero)
<ul>
<li><a href="https://code.google.com/p/google-security-research/issues/detail?id=538" target="_blank">CVE-2015-7358</a>&nbsp;(critical): Local Elevation of Privilege on Windows by<br>
abusing drive letter handling. </li><li><a href="https://code.google.com/p/google-security-research/issues/detail?id=537" target="_blank">CVE-2015-7359</a>: Local Elevation of Privilege on Windows caused by<br>
incorrect Impersonation Token Handling. </li></ul>
</li><li>Fix regression in mounting of favorite volumes at user logon. </li><li>Fix display of some Unicode languages (e.g. Chinese) in formatting wizard. </li><li>Set keyboard focus to PIM field when &quot;Use PIM&quot; is checked. </li><li>Allow Application key to open context menu on drive letters list </li><li>Support specifying volumes size in TB in the GUI (command line already supports this)
</li></ul>
</li></ul>
<p><strong style="text-align:left">1.14 </strong>(September 16<sup>th</sup>, 2015):</p>
<ul>
<li><strong>All OSs:</strong>
<ul>
<li>Mask and unmask PIM value in GUI and bootloader like the password. </li></ul>
</li></ul>
<ul>
<li><strong>Windows:</strong>
<ul>
<li>Solve Rescue Disk damaged error when using cascade ciphers and SHA256 for system encryption.
</li><li>Solve option &quot;Cache password in drive memory&quot; always disabled even if checked in preferences.
</li><li>Solve UI language change not taken into account for new install unless a preference is changed.
</li><li>Implement creating file containers using command line. </li><li>Driver: disable support of IOCTL_STORAGE_QUERY_PROPERTY by default and add option to enable it.
</li><li>Driver:&nbsp; Support returning StorageDeviceProperty when queried through IOCTL_STORAGE_QUERY_PROPERTY.
</li><li>Support setting volume label in Explorer through mount option or favorite label value.
</li><li>Fix for Hot Keys assignment dialog issue where OEM-233 is always displayed and can't be changed.
</li><li>Always copy both 32-bit and 64-bit executable binaries during install and in Traveler Disk Setup.
<ul>
<li>Traveler Disk will again use 32-bit exe by default while also offering 64-bit exe.
</li><li>On Windows 64-bit, 32-bit exe files are now available(e.g. if needed to use 32-bit PKCS#11 dll)
</li></ul>
</li><li>Include Volume Expander in Traveler Disk Setup. </li><li>Don't offer creating a restore point if it is disabled in Windows. </li><li>Add possibility to verify a Rescue Disk ISO image file. </li><li>Minors fixes in the installer, GUI and driver. </li></ul>
</li></ul>
<ul>
<li><strong>Linux:</strong>
<ul>
<li>Support supplying password using stdin in non interactive mode (contributed by
<a href="https://github.com/LouisTakePILLz" target="_blank">LouisTakePILLz</a>)
<ul>
<li>Example: <code>veracrypt -t ${IMAGE_PATH} ${MOUNT_PATH} --mount --non-interactive --stdin &lt;&lt;&lt; &quot;$PWD&quot;</code>
</li></ul>
</li></ul>
</li></ul>
<p><strong style="text-align:left">1.13 </strong>(August 9<sup>th</sup>, 2015):</p>
<ul>
<li><strong>Windows:</strong>
<ul>
<li>Solve TOR crashing when run from a VeraCrypt volume. </li></ul>
</li></ul>
<p><strong style="text-align:left">1.12 </strong>(August 5<sup>th</sup>, 2015):</p>
<ul>
<li><strong>All OSs:</strong>
<ul>
<li>Implement &quot;Dynamic Mode&quot; by supporting a Personal Iterations Multiplier (PIM). See documentation for more information.
</li></ul>
</li></ul>
<ul>
<li><strong>Windows:</strong>
<ul>
<li>Detect Boot Loader tampering (&quot;Evil Maid&quot; attacks) for system encryption and propose recovery options.
</li><li>Fix buffer overrun issue and other memory related bugs when parsing language XML files.
</li><li>Fix wrongly reported bad sectors by chkdsk caused by a bug in&nbsp;IOCTL_DISK_VERIFY handling.
</li><li>Fix privacy issue caused by configuration and history files being updated whenever VeraCrypt is used (reported by Liran Elharar)
</li><li>Fix system favorites not always mounting after cold start. </li><li>Solve installer error when updating VeraCrypt on Windows 10. </li><li>Implement decryption of non-system partition/drive. </li><li>Include 64-bit exe files in the installer and deploy them on 64-bit machines for better performances.
</li><li>Allow using drive letters A: and B: for mounting volumes </li><li>Make command line argument parsing more strict and robust (e.g. /lz rejected, must be /l z)
</li><li>Add possibility to show system encryption password in Windows GUI and bootloader
</li><li>Solve &quot;Class Already exists&quot; error that was happening for some users. </li><li>Solve some menu items and GUI fields not translatable </li><li>Make volumes correctly report Physical Sector size to Windows. </li><li>Correctly detect switch user/RDP disconnect operations for autounmount on session locked.
</li><li>Add manual selection of partition when resuming in-place encryption. </li><li>Add command line option (/cache f) to temporarily cache password during favorites mounting.
</li><li>Add waiting dialog for Auto-Mount Devices operations to avoid freezing GUI. </li><li>Add extra information to displayed error message in order to help analyze reported issues.
</li><li>Disable menu entry for changing system encryption PRF since it's not yet implemented.
</li><li>Fix failure to change password when UAC required (inherited from TrueCrypt) </li><li>Minor fixes and changes (see Git history for more details) </li></ul>
</li></ul>
<ul>
<li><strong>Linux:</strong>
<ul>
<li>Solve installer issue under KDE when xterm not available </li><li>Fix warnings on about/LegalNotice dialogs when wxWidgets linked dynamically (N/A for official binary)
</li><li>Support hash names with '-' in command line (sha-256, sha-512 and ripemd-160)
</li><li>Remove &quot;--current-hash&quot; switch and add &quot;--new-hash&quot; to be more coherent with existing switches.
</li><li>When only keyfile specified in command line, don't try to mount using empty password.
<ul>
<li>If mounting using empty password is needed, explicitly specify so using: -p &quot;&quot;
</li></ul>
</li></ul>
</li></ul>
<p id="1.0f-2"><strong style="text-align:left">1.0f-2</strong>(April 5<sup>th</sup>, 2015):</p>
<ul>
<li><strong>All OSs:</strong>
<ul>
<li>Mounting speed improvement, up to 20% quicker on 64-bit (contributed by Nils Maier)
</li><li>Add option to set default hash/TrueCryptMode used for mounting volumes. </li><li>Use TrueCryptMode/Hash specified in command line in password dialog. </li></ul>
</li><li><strong>Windows:</strong>
<ul>
<li>Solve CryptAcquireContext vulnerability reported by Open Crypto Audit Phase II.
</li><li>Proper handling of random generator failures. Inform user in such cases. </li><li>TrueCrypt Mode related changes:
<ul>
<li>Support mounting TrueCrypt system partition (no conversion yet) </li><li>Support TrueCrypt volumes as System Favorites. </li><li>Correct displaying wrong TrueCrypt mode in volume properties when SHA-256 is used.
</li></ul>
</li><li>Solve PIN BLOCKED issue with smart cards in a special case. </li><li>Correctly handle file access errors when mounting containers. </li><li>Solve several issues reported by the Static Code Analysis too Coverity. </li><li>Bootloader: Add &quot;Verifying Password...&quot; message. </li><li>When UAC prompt fails (for example timeout), offer the user to retry the operation.
</li><li>Uninstall link now open the standard &quot;Add/Remove Programs&quot; window. </li><li>On uninstall, remove all VeraCrypt references from registry and disk. </li><li>Included VeraCryptExpander in the Setup. </li><li>Add option to temporary cache password when mounting multiple favorites. </li><li>Minor fixes and enhancements (see git history for more information) </li></ul>
</li><li><strong>MacOSX:</strong>
<ul>
<li>Solve issue volumes not auto-unmounting when quitting VeraCrypt<strong>.</strong>
</li><li>Solve issue VeraCrypt window not reopening by clicking dock icon. </li></ul>
</li><li><strong>Linux/MacOSX:</strong>
<ul>
<li>Solve preferences dialog not closing when clicking on the 'X' icon. </li><li>Solve read-only issue when mounting non-FAT volumes in some cases. </li><li>Support opening/exploring mounted volumes on desktops other than Gnome/KDE. </li><li>Solve various installer issues when running on less common configurations </li><li>Minor fixes (see git history for more information) </li></ul>
</li></ul>
<p><strong style="text-align:left">1.0f-1 </strong>(January 4<sup>th</sup>, 2015)</p>
<ul>
<li><strong>All OSs</strong>:
<ul>
<li>Add support for old TrueCrypt 6.0. </li><li>Change naming of cascades algorithms in GUI for a better description. </li></ul>
</li><li><strong>Linux/MacOSX:</strong>
<ul>
<li>Make cancel button of the preference dialog working. </li><li>Solve impossibility to enter a one digit size for the volume. </li><li>Add wait dialog to the benchmark calculation. </li></ul>
</li><li><strong>Windows:</strong>
<ul>
<li>Add TrueCrypt mode to the mounted volume information. </li><li>For Windows XP, correct the installer graphical artefacts. </li></ul>
</li></ul>
<p><strong style="text-align:left">1.0f </strong>(December 30, 2014)</p>
<ul>
<li><strong>All OSs</strong>:
<ul>
<li>Add support for mounting TrueCrypt volumes. </li><li>Add support for converting TrueCrypt containers and non-system partitions. </li><li>Add support for SHA-256 for volume encryption. </li><li>Make SHA-512 the default key derivation algorithm and change the order of preference of derivation algorithms : SHA-512 -&gt; Whirlpool -&gt; SHA-256 -&gt; RIPEMD160
</li><li>Deprecate RIPEMD160 for non-system encryption. </li><li>Speedup mount operation by enabling choice of correct hash algorithm. </li><li>Display a wait dialog during lengthy operations to avoid freezing the GUI. </li><li>Implement creation of multiple keyfiles at once, with predefined or random size.
</li><li>Always display random gathering dialog before performing sensitive operations.
</li><li>Links in the application now points to the online resources on Codeplex </li><li>First version of proper VeraCrypt User Guide </li></ul>
</li><li><strong>MacOSX:</strong>
<ul>
<li>Implement support for hard drives with a large sector size (&gt; 512). </li><li>Link against new wxWidgets version 3.0.2. </li><li>Solve truncated text in some Wizard windows. </li></ul>
</li><li><strong>Linux:</strong>
<ul>
<li>Add support of NTFS formatting of volumes. </li><li>Correct issue on opening of the user guide PDF. </li><li>Better support for hard drives with a large sector size (&gt; 512). </li><li>Link against new wxWidgets version 3.0.2. </li></ul>
</li><li><strong>Windows:</strong><br>
<ul>
<li>Security: fix vulnerability in bootloader detected by Open Crypto Audit and make it more robust.
</li><li>Add support for SHA-256 in system boot encryption. </li><li>Various optimizations in bootloader. </li><li>Complete fix of ShellExecute security issue. </li><li>Kernel driver: check that the password length received from bootloader is less or equal to 64.
</li><li>Correct a random crash when clicking the link for more information on keyfiles
</li><li>Implement option to auto-unmount when user session is locked </li><li>Add self-test vectors for SHA-256 </li><li>Modern look-and-feel by enabling visual styles </li><li>few minor fixed. </li></ul>
</li></ul>
<div style="text-align:left; margin-top:19px; margin-bottom:19px; padding-top:0px; padding-bottom:0px">
<strong style="text-align:left">1.0e </strong>(September 4, 2014)</div>
<div style="text-align:left; margin-top:19px; margin-bottom:19px; padding-top:0px; padding-bottom:0px">
<ul>
<li><strong style="text-align:left">Improvements and bug fixes:</strong>
<ul>
<li>Correct most of the security vulnerabilities reported by the Open Crypto Audit Project.
</li><li>Correct security issues detected by Static Code Analysis, mainly under Windows.
</li><li>Correct issue of unresponsiveness when changing password/key file of a volume. Reduce overall time taken for creating encrypted volume/partition.
</li><li>Minor improvements and bug fixes (look at git history for more details). </li></ul>
</li></ul>
</div>
<div style="text-align:left; margin-top:19px; margin-bottom:19px; padding-top:0px; padding-bottom:0px">
<br style="text-align:left">
<strong style="text-align:left">1.0d </strong>(June 3, 2014)</div>
<div style="text-align:left; margin-top:19px; margin-bottom:19px; padding-top:0px; padding-bottom:0px">
<ul>
<li><strong style="text-align:left">Improvements and bug fixes:</strong>
<ul>
<li>Correct issue while creating hidden operating system. </li><li>Minor improvements and bug fixes. </li></ul>
</li></ul>
</div>
</div><div class="ClearBoth"></div></body></html>
