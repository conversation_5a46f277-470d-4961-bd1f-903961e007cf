﻿<!DOCTYPE html>
<html lang="ru">
<head>
<meta http-equiv="content-type" content="text/html; charset=utf-8" />
<title>VeraCrypt - Бесплатное надёжное шифрование дисков с открытым исходным кодом</title>
<meta name="description" content="VeraCrypt это бесплатное программное обеспечение для шифрования дисков с открытым исходным кодом для Windows, Mac OS X (macOS) и Linux. В случае, если злоумышленник вынуждает вас раскрыть пароль, VeraCrypt обеспечивает правдоподобное отрицание наличия шифрования. В отличие от пофайлового шифрования, VeraCrypt шифрует данные в реальном времени (на лету), автоматически, прозрачно, требует очень мало памяти и не использует временные незашифрованные файлы."/>
<meta name="keywords" content="encryption, security, шифрование, безопасность"/>
<link href="styles.css" rel="stylesheet" type="text/css" />
</head>
<body>

<div>
<a href="Documentation.html"><img src="VeraCrypt128x128.png" alt="VeraCrypt"/></a>
</div>

<div id="menu">
	<ul>
	  <li><a href="Home.html">Начало</a></li>
	  <li><a href="Code.html">Исходный код</a></li>
	  <li><a href="Downloads.html">Загрузить</a></li>
	  <li><a class="active" href="Documentation.html">Документация</a></li>
	  <li><a href="Donation.html">Поддержать разработку</a></li>
	  <li><a href="https://sourceforge.net/p/veracrypt/discussion/" target="_blank">Форум</a></li>
	</ul>
</div>

<div>
<p>
<a href="Documentation.html">Документация</a>
<img src="arrow_right.gif" alt=">>" style="margin-top: 5px">
<a href="Security%20Model.html">Модель безопасности</a>
</p></div>

<div class="wikidoc">
<div>
<h1>Модель безопасности</h1>
<div>
<h4><i>Примечание для исследователей безопасности.</i> Если вы намереваетесь сообщить о проблеме с безопасностью или
опубликовать атаку на VeraCrypt, пожалуйста, удостоверьтесь, что вы не пренебрегаете описанной ниже моделью безопасности
VeraCrypt. В противном случае атака (или проблема с безопасностью) будет расцениваться как недействительная/поддельная.</h4>
</div>
<p>VeraCrypt это компьютерная программа, в чьи основные цели входят:</p>
<ul>
<li>защита данных с помощью шифрования перед записью на диск;</li>
<li>расшифровка зашифрованных данных после их считывания с диска.</li></ul>
<p>В задачи VeraCrypt <strong>не</strong> входит:</p>
<ul>
<li>шифрование или защита любой области ОЗУ (оперативной памяти ПК);</li>
<li>защита данных в компьютере,* если атакующий имеет привилегии&dagger; администратора в среде операционной системы,
установленной в этом компьютере;</li>
<li>защита данных в компьютере, содержащем какое-либо вредоносное ПО (например, вирус, трояна, шпионскую программу)
или любую часть ПО (включая TrueCrypt или компонент операционной системы), которая была изменена, создана или может
быть подконтрольна атакующему;</li>
<li>защита данных в компьютере, если у атакующего был к нему физический доступ до или во время работы VeraCrypt;</li>
<li>защита данных в компьютере, если у атакующего есть физический доступ к нему между временем завершения работы
VeraCrypt и временем, необходимым для окончательного и безвозвратного стирания/утери всей информации из модулей
временной памяти, подключённых к компьютеру (включая модули памяти в периферийных устройствах);</li>
<li>защита данных в компьютере, если атакующий может удалённо перехватить излучения от аппаратуры компьютера
(например, от монитора или кабелей) во время работы VeraCrypt (или иным образом выполнять удалённый мониторинг
аппаратной части ПК и её использования, непосредственно или косвенно, во время работы VeraCrypt в этом ПК);</li>
<li>защита данных, хранящихся в томе&Dagger; VeraCrypt, если атакующий без привилегий администратора имеет доступ
к содержимому смонтированного тома (например, если права на файл/папку/том не препятствуют такому доступу атакующего);</li>
<li>сохранение/контроль целостности или аутентичности зашифрованных и расшифрованных данных;</li>
<li>предотвращение анализа трафика при передаче зашифрованных данных по сети;</li>
<li>предотвращение определения неприятелем, какие сектора с содержимым тома были изменены (а также когда и сколько
раз), если неприятель имеет возможность следить за томом (смонтированным или не смонтированным) до и после записи
в него, либо если носитель/устройство хранения информации позволяет неприятелю определять такую информацию (например,
том находится на устройстве, сохраняющем метаданные, которые можно использовать для выяснения, когда данные были
записаны в конкретный сектор);</li>
<li>шифрование "на месте" любых имеющихся незашифрованных данных (или перешифрование/удаление данных) в
устройствах/файловых системах с технологией распределения износа ячеек памяти (wear-leveling) или иным
перераспределением данных внутренними средствами;</li>
<li>гарантия, что пользователь выбирает криптографически надёжные пароли и ключевые файлы;</li>
<li>защита любого аппаратного компонента компьютера или всего компьютера;</li>
<li>защита данных в компьютере, в котором не соблюдены условия, перечисленные в главе
<a href="Security%20Requirements%20and%20Precautions.html">
<em>Требования безопасности и меры предосторожности</em></a>.</li>
<li>выполнение чего-либо, указанного в разделе <a href="Issues%20and%20Limitations.html#limitations">
Ограничения</a> (глава <a href="Issues%20and%20Limitations.html">
Замеченные проблемы и ограничения</a>).</li></ul>
<p>В среде <strong>Windows</strong> пользователь без привилегий администратора может (при условии, что
используются стандартные конфигурации VeraCrypt и операционной системы):</p>
<ul>
<li>монтировать любой том VeraCrypt на основе файла при условии, что это позволено правами на файловый контейнер;</li>
<li>монтировать любой том VeraCrypt на основе раздела/устройства;</li>
<li>завершать процесс предзагрузочной аутентификации и, таким образом, получать доступ к данным на зашифрованном
системном разделе/диске (и запускать зашифрованную операционную систему);</li>
<li>пропускать процесс предзагрузочной аутентификации (это можно предотвратить, отключив параметр
<em>Настройки</em> &gt; <em>Шифрование системы</em> &gt; <em>Обход предзагрузочной аутентификации по Esc</em>;
обратите внимание, что включать/отключать данный параметр может только администратор);</li>
<li>размонтировать, используя VeraCrypt, любой смонтированный этим пользователем том VeraCrypt (и, в окне программы
VeraCrypt, видеть его путь и свойства). Это, однако, неприменимо к "системным избранным томам", которые пользователь
может размонтировать (и т. д.) вне зависимости от того, кто их смонтировал (это можно предотвратить, включив параметр
<em>Настройки</em> &gt; <em>Системные избранные тома</em> &gt; <em>Просматривать/размонтировать системные избранные
тома могут лишь администраторы</em>; обратите внимание, что включать/отключать данный параметр может только администратор).</li>
<li>создавать том VeraCrypt на основе файла с файловой системой FAT или без файловой системы (при условии, что это
разрешено правами соответствующей папки);</li>
<li>изменять пароль, ключевые файлы и алгоритм формирования ключа заголовка, восстанавливать заголовок или создавать
его резервную копию для тома VeraCrypt на основе файла (при условии, что это разрешено правами данного файла);</li>
<li>обращаться к файловой системе внутри тома VeraCrypt, смонтированного другим пользователем системы (что, однако,
можно запретить, назначив соответствующие права на файл/папку/том);</li>
<li>использовать пароли (и обработанные ключевые файлы), сохранённые в кэше паролей (обратите внимание, что кэширование
можно отключить; см. подробности в разделе
<em>Настройки &gt; Параметры</em>, подраздел <em>Кэшировать пароли в памяти драйвера</em>);</li>
<li>просматривать основные свойства (например, размер зашифрованной области, используемые алгоритмы шифрования и
хеширования, и т. д.) зашифрованного системного раздела/диска, когда работает зашифрованная система;</li>
<li>запускать и использовать программу VeraCrypt (включая мастер создания томов VeraCrypt) при условии, что запущен
драйвер устройств VeraCrypt и это позволяют права на файлы.
</li></ul>
<p>В среде <strong>Linux</strong> пользователь без привилегий администратора может (при условии, что используются
стандартные конфигурации VeraCrypt и операционной системы):</p>
<ul>
<li>создавать том VeraCrypt на основе файла или раздела/устройства с файловой системой FAT или без файловой системы
(при условии, что это разрешено правами соответствующей папки/устройства);</li>
<li>изменять пароль, ключевые файлы и алгоритм деривации ключа заголовка, восстанавливать заголовок или делать
его резервную копию для тома VeraCrypt на основе файла или раздела/устройства (при условии, что это разрешено
правами данного файла/устройства);</li>
<li>обращаться к файловой системе внутри тома VeraCrypt, смонтированного другим пользователем системы (что, однако,
можно запретить, назначив соответствующие права на файл/папку/том);</li>
<li>запускать и использовать программу VeraCrypt (включая мастер создания томов VeraCrypt) при условии, что это
позволяют права на файлы;</li>
<li>в окне программы VeraCrypt видеть путь и свойства любого тома VeraCrypt, смонтированного этим пользователем.</li></ul>
<p>В среде <strong>Mac OS X</strong> (<strong>macOS</strong>) пользователь без привилегий администратора может
(при условии, что используются стандартные конфигурации VeraCrypt и операционной системы):</p>
<ul>
<li>монтировать любой том VeraCrypt на основе файла или раздела/устройства (при условии, что это разрешено
правами соответствующего файла/устройства);</li>
<li>размонтировать, используя VeraCrypt, любой смонтированный этим пользователем том VeraCrypt (и, в окне программы
VeraCrypt, видеть его путь и свойства);</li>
<li>создавать том VeraCrypt на основе файла или раздела/устройства (при условии, что это разрешено правами
соответствующей папки/устройства);</li>
<li>изменять пароль, ключевые файлы и алгоритм формирования ключа заголовка, восстанавливать заголовок или
делать его резервную копию для тома VeraCrypt на основе файла или раздела/устройства (при условии, что это
разрешено правами данного файла/устройства);</li>
<li>обращаться к файловой системе внутри тома VeraCrypt, смонтированного другим пользователем системы
(что, однако, можно запретить, назначив соответствующие права на файл/папку/том);</li>
<li>запускать и использовать программу VeraCrypt (включая мастер создания томов VeraCrypt) при условии,
что это позволяют права на файлы.
</li></ul>
<p>VeraCrypt не поддерживает корневой режим выполнения set-euid.<br>
<br>
Дополнительная информация и подробности о модели безопасности содержатся в главе
<a href="Security%20Requirements%20and%20Precautions.html">
<em>Требования безопасности и меры предосторожности</em></a>.</p>
<p>* В этом разделе (<em>Модель безопасности</em>) фраза "данные в компьютере" означает данные на
внутренних и внешних запоминающих устройствах/носителях (включая съёмные устройства и сетевые диски),
подключённых к компьютеру.</p>
<p>&dagger; В этом разделе (<em>Модель безопасности</em>), фраза "привилегии администратора" не обязательно
относится к действительной учётной записи администратора. Она также может относиться к злоумышленнику,
не имеющему действующей учётной записи администратора, но способному (например, из-за неправильной настройки
системы или использования уязвимости в операционной системе или стороннем приложении) выполнить любое действие,
что обычно разрешено только пользователю с действительной учётной записью администратора (например, читать
или изменять произвольную часть диска или ОЗУ и т. д.).</p>
<p>&Dagger; "Том VeraCrypt" также означает системный раздел/диск, зашифрованный с помощью VeraCrypt (см. главу
<a href="System%20Encryption.html"><em>Шифрование системы</em></a>).</p>
</div>
</div>
</body></html>
