<!DOCTYPE html>
<html lang="en">
<head>
<meta http-equiv="content-type" content="text/html; charset=utf-8" />
<title>VeraCrypt - Free Open source disk encryption with strong security for the Paranoid</title>
<meta name="description" content="VeraCrypt is free open-source disk encryption software for Windows, Mac OS X and Linux. In case an attacker forces you to reveal the password, VeraCrypt provides plausible deniability. In contrast to file encryption, data encryption performed by VeraCrypt is real-time (on-the-fly), automatic, transparent, needs very little memory, and does not involve temporary unencrypted files."/>
<meta name="keywords" content="encryption, security"/>
<link href="styles.css" rel="stylesheet" type="text/css" />
</head>
<body>

<div>
<a href="Documentation.html"><img src="VeraCrypt128x128.png" alt="VeraCrypt"/></a>
</div>

<div id="menu">
	<ul>
	  <li><a href="Home.html">Home</a></li>
	  <li><a href="Code.html">Source Code</a></li>
	  <li><a href="Downloads.html">Downloads</a></li>
	  <li><a class="active" href="Documentation.html">Documentation</a></li>
	  <li><a href="Donation.html">Donate</a></li>
	  <li><a href="https://sourceforge.net/p/veracrypt/discussion/" target="_blank">Forums</a></li>
	</ul>
</div>

<div>
<p>
<a href="Documentation.html">Documentation</a>
<img src="arrow_right.gif" alt=">>" style="margin-top: 5px">
<a href="Security%20Requirements%20and%20Precautions.html">Security Requirements and Precautions</a>
</p></div>

<div class="wikidoc">
<h1>Security Requirements and Precautions</h1>
<table style="border-collapse:separate; border-spacing:0px; text-align:left; font-size:11px; line-height:13px; font-family:Verdana,Arial,Helvetica,sans-serif">
<tbody style="text-align:left">
<tr style="text-align:left">
<td style="text-align:left; font-size:11px; line-height:13px; font-family:Verdana,Arial,Helvetica,sans-serif; color:#ff0000; padding:15px; border:1px solid #000000">
<strong style="text-align:left">IMPORTANT</strong>: If you want to use VeraCrypt, you must follow the security requirements and security precautions listed in this chapter.</td>
</tr>
</tbody>
</table>
<div style="text-align:left; margin-top:19px; margin-bottom:19px; padding-top:0px; padding-bottom:0px">
The sections in this chapter specify security requirements for using VeraCrypt and give information about things that adversely affect or limit the ability of VeraCrypt to secure data and to provide plausible deniability. Disclaimer: This chapter is not guaranteed
 to contain a list of <em style="text-align:left">all</em> security issues and attacks that might adversely affect or limit the ability of VeraCrypt to secure data and to provide plausible deniability.</div>
<ul style="text-align:left; margin-top:18px; margin-bottom:19px; padding-top:0px; padding-bottom:0px">
<li style="text-align:left; margin-top:0px; margin-bottom:0px; padding-top:0px; padding-bottom:0px">
<a href="Data%20Leaks.html" style="text-align:left; color:#0080c0; text-decoration:none">Data Leaks</a>
<ul>
<li style="text-align:left; margin-top:0px; margin-bottom:0px; padding-top:0px; padding-bottom:0px">
<a href="Paging%20File.html" style="text-align:left; color:#0080c0; text-decoration:none">Paging File</a>
</li><li style="text-align:left; margin-top:0px; margin-bottom:0px; padding-top:0px; padding-bottom:0px">
<a href="Hibernation%20File.html" style="text-align:left; color:#0080c0; text-decoration:none">Hibernation File</a>
</li><li style="text-align:left; margin-top:0px; margin-bottom:0px; padding-top:0px; padding-bottom:0px">
<a href="Memory%20Dump%20Files.html" style="text-align:left; color:#0080c0; text-decoration:none">Memory Dump Files</a>
</li></ul>
</li><li style="text-align:left; margin-top:0px; margin-bottom:0px; padding-top:0px; padding-bottom:0px">
<a href="Unencrypted%20Data%20in%20RAM.html" style="text-align:left; color:#0080c0; text-decoration:none">Unencrypted Data in RAM</a>
</li><li style="text-align:left; margin-top:0px; margin-bottom:0px; padding-top:0px; padding-bottom:0px">
<a href="VeraCrypt%20Memory%20Protection.html" style="text-align:left; color:#0080c0; text-decoration:none">VeraCrypt Memory Protection</a>
</li><li style="text-align:left; margin-top:0px; margin-bottom:0px; padding-top:0px; padding-bottom:0px">
<a href="Physical%20Security.html" style="text-align:left; color:#0080c0; text-decoration:none">Physical Security</a>
</li><li style="text-align:left; margin-top:0px; margin-bottom:0px; padding-top:0px; padding-bottom:0px">
<a href="Malware.html" style="text-align:left; color:#0080c0; text-decoration:none">Malware</a>
</li><li style="text-align:left; margin-top:0px; margin-bottom:0px; padding-top:0px; padding-bottom:0px">
<a href="Multi-User%20Environment.html" style="text-align:left; color:#0080c0; text-decoration:none">Multi-User Environment</a>
</li><li style="text-align:left; margin-top:0px; margin-bottom:0px; padding-top:0px; padding-bottom:0px">
<a href="Authenticity%20and%20Integrity.html" style="text-align:left; color:#0080c0; text-decoration:none">Authenticity and Integrity</a>
</li><li style="text-align:left; margin-top:0px; margin-bottom:0px; padding-top:0px; padding-bottom:0px">
<a href="Choosing%20Passwords%20and%20Keyfiles.html" style="text-align:left; color:#0080c0; text-decoration:none">Choosing Passwords and Keyfiles</a>
</li><li style="text-align:left; margin-top:0px; margin-bottom:0px; padding-top:0px; padding-bottom:0px">
<a href="Changing%20Passwords%20and%20Keyfiles.html" style="text-align:left; color:#0080c0; text-decoration:none">Changing Passwords and Keyfiles</a>
</li><li style="text-align:left; margin-top:0px; margin-bottom:0px; padding-top:0px; padding-bottom:0px">
<a href="Trim%20Operation.html" style="text-align:left; color:#0080c0; text-decoration:none">Trim Operation</a>
</li><li style="text-align:left; margin-top:0px; margin-bottom:0px; padding-top:0px; padding-bottom:0px">
<a href="Wear-Leveling.html" style="text-align:left; color:#0080c0; text-decoration:none">Wear-Leveling</a>
</li><li style="text-align:left; margin-top:0px; margin-bottom:0px; padding-top:0px; padding-bottom:0px">
<a href="Reallocated%20Sectors.html" style="text-align:left; color:#0080c0; text-decoration:none">Reallocated Sectors</a>
</li><li style="text-align:left; margin-top:0px; margin-bottom:0px; padding-top:0px; padding-bottom:0px">
<a href="Defragmenting.html" style="text-align:left; color:#0080c0; text-decoration:none">Defragmenting</a>
</li><li style="text-align:left; margin-top:0px; margin-bottom:0px; padding-top:0px; padding-bottom:0px">
<a href="Journaling%20File%20Systems.html" style="text-align:left; color:#0080c0; text-decoration:none">Journaling File Systems</a>
</li><li style="text-align:left; margin-top:0px; margin-bottom:0px; padding-top:0px; padding-bottom:0px">
<a href="Volume%20Clones.html" style="text-align:left; color:#0080c0; text-decoration:none">Volume Clones</a>
</li><li style="text-align:left; margin-top:0px; margin-bottom:0px; padding-top:0px; padding-bottom:0px">
<a href="Additional%20Security%20Requirements%20and%20Precautions.html" style="text-align:left; color:#0080c0; text-decoration:none">Additional Security Requirements and Precautions</a>
</li></ul>
</div><div class="ClearBoth"></div></body></html>
