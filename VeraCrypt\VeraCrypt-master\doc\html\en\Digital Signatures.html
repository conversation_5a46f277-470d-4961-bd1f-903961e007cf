<!DOCTYPE html>
<html lang="en">
<head>
<meta http-equiv="content-type" content="text/html; charset=utf-8" />
<title>VeraCrypt - Free Open source disk encryption with strong security for the Paranoid</title>
<meta name="description" content="VeraCrypt is free open-source disk encryption software for Windows, Mac OS X and Linux. In case an attacker forces you to reveal the password, VeraCrypt provides plausible deniability. In contrast to file encryption, data encryption performed by VeraCrypt is real-time (on-the-fly), automatic, transparent, needs very little memory, and does not involve temporary unencrypted files."/>
<meta name="keywords" content="encryption, security"/>
<link href="styles.css" rel="stylesheet" type="text/css" />
</head>
<body>

<div>
<a href="Documentation.html"><img src="VeraCrypt128x128.png" alt="VeraCrypt"/></a>
</div>

<div id="menu">
	<ul>
	  <li><a href="Home.html">Home</a></li>
	  <li><a href="Code.html">Source Code</a></li>
	  <li><a href="Downloads.html">Downloads</a></li>
	  <li><a class="active" href="Documentation.html">Documentation</a></li>
	  <li><a href="Donation.html">Donate</a></li>
	  <li><a href="https://sourceforge.net/p/veracrypt/discussion/" target="_blank">Forums</a></li>
	</ul>
</div>

<div>
<p>
<a href="Documentation.html">Documentation</a>
<img src="arrow_right.gif" alt=">>" style="margin-top: 5px">
<a href="Miscellaneous.html">Miscellaneous</a>
<img src="arrow_right.gif" alt=">>" style="margin-top: 5px">
<a href="Digital%20Signatures.html">Digital Signatures</a>
</p></div>

<div class="wikidoc">
<h1>Digital Signatures</h1>
<h3>Why Verify Digital Signatures</h3>
<p>It might happen that a VeraCrypt installation package you download from our server was created or modified by an attacker. For example, the attacker could exploit a vulnerability in the server software we use and alter the installation packages stored on
 the server, or he/she could alter any of the files en route to you.<br>
<br>
Therefore, you should always verify the integrity and authenticity of each VeraCrypt distribution package you download or otherwise obtain from any source. In other words, you should always make sure that the file was created by us and it was not altered by
 an attacker. One way to do so is to verify so-called digital signature(s) of the file.</p>
<h3>Types of Digital Signatures We Use</h3>
<p>We currently use two types of digital signatures:</p>
<ul>
<li><strong>PGP</strong> signatures (available for all binary and source code packages for all supported systems).
</li><li><strong>X.509</strong> signatures (available for binary packages for Windows).
</li></ul>
<h3>Advantages of X.509 Signatures</h3>
<p>X.509 signatures have the following advantages, in comparison to PGP signatures:</p>
<ul>
<li>It is much easier to verify that the key that signed the file is really ours (not attacker&rsquo;s).
</li><li>You do not have to download or install any extra software to verify an X.509 signature (see below).
</li><li>You do not have to download and import our public key (it is embedded in the signed file).
</li><li>You do not have to download any separate signature file (the signature is embedded in the signed file).
</li></ul>
<h3>Advantages of PGP Signatures</h3>
<p>PGP signatures have the following advantages, in comparison to X.509 signatures:</p>
<ul>
<li>They do not depend on any certificate authority (which might be e.g. infiltrated or controlled by an adversary, or be untrustworthy for other reasons).
</li></ul>
<h3>How to Verify X.509 Signatures</h3>
<p>Please note that X.509 signatures are currently available only for the VeraCrypt self-extracting installation packages for Windows. An X.509 digital signature is embedded in each of those files along with the digital certificate of the VeraCrypt Foundation
 issued by a public certification authority. To verify the integrity and authenticity of a self-extracting installation package for Windows, follow these steps:</p>
<ol>
<li>Download the VeraCrypt self-extracting installation package. </li><li>In the Windows Explorer, click the downloaded file (&lsquo;<em>VeraCrypt Setup.exe</em>&rsquo;) with the right mouse button and select &lsquo;<em>Properties</em>&rsquo; from the context menu.
</li><li>In the <em>Properties</em> dialog window, select the &lsquo;<em>Digital Signatures</em>&rsquo; tab.
</li><li>On the &lsquo;<em>Digital Signatures</em>&rsquo; tab, in the &lsquo;<em>Signature list</em>&rsquo;, double click the line saying &quot;<em>IDRIX</em>&quot; or
<em>&quot;IDRIX SARL&quot;</em>. </li><li>The &lsquo;<em>Digital Signature Details</em>&rsquo; dialog window should appear now. If you see the following sentence at the top of the dialog window, then the integrity and authenticity of the package have been successfully verified:<br>
<br>
&quot;<em>This digital signature is OK.</em>&quot;<br>
<br>
If you do not see the above sentence, the file is very likely corrupted. Note: On some obsolete versions of Windows, some of the necessary certificates are missing, which causes the signature verification to fail.
</li></ol>
<h3 id="VerifyPGPSignature">How to Verify PGP Signatures</h3>
<p>To verify a PGP signature, follow these steps:</p>
<ol>
<li>Install any public-key encryption software that supports PGP signatures. For Windows, you can download <a href="http://www.gpg4win.org/" target="_blank">Gpg4win</a>. For more information, you can visit <a href="https://www.gnupg.org/">https://www.gnupg.org/</a>. </li>
<li>Create a private key (for information on how to do so, please see the documentation for the public-key encryption software).</li>
<li>Download our PGP public key from <strong>AM Crypto</strong> website (<a href="https://amcrypto.jp/VeraCrypt/VeraCrypt_PGP_public_key.asc" target="_blank">https://amcrypto.jp/VeraCrypt/VeraCrypt_PGP_public_key.asc</a>) or from a trusted public key repository
 (ID=0x680D16DE), and import the downloaded key to your keyring (for information on how to do so, please see the documentation for the public-key encryption software). Please check that its fingerprint is
<strong>5069A233D55A0EEB174A5FC3821ACD02680D16DE</strong>.
<ul>
<li>For VeraCrypt version 1.22 and below, the verification must use the PGP public key available at <a href="https://amcrypto.jp/VeraCrypt/VeraCrypt_PGP_public_key_2014.asc" target="_blank">https://amcrypto.jp/VeraCrypt/VeraCrypt_PGP_public_key_2014.asc</a> or from a trusted public key repository
 (ID=0x54DDD393), whose fingerprint is <strong>993B7D7E8E413809828F0F29EB559C7C54DDD393</strong>.
</li>
</ul>
</li>
<li>Sign the imported key with your private key to mark it as trusted (for information on how to do so, please see the documentation for the public-key encryption software).<br>
<br>
Note: If you skip this step and attempt to verify any of our PGP signatures, you will receive an error message stating that the signing key is invalid.
</li>
<li>Download the digital signature by downloading the <em>PGP Signature</em> of the file you want to verify (on the <a href="https://veracrypt.jp/en/Downloads.html">Downloads page</a>).
</li>
<li>Verify the downloaded signature (for information on how to do so, please see the documentation for the public-key encryption software).</li>
</ol>
<p>Under Linux, these steps can be achieved using the following commands:</p>
<ul>
<li>Check that the fingerprint of the public key is <strong>5069A233D55A0EEB174A5FC3821ACD02680D16DE</strong>:<strong>gpg --import --import-options show-only VeraCrypt_PGP_public_key.asc</strong> (for older gpg versions, type instead:
<strong>gpg --with-fingerprint VeraCrypt_PGP_public_key.asc</strong>)</li><li>If the fingerprint is the expected one, import the public key: <strong>gpg --import VeraCrypt_PGP_public_key.asc</strong>
</li><li>Verify the signature of the Linux setup archive (here for version 1.23): <strong>
gpg --verify veracrypt-1.23-setup.tar.bz2.sig veracrypt-1.23-setup.tar.bz2</strong>
</li></ul>
</div><div class="ClearBoth"></div></body></html>
