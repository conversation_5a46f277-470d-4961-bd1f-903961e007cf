﻿<!DOCTYPE html>
<html lang="ru">
<head>
<meta http-equiv="content-type" content="text/html; charset=utf-8" />
<title>VeraCrypt - Бесплатное надёжное шифрование дисков с открытым исходным кодом</title>
<meta name="description" content="VeraCrypt это бесплатное программное обеспечение для шифрования дисков с открытым исходным кодом для Windows, Mac OS X (macOS) и Linux. В случае, если злоумышленник вынуждает вас раскрыть пароль, VeraCrypt обеспечивает правдоподобное отрицание наличия шифрования. В отличие от пофайлового шифрования, VeraCrypt шифрует данные в реальном времени (на лету), автоматически, прозрачно, требует очень мало памяти и не использует временные незашифрованные файлы."/>
<meta name="keywords" content="encryption, security, шифрование, безопасность"/>
<link href="styles.css" rel="stylesheet" type="text/css" />
</head>
<body>

<div>
<a href="Documentation.html"><img src="VeraCrypt128x128.png" alt="VeraCrypt"/></a>
</div>

<div id="menu">
	<ul>
	  <li><a href="Home.html">Начало</a></li>
	  <li><a href="Code.html">Исходный код</a></li>
	  <li><a href="Downloads.html">Загрузить</a></li>
	  <li><a class="active" href="Documentation.html">Документация</a></li>
	  <li><a href="Donation.html">Поддержать разработку</a></li>
	  <li><a href="https://sourceforge.net/p/veracrypt/discussion/" target="_blank">Форум</a></li>
	</ul>
</div>

<div>
<p>
<a href="Documentation.html">Документация</a>
<img src="arrow_right.gif" alt=">>" style="margin-top: 5px">
<a href="How%20to%20Back%20Up%20Securely.html">О безопасном резервном копировании</a>
</p></div>
<div class="wikidoc">
<div>
<h2>О безопасном резервном копировании</h2>
<p>Из-за аппаратных или программных ошибок/сбоев файлы в томе VeraCrypt могут оказаться повреждёнными. Поэтому
мы настоятельно рекомендуем регулярно делать резервные копии всех важных файлов (разумеется, это относится к
любым важным данным, а не только к зашифрованным в томах VeraCrypt).</p>
<h3>Несистемные тома</h3>
<p>Чтобы безопасно создать резервную копию несистемного тома VeraCrypt, рекомендуем следующую последовательность действий:</p>
<ol>
<li>Создайте новый том VeraCrypt с помощью мастера создания томов VeraCrypt (не включайте опции
<em>Быстрое форматирование</em> и <em>Динамический</em>). Это будет ваш <em>резервный</em> том, поэтому он должен
совпадать по размеру с <em>основным</em> томом (или быть больше него).<br>
<br>
Если <em>основной</em> том VeraCrypt – скрытый (см. раздел <a href="Hidden%20Volume.html">
<em>Скрытый том</em></a>), то <em>резервный</em> том также должен быть скрытым томом VeraCrypt. Прежде чем создать скрытый
<em>резервный</em> том, вы должны создать для него новый том, в котором он будет храниться (внешний том), не включая
опцию <em>Быстрое форматирование</em>. Кроме того, особенно если <em>резервный</em> том – на основе файла, скрытый
<em>резервный</em> том должен занимать лишь очень маленькую часть контейнера, а внешний том должен быть почти целиком
заполнен файлами (иначе это может неблагоприятно сказаться на правдоподобности отрицания наличия скрытого тома).</li>
<li>Смонтируйте вновь созданный <em>резервный</em> том.</li>
<li>Смонтируйте <em>основной</em> том.</li>
<li>Скопируйте все файлы из смонтированного <em>основного</em> тома непосредственно в смонтированный <em>резервный</em> том.</li></ol>
<h4>ВАЖНО: Если вы храните резервный том в месте, к которому может регулярно обращаться злоумышленник (например,
на устройстве, хранящемся в сейфе банка), вам следует повторять все описанные выше действия (включая шаг 1)
всякий раз, когда вы будете изготавливать резервную копию тома (см. ниже).</h4>
<p>Если вы будете выполнять все указанные выше действия, то этим помешаете неприятелю выяснить:</p>
<ul>
<li>какие сектора томов изменяются (так как вы всегда выполняете шаг 1), что особенно важно, например, если
устройство с резервным томом находится в банковском сейфе (или любом другом месте, к которому может регулярно
обращаться злоумышленник), и в томе содержится скрытый том (см. подробности в подразделе
<a href="Security%20Requirements%20for%20Hidden%20Volumes.html">
<em>Требования безопасности и меры предосторожности, относящиеся к скрытым томам</em></a> в главе
<a href="Plausible%20Deniability.html"><em>Правдоподобное отрицание наличия шифрования</em></a>);</li>
<li>что один из томов является резервной копией другого. </li></ul>
<h3>Системные разделы</h3>
<p>Примечание: Помимо резервного копирования файлов также рекомендуется делать резервные копии диска
восстановления VeraCrypt (выберите <em>Система</em> &gt; <em>Создать Диск восстановления</em>). Более подробную
информацию см. в разделе <em>Диск восстановления VeraCrypt</em>.</p>
<p>Чтобы надёжно и безопасно сделать резервную копию зашифрованного системного раздела, рекомендуем следующую
последовательность действий:</p>
<ol>
<li>Если в компьютере установлено несколько операционных систем, загрузите ту из них, которая не требует
предзагрузочной аутентификации.<br>
<br>
Если в компьютере установлена только одна операционная система, можно загрузиться с CD/DVD, содержащего WinPE
или BartPE (live-версию Windows, целиком хранящуюся на CD/DVD и оттуда же загружающуюся; подробности ищите в главе
<a href="FAQ.html"><em>Вопросы и ответы</em></a> по ключевому слову &lsquo;BartPE&rsquo;).<br>
<br>
Если оба указанных выше варианта невозможны, подключите свой системный диск как вторичный накопитель к другому
компьютеру и затем загрузите операционную систему, установленную в том компьютере.<br>
<br>
Примечание: Если операционная система, резервную копию которой вы хотите сделать, находится в скрытом томе
VeraCrypt (см. раздел <a href="Hidden%20Operating%20System.html">
<em>Скрытая операционная система</em></a>), то, из соображений безопасности, операционная система, которую вы
загружаете на этом этапе, должна быть либо ещё одной скрытой ОС, либо системой "live-CD" (см. выше). Более
подробную информацию см. в подразделе
<a href="Security%20Requirements%20for%20Hidden%20Volumes.html">
<em>Требования безопасности и меры предосторожности, касающиеся скрытых томов</em></a> в главе
<a href="Plausible%20Deniability.html"><em>Правдоподобное отрицание наличия шифрования</em></a>.
</li><li>Создайте новый несистемный том VeraCrypt с помощью мастера создания томов VeraCrypt (не включая опции
<em>Быстрое форматирование</em> и <em>Динамический</em>). Это будет ваш <em>резервный</em> том, поэтому
он по размеру должен совпадать с системным разделом (или превосходить его), резервную копию которого
вы намереваетесь сделать.<br>
<br>
Если операционная система, резервную копию которой вы хотите создать, установлена в скрытом томе
VeraCrypt (см. раздел <em>Скрытая операционная система</em>), то <em>резервный</em> том тоже должен быть скрытым.
Прежде чем создать скрытый <em>резервный</em> том, вы должны создать для него новый том, в котором он будет
храниться (внешний том), не включая опцию <em>Быстрое форматирование</em>. Кроме того, особенно если
<em>резервный</em> том – на основе файла, скрытый <em>резервный</em> том должен занимать лишь очень маленькую
часть контейнера, а внешний том должен быть почти целиком заполнен файлами (иначе это может неблагоприятно
сказаться на правдоподобности отрицания наличия скрытого тома).</li>
<li>Смонтируйте вновь созданный <em>резервный</em> том.</li>
<li>Смонтируйте системный раздел, резервную копию которого вы хотите сделать, выполнив следующее:
<ol type="a">
<li>Нажмите кнопку <em>Выбрать устройство</em> и выберите системный раздел, с которого нужно сделать
резервную копию (в случае скрытой ОС, выберите раздел, содержащий скрытый том, в котором установлена
скрытая ОС).</li>
<li>Нажмите <em>OK</em>. </li>
<li>Выберите <em>Система</em> &gt; <em>Смонтировать без предзагрузочной аутентификации</em>.</li>
<li>Введите свой пароль предзагрузочной аутентификации и нажмите <em>OK</em>.</li></ol>
</li><li>Смонтируйте <em>резервный</em> том, а затем с помощью какой-либо сторонней программы или
средствами Windows создайте образ файловой системы, находящейся в системном разделе (который на предыдущем
этапе был смонтирован как обычный том VeraCrypt) и сохраните этот образ непосредственно в смонтированном
резервном томе. </li></ol>
<h4>ВАЖНО: Если вы храните резервный том в месте, к которому может регулярно обращаться злоумышленник (например,
на устройстве, хранящемся в сейфе банка), вам следует повторять все описанные выше действия (включая шаг 2)
всякий раз, когда вы будете изготавливать резервную копию тома (см. ниже).</h4>
<p>Если вы будете выполнять все указанные выше действия, то этим помешаете неприятелю выяснить:</p>
<ul>
<li>какие сектора томов изменяются (так как вы всегда выполняете шаг 2), что особенно важно, например, если
устройство с резервным томом находится в банковском сейфе (или любом другом месте, к которому может регулярно
обращаться злоумышленник), и в томе содержится скрытый том (см. подробности в подразделе
<a href="Security%20Requirements%20for%20Hidden%20Volumes.html">
<em>Требования безопасности и меры предосторожности, касающиеся скрытых томов</em></a> в главе
<a href="Plausible%20Deniability.html"><em>Правдоподобное отрицание наличия шифрования</em></a>);
</li><li>что один из томов является резервной копией другого. </li></ul>
<h3>Общие замечания</h3>
<p>Если вы храните резервную копию тома в месте, где неприятель может сделать копию тома, имеет смысл
шифровать том каскадом (последовательностью) алгоритмов (например, AES-Twofish-Serpent). В противном
случае, если том зашифрован только одним алгоритмом, и этот алгоритм в дальнейшем удастся взломать
(например, вследствие прогресса в криптоанализе), неприятель сумеет расшифровать имеющиеся у него копии
тома. Вероятность взлома сразу трёх разных алгоритмов шифрования значительно ниже, чем одного из них.</p>
</div>
</div><div class="ClearBoth"></div></body></html>
