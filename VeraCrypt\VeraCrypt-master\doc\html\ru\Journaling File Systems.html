﻿<!DOCTYPE html>
<html lang="ru">
<head>
<meta http-equiv="content-type" content="text/html; charset=utf-8" />
<title>VeraCrypt - Бесплатное надёжное шифрование дисков с открытым исходным кодом</title>
<meta name="description" content="VeraCrypt это бесплатное программное обеспечение для шифрования дисков с открытым исходным кодом для Windows, Mac OS X (macOS) и Linux. В случае, если злоумышленник вынуждает вас раскрыть пароль, VeraCrypt обеспечивает правдоподобное отрицание наличия шифрования. В отличие от пофайлового шифрования, VeraCrypt шифрует данные в реальном времени (на лету), автоматически, прозрачно, требует очень мало памяти и не использует временные незашифрованные файлы."/>
<meta name="keywords" content="encryption, security, шифрование, безопасность"/>
<link href="styles.css" rel="stylesheet" type="text/css" />
</head>
<body>

<div>
<a href="Documentation.html"><img src="VeraCrypt128x128.png" alt="VeraCrypt"/></a>
</div>

<div id="menu">
	<ul>
	  <li><a href="Home.html">Начало</a></li>
	  <li><a href="Code.html">Исходный код</a></li>
	  <li><a href="Downloads.html">Загрузить</a></li>
	  <li><a class="active" href="Documentation.html">Документация</a></li>
	  <li><a href="Donation.html">Поддержать разработку</a></li>
	  <li><a href="https://sourceforge.net/p/veracrypt/discussion/" target="_blank">Форум</a></li>
	</ul>
</div>

<div>
<p>
<a href="Documentation.html">Документация</a>
<img src="arrow_right.gif" alt=">>" style="margin-top: 5px">
<a href="Security%20Requirements%20and%20Precautions.html">Требования безопасности и меры предосторожности</a>
<img src="arrow_right.gif" alt=">>" style="margin-top: 5px">
<a href="Journaling%20File%20Systems.html">Журналируемые файловые системы</a>
</p></div>

<div class="wikidoc">
<h1>Журналируемые файловые системы</h1>
<p>Если том VeraCrypt на основе файла находится в журналируемой файловой системе (например, в NTFS или Ext3), то в
свободной области хост-тома может оставаться копия контейнера VeraCrypt (или его фрагмента). Это может повлечь
за собой ряд проблем с безопасностью. Например, если вы измените у тома пароль и/или ключевые файлы, а неприятель
обнаружит старую копию или фрагмент (старый заголовок) тома VeraCrypt, он может с его помощью смонтировать том,
используя старый скомпрометированный пароль (и/или старые скомпрометированные ключевые файлы, действительные для
монтирования этого тома до того, как был перешифрован заголовок тома). Кроме того, некоторые журналируемые файловые
системы записывают в своих внутренних ресурсах время доступа к файлам и другую потенциально важную для сохранения
конфиденциальности информацию. Если вам нужна возможность правдоподобного отрицания наличия шифрования (см. раздел
<a href="Plausible%20Deniability.html"><em>Правдоподобное отрицание наличия шифрования</em></a>), хранить контейнеры
VeraCrypt на основе файлов в журналируемых файловых системах нельзя. Чтобы предотвратить возможные проблемы
безопасности, связанные с журналированием файловых систем, выполните одно из следующего:</p>
<ul>
<li>используйте тома TrueCrypt на основе раздела/устройства, а не на основе файла;</li>
<li>храните файловый контейнер в нежурналируемой файловой системе (например, в FAT32). </li></ul>
</div><div class="ClearBoth"></div></body></html>
