﻿<!DOCTYPE html>
<html lang="ru">
<head>
<meta http-equiv="content-type" content="text/html; charset=utf-8" />
<title>VeraCrypt - Бесплатное надёжное шифрование дисков с открытым исходным кодом</title>
<meta name="description" content="VeraCrypt это бесплатное программное обеспечение для шифрования дисков с открытым исходным кодом для Windows, Mac OS X (macOS) и Linux. В случае, если злоумышленник вынуждает вас раскрыть пароль, VeraCrypt обеспечивает правдоподобное отрицание наличия шифрования. В отличие от пофайлового шифрования, VeraCrypt шифрует данные в реальном времени (на лету), автоматически, прозрачно, требует очень мало памяти и не использует временные незашифрованные файлы."/>
<meta name="keywords" content="encryption, security, шифрование, безопасность"/>
<link href="styles.css" rel="stylesheet" type="text/css" />
</head>
<body>

<div>
<a href="Documentation.html"><img src="VeraCrypt128x128.png" alt="VeraCrypt"/></a>
</div>

<div id="menu">
	<ul>
	  <li><a href="Home.html">Начало</a></li>
	  <li><a href="Code.html">Исходный код</a></li>
	  <li><a href="Downloads.html">Загрузить</a></li>
	  <li><a class="active" href="Documentation.html">Документация</a></li>
	  <li><a href="Donation.html">Поддержать разработку</a></li>
	  <li><a href="https://sourceforge.net/p/veracrypt/discussion/" target="_blank">Форум</a></li>
	</ul>
</div>

<div>
<p>
<a href="Documentation.html">Документация</a>
<img src="arrow_right.gif" alt=">>" style="margin-top: 5px">
<a href="Security%20Requirements%20and%20Precautions.html">Требования безопасности и меры предосторожности</a>
<img src="arrow_right.gif" alt=">>" style="margin-top: 5px">
<a href="Data%20Leaks.html">Утечки данных</a>
<img src="arrow_right.gif" alt=">>" style="margin-top: 5px">
<a href="Hibernation%20File.html">Файл гибернации</a>
</p></div>

<div class="wikidoc">
<div>
<h1>Файл гибернации</h1>
<p>Примечание: описанная ниже проблема вас не касается, если системный раздел или системный диск зашифрован<span>*</span>
(см. подробности в главе <a href="System%20Encryption.html">
<em>Шифрование системы</em></a>) и если файл гибернации расположен на одном из разделов, входящих в область
действия шифрования системы (что, как правило, принимается по умолчанию), например, на разделе, в котором
установлена. Когда компьютер переходит в состояние гибернации, данные шифруются на лету перед тем, как они
будут сохранены в файле гибернации.</p>
<p>Когда компьютер переходит в состояние гибернации (или входит в режим энергосбережения), содержимое его
оперативной памяти записывается в так называемый файл гибернации на жёстком диске. Вы можете настроить
VeraCrypt (<em>Настройки</em> &gt; <em>Параметры</em> &gt; <em>Размонтировать все тома при: входе в энергосбережение</em>)
на автоматическое размонтирование всех смонтированных томов VeraCrypt, удаление их хранящихся в ОЗУ мастер-ключей
и очистку кэшированных в ОЗУ паролей (если они есть) перед тем, как компьютер перейдёт в состояние гибернации
(или войдёт в режим энергосбережения). Нужно, однако, иметь в виду, что если не используется шифрование системы
(см. главу <a href="System%20Encryption.html"><em>Шифрование системы</em></a>), VeraCrypt не может надёжно
препятствовать сохранению в файле гибернации в незашифрованном виде содержимого конфиденциальных файлов,
открытых в ОЗУ. Помните, что когда вы открываете файл, хранящийся в томе VeraCrypt, например, в текстовом
редакторе, содержимое этого файла в <i>незашифрованном</i> виде помещается в ОЗУ (и может оставаться в ОЗУ
<i>незашифрованным</i>, пока не будет выключен компьютер).<br>
<br>
Обратите внимание, что когда компьютер переходит в режим сна, на самом деле он может быть настроен на переход
в так называемый гибридный спящий режим, вызывающий гибернацию. Также учтите, что операционная система может
быть настроена на переход в режим гибернации или в гибридный спящий режим при выборе пункта «Завершить работу»
(см. подробности в документации на свою операционную систему).<br>
<br>
<strong>Чтобы избежать описанных выше проблем</strong>, зашифруйте системный раздел/диск (о том, как это сделать,
см. в главе <a href="System%20Encryption.html"><em>Шифрование системы</em></a>) и убедитесь, что файл гибернации
находится на одном из разделов, входящих в область действия шифрования системы (что, как правило, принимается
по умолчанию), например, на разделе, в котором установлена Windows. Когда компьютер переходит в состояние гибернации,
данные шифруются на лету перед тем, как они будут сохранены в файле гибернации.</p>
<p>Примечание: ещё один подходящий вариант – создать скрытую операционную систему (см. подробности в разделе
<a href="Hidden%20Operating%20System.html">
<em>Скрытая операционная система</em></a>)<span>.</span></p>
<p>Если по каким-то причинам вы не можете использовать шифрование системы, отключите или не допускайте гибернации
в своём компьютере, по крайней мере, в течение каждого сеанса, когда вы работаете с секретными данными и монтируете
том VeraCrypt.</p>
<p><span>*</span> ОТКАЗ ОТ ОТВЕТСТВЕННОСТИ: Поскольку Windows XP и Windows 2003 не предоставляют никакого API
для шифрования файлов гибернации, VeraCrypt пришлось модифицировать недокументированные компоненты Windows XP/2003,
чтобы позволить пользователям шифровать файлы гибернации. Поэтому VeraCrypt не может гарантировать, что файлы
гибернации Windows XP/2003 будут всегда зашифрованы. В ответ на нашу публичную жалобу на отсутствие API, компания
Microsoft начала предоставлять общедоступный API для шифрования файлов гибернации в Windows Vista и более поздних версиях
Windows. VeraCrypt использует этот API и поэтому может безопасно шифровать файлы гибернации в Windows Vista и более
поздних версиях Windows. Поэтому если вы используете Windows XP/2003 и хотите, чтобы файл гибернации был надёжно
зашифрован, настоятельно рекомендуем вам выполнить обновление до Windows Vista или более поздней версии.</p>
</div>
</div><div class="ClearBoth"></div></body></html>
