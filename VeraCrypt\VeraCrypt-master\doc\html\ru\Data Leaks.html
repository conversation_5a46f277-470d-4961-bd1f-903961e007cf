﻿<!DOCTYPE html>
<html lang="ru">
<head>
<meta http-equiv="content-type" content="text/html; charset=utf-8" />
<title>VeraCrypt - Бесплатное надёжное шифрование дисков с открытым исходным кодом</title>
<meta name="description" content="VeraCrypt это бесплатное программное обеспечение для шифрования дисков с открытым исходным кодом для Windows, Mac OS X (macOS) и Linux. В случае, если злоумышленник вынуждает вас раскрыть пароль, VeraCrypt обеспечивает правдоподобное отрицание наличия шифрования. В отличие от пофайлового шифрования, VeraCrypt шифрует данные в реальном времени (на лету), автоматически, прозрачно, требует очень мало памяти и не использует временные незашифрованные файлы."/>
<meta name="keywords" content="encryption, security, шифрование, безопасность"/>
<link href="styles.css" rel="stylesheet" type="text/css" />
</head>
<body>

<div>
<a href="Documentation.html"><img src="VeraCrypt128x128.png" alt="VeraCrypt"/></a>
</div>

<div id="menu">
	<ul>
	  <li><a href="Home.html">Начало</a></li>
	  <li><a href="Code.html">Исходный код</a></li>
	  <li><a href="Downloads.html">Загрузить</a></li>
	  <li><a class="active" href="Documentation.html">Документация</a></li>
	  <li><a href="Donation.html">Поддержать разработку</a></li>
	  <li><a href="https://sourceforge.net/p/veracrypt/discussion/" target="_blank">Форум</a></li>
	</ul>
</div>

<div>
<p>
<a href="Documentation.html">Документация</a>
<img src="arrow_right.gif" alt=">>" style="margin-top: 5px">
<a href="Security%20Requirements%20and%20Precautions.html">Требования безопасности и меры предосторожности</a>
<img src="arrow_right.gif" alt=">>" style="margin-top: 5px">
<a href="Data%20Leaks.html">Утечки данных</a>
</p></div>

<div class="wikidoc">
<h2>Утечки данных</h2>
<p>Когда смонтирован том VeraCrypt, операционная система и сторонние приложения могут записывать в незашифрованные тома
(обычно в незашифрованный системный том) незашифрованную информацию о данных, хранящихся в томе VeraCrypt (например,
имена и пути файлов, к которым недавно было обращение, создаваемые программами индексации базы данных, и т. д.), или
собственно данные в незашифрованном виде (временные файлы и т. п.), или незашифрованную информацию о находящейся в томе
VeraCrypt файловой системе.
</p>
<p>Обратите внимание, что Windows автоматически ведёт запись больших объёмов таких потенциально секретных данных, как
имена и пути открываемых вами файлов, запускаемые вами приложения и т. д. Например, Windows при работе с Проводником
использует набор ключей реестра, называемых “shellbags” (“скорлупа”) для хранения имени, размера, вида, значка и позиции папки.
При каждом открытии папки эта информация обновляется, включая дату и время доступа. Пакеты Windows Shellbags можно
найти в нескольких местах, в зависимости от версии операционной системы и профиля пользователя.
В Windows XP пакеты shellbags находятся в "<strong>HKEY_USERS\{USERID}\Software\Microsoft\Windows\Shell\</strong>" и "<strong>HKEY_USERS\{USERID}\Software\Microsoft\Windows\ShellNoRoam\</strong>".
В Windows 7 они располагаются в "<strong>HKEY_USERS\{USERID}\Local Settings\Software\Microsoft\Windows\Shell\</strong>".
Более подробную информацию см. на странице <a href="https://www.sans.org/reading-room/whitepapers/forensics/windows-shellbag-forensics-in-depth-34545" target="_blank">https://www.sans.org/reading-room/whitepapers/forensics/windows-shellbag-forensics-in-depth-34545</a>.
<p>Кроме того, начиная с Windows 8, при каждом монтировании тома VeraCrypt, отформатированном в NTFS, в журнал системных
событий записывается Событие 98 (Event 98), содержащее имя устройства (\\device\VeraCryptVolumeXX) тома. Эта
&quot;особенность&quot; журнала событий была представлена в Windows 8 как часть недавно введённых проверок состояния NTFS,
см. пояснения <a href="https://blogs.msdn.microsoft.com/b8/2012/05/09/redesigning-chkdsk-and-the-new-ntfs-health-model/" target="_blank">
здесь</a>. Чтобы избежать этой утечки, необходимо монтировать том VeraCrypt <a href="Removable%20Medium%20Volume.html">
как сменный носитель</a>. Большое спасибо Liran Elharar, обнаружившему эту утечку и предложившему способ её обхода.<br>
<br>
Чтобы предотвратить утечки данных, вы должны проделать следующее (возможны и альтернативные меры):</p>
<ul>
<li>Если вам <em>не</em> нужно правдоподобное отрицание наличия шифрования:
<ul>
<li>Зашифруйте системный раздел/диск (о том, как это сделать, см. главу
<a href="System%20Encryption.html"><em>Шифрование системы</em></a>) и убедитесь, что в течение каждого сеанса работы с
секретными данными смонтированы только зашифрованные файловые системы или системы, доступные только для чтения.<br>
<br>
или</li>
<li>Если вы не можете проделать указанное выше, загрузите или создайте "live CD"-версию своей операционной системы
(то есть live-систему, целиком расположенную на CD/DVD и оттуда же загружающуюся) – это гарантирует, что любые записываемые
в системный том данные записываются в RAM-диск (диск в ОЗУ). Когда вам требуется поработать с секретными данными,
загрузите систему с такого live-CD/DVD и проверьте, что в течение сеанса смонтированы только зашифрованные и/или доступные
только для чтения файловые системы.
</li></ul>
</li><li>Если вам <em>нужно</em> правдоподобное отрицание наличия шифрования:
<ul>
<li>Создайте скрытую операционную систему. При этом защита от утечек данных будет обеспечена VeraCrypt автоматически.
См. подробности в разделе <a href="Hidden%20Operating%20System.html"> <em>Скрытая операционная система</em></a>.<br>
<br>
или</li>
<li>Если вы не можете проделать вышеуказанное, загрузите или создайте "live CD"-версию своей операционной системы
(то есть live-систему, целиком расположенную на CD/DVD и оттуда же загружающуюся) – это гарантирует, что любые записываемые
в системный том данные записываются в RAM-диск (диск в ОЗУ). Когда вам требуется поработать с секретными данными,
загрузите систему с такого live-CD/DVD. Если вы используете скрытые тома, следуйте требованиям безопасности, указанным
в подразделе <a href="Security%20Requirements%20for%20Hidden%20Volumes.html">
<em>Требования безопасности и меры предосторожности, касающиеся скрытых томов</em></a>. Если скрытые тома вами не используются,
проверьте, что в течение сеанса смонтированы только несистемные тома VeraCrypt на основе раздела и/или файловые системы,
доступные только для чтения.
</li></ul>
</li></ul>
</div><div class="ClearBoth"></div></body></html>
