<!DOCTYPE html>
<html lang="en">
<head>
<meta http-equiv="content-type" content="text/html; charset=utf-8" />
<title>VeraCrypt - Free Open source disk encryption with strong security for the Paranoid</title>
<meta name="description" content="VeraCrypt is free open-source disk encryption software for Windows, Mac OS X and Linux. In case an attacker forces you to reveal the password, VeraCrypt provides plausible deniability. In contrast to file encryption, data encryption performed by VeraCrypt is real-time (on-the-fly), automatic, transparent, needs very little memory, and does not involve temporary unencrypted files."/>
<meta name="keywords" content="encryption, security"/>
<link href="styles.css" rel="stylesheet" type="text/css" />
</head>
<body>

<div>
<a href="Documentation.html"><img src="VeraCrypt128x128.png" alt="VeraCrypt"/></a>
</div>

<div id="menu">
	<ul>
	  <li><a href="Home.html">Home</a></li>
	  <li><a href="Code.html">Source Code</a></li>
	  <li><a href="Downloads.html">Downloads</a></li>
	  <li><a class="active" href="Documentation.html">Documentation</a></li>
	  <li><a href="Donation.html">Donate</a></li>
	  <li><a href="https://sourceforge.net/p/veracrypt/discussion/" target="_blank">Forums</a></li>
	</ul>
</div>

<div>
<p>
<a href="Documentation.html">Documentation</a>
<img src="arrow_right.gif" alt=">>" style="margin-top: 5px">
<a href="Security%20Requirements%20and%20Precautions.html">Security Requirements and Precautions</a>
<img src="arrow_right.gif" alt=">>" style="margin-top: 5px">
<a href="Trim%20Operation.html">Trim Operation</a>
</p></div>

<div class="wikidoc">
<h1>Trim Operation</h1>
<div style="text-align:left; margin-top:19px; margin-bottom:19px; padding-top:0px; padding-bottom:0px">
Some storage devices (e.g., some solid-state drives, including USB flash drives) use so-called 'trim' operation to mark drive sectors as free e.g. when a file is deleted. Consequently, such sectors may contain unencrypted zeroes or other undefined data (unencrypted)
 even if they are located within a part of the drive that is encrypted by VeraCrypt.<br>
<br>
On Windows, VeraCrypt allows users to control the trim operation for both non-system and system volumes:
<ul>
<li>For non-system volumes, trim is blocked by default. Users can enable trim through VeraCrypt's interface by navigating to "Settings -> Performance/Driver Configuration" and checking the option "Allow TRIM command for non-system SSD partition/drive."</li>
<li>For <a href="System%20Encryption.html">system encryption</a>, trim is enabled by default (unless a <a href="Hidden%20Operating%20System.html">hidden operating system</a> is running). Users can disable trim by navigating to "System -> Settings" and checking the option "Block TRIM command on system partition/drive."</li>
</ul>

Under Linux, VeraCrypt does not block the trim operation on volumes using the native Linux kernel cryptographic services, which is the default setting. To block TRIM on Linux, users should either enable the "do not use kernel cryptographic services" option in VeraCrypt's Preferences (applicable only to volumes mounted afterward) or use the <code>--mount-options=nokernelcrypto</code> switch in the command line when mounting.
<br>
<br>
Under macOS, VeraCrypt does not support the trim operation. Therefore, trim is always blocked on all volumes.
<br>
<br>
In cases where trim operations occur, the adversary will be able to tell which sectors contain free space (and may be able to use this information for
 further analysis and attacks) and <a href="Plausible%20Deniability.html" style="text-align:left; color:#0080c0; text-decoration:none">
plausible deniability</a> may be negatively affected. In order to avoid these issues, users should either disable trim in VeraCrypt settings as previously described or make sure VeraCrypt volumes are not located on drives that use the trim operation.</div>
<p>To find out whether a device uses the trim operation, please refer to documentation supplied with the device or contact the vendor/manufacturer.</p>
</div><div class="ClearBoth"></div></body></html>
