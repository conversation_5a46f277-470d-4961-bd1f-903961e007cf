﻿<!DOCTYPE html>
<html lang="ru">
  <head>
    <meta http-equiv="content-type" content="text/html; charset=utf-8" />
    <title>
      VeraCrypt - Бесплатное надёжное шифрование дисков с открытым исходным кодом
    </title>
    <meta
      name="description"
      content="VeraCrypt это бесплатное программное обеспечение для шифрования дисков с открытым исходным кодом для Windows, Mac OS X (macOS) и Linux. В случае, если злоумышленник вынуждает вас раскрыть пароль, VeraCrypt обеспечивает правдоподобное отрицание наличия шифрования. В отличие от пофайлового шифрования, VeraCrypt шифрует данные в реальном времени (на лету), автоматически, прозрачно, требует очень мало памяти и не использует временные незашифрованные файлы."
    />
    <meta name="keywords" content="encryption, security, шифрование, безопасность" />
    <link href="styles.css" rel="stylesheet" type="text/css" />
  </head>
  <body>
    <div>
      <a href="Documentation.html"
        ><img src="VeraCrypt128x128.png" alt="VeraCrypt"
      /></a>
    </div>

    <div id="menu">
      <ul>
        <li><a href="Home.html">Начало</a></li>
        <li><a href="Code.html">Исходный код</a></li>
        <li><a href="Downloads.html">Загрузить</a></li>
        <li><a class="active" href="Documentation.html">Документация</a></li>
        <li><a href="Donation.html">Поддержать разработку</a></li>
        <li>
          <a
            href="https://sourceforge.net/p/veracrypt/discussion/"
            target="_blank">Форум</a>
        </li>
      </ul>
    </div>

    <div>
      <p>
        <a href="Documentation.html">Документация</a>
        <img src="arrow_right.gif" alt=">>" style="margin-top: 5px" />
        <a href="Technical%20Details.html">Технические подробности</a>
        <img src="arrow_right.gif" alt=">>" style="margin-top: 5px" />
        <a href="Keyfiles.html">Ключевые файлы</a>
      </p>
    </div>

<div class="wikidoc">
<h1>Ключевые файлы</h1>
<div style="text-align:left; margin-top:19px; margin-bottom:19px; padding-top:0px; padding-bottom:0px">
<p>Ключевой файл VeraCrypt – это файл, содержимое которого объединено с паролем. В качестве ключевого файла можно
использовать любой файл. Пользователь также может сгенерировать ключевой файл с помощью встроенного генератора
ключевых файлов, который использует VeraCrypt RNG для создания файла со случайным содержимым (см. подробности
в разделе <a href="Random%20Number%20Generator.html"><em>Генератор случайных чисел</em></a>).</p>
<p>Максимальный размер ключевого файла не ограничен, однако обрабатываются только его первые 1 048 576 байт (1 МиБ)
(все остальные байты игнорируются, чтобы не жертвовать производительностью из-за обработки очень больших файлов).
Можно указывать один или несколько ключевых файлов (количество не ограничено).</p>
<p>Ключевые файлы могут храниться на токенах безопасности и смарт-картах, совместимых с PKCS-11 [23], защищённых
несколькими пин-кодами (которые можно ввести с помощью аппаратной пин-панели или через графический интерфейс VeraCrypt).</p>
<p>Ключевые файлы обрабатываются и применяются к паролю следующим способом:</p>
<ol>
<li>Пусть <em>P</em> это пароль тома VeraCrypt, указанный пользователем (может быть пустым)</li>
<li>Пусть <em>KP</em> это пул ключевых файлов</li>
<li>Пусть <em>kpl</em> это размер пула ключевых файлов <em>KP</em>, в байтах (64, то есть 512 бит);
<p><em>kpl</em> должен быть кратен выходному размеру хеш-функции <em>H</em></p></li>
<li>Пусть <em>pl</em> это длина пароля <em>P</em>, в байтах (в текущей версии: 0 &le; <em>pl</em> &le; 64)</li>
<li>Если <em>kpl &gt; pl</em>, добавляем (<em>kpl &ndash; pl</em>) нулевых байт к паролю <em>P</em> (таким образом,
<em>pl = kpl</em>)</li>
<li>Заполняем пул ключевых файлов <em>KP</em> нулевыми байтами в количестве <em>kpl</em>.</li>
<li>Для каждого ключевого файла выполняем следующие шаги:
<ol type="a">
<li>Устанавливаем положение указателя пула ключевых файлов в начало пула</li>
<li>Инициализируем хеш-функцию <em>H</em></li>
<li>Загружаем все байты ключевого файла один за другим, и для каждого загруженного байта выполняем следующие шаги:
<ol type="i">
<li>Хешируем загруженный байт с помощью хеш-функции <em>H</em> без инициализации хеша, чтобы получить
промежуточный хеш (состояние) <em>M</em>. Не финализируем хеш (состояние сохраняется для следующего раунда).</li>
<li>Делим состояние <em>M</em> на отдельные байты.<br>
Например, если выходной размер хеша составляет 4 байта, (<em>T</em><sub>0</sub> || <em>T</em><sub>1</sub> ||
<em>T</em><sub>2</sub> || <em>T</em><sub>3</sub>) = <em>M</em> </li>
<li>Записываем эти байты (полученные на шаге 7.c.ii) по отдельности в пул ключевых файлов с помощью операции
сложения по модулю 2<sup>8</sup> (не заменяя старые значения в пуле) в позиции указателя пула. После записи
байта позиция указателя пула увеличивается на один байт. Когда указатель достигает конца пула, его положение
устанавливается в начало пула.
</li></ol>
</li></ol>
</li><li>Применяем содержимое пула ключевых файлов к паролю <em>P</em>, используя следующий метод:
<ol type="a">
<li>Делим пароль <em>P</em> на отдельные байты <em>B</em><sub>0</sub>...<em>B</em><sub>pl-1</sub>.<br>
Обратите внимание, что если пароль был короче пула ключевых файлов, то пароль дополнялся нулевыми байтами
до длины пула на шаге 5 (следовательно, в этот момент длина пароля всегда больше или равна длине пула ключевых файлов).</li>
<li>Делим пул ключевых файлов <em>KP</em> на отдельные байты <em>G</em><sub>0</sub>...<em>G</em><sub>kpl-1</sub></li>
<li>Для 0 &le; i &lt; kpl выполняем: Bi = Bi &oplus; Gi</li>
<li><em>P</em> = <em>B</em><sub>0</sub> || <em>B</em><sub>1</sub> || ... || <em>B</em><sub>pl-2</sub> ||
<em>B</em><sub>pl-1</sub> </li></ol></li>
<li>Пароль <em>P</em> (после применения к нему содержимого пула ключевых файлов) теперь передаётся в функцию
формирования ключа заголовка PBKDF2 (PKCS #5 v2), которая его обрабатывает (вместе с солью и другими данными)
используя выбранный пользователем криптографически безопасный алгоритм хеширования (например, SHA-512).
См. подробности в разделе <a href="Header%20Key%20Derivation.html">
<em>Формирование ключа заголовка, соль и количество итераций</em></a>.
</li></ol>
<p>Роль хеш-функции <em>H</em> заключается просто в выполнении диффузии [2]. В качестве хеш-функции
<em>H</em> применяется CRC-32. Обратите внимание, что вывод CRC-32 впоследствии обрабатывается с использованием
криптографически безопасного хеш-алгоритма: содержимое пула ключевых файлов (в дополнение к хешированию с помощью CRC-32)
применяется к паролю, который затем передаётся в функцию формирования ключа заголовка PBKDF2 (PKCS #5 v2), которая
его обрабатывает (вместе с солью и другими данными), используя выбранный пользователем криптографически безопасный
алгоритм хеширования (например, SHA-512). Результирующие значения используются для формирования ключа заголовка
и вторичного ключа заголовка (режим XTS).</p>
<p>&nbsp;</p>
<p><a href="Personal%20Iterations%20Multiplier%20%28PIM%29.html" style="text-align:left; color:#0080c0; text-decoration:none; font-weight:bold">Следующий раздел &gt;&gt;</a></p>
</div>
</div><div class="ClearBoth"></div></body></html>
