<!DOCTYPE html>
<html lang="en">
<head>
<meta http-equiv="content-type" content="text/html; charset=utf-8" />
<title>VeraCrypt - Free Open source disk encryption with strong security for the Paranoid</title>
<meta name="description" content="VeraCrypt is free open-source disk encryption software for Windows, Mac OS X and Linux. In case an attacker forces you to reveal the password, VeraCrypt provides plausible deniability. In contrast to file encryption, data encryption performed by VeraCrypt is real-time (on-the-fly), automatic, transparent, needs very little memory, and does not involve temporary unencrypted files."/>
<meta name="keywords" content="encryption, security"/>
<link href="styles.css" rel="stylesheet" type="text/css" />
<style>
.textbox {
  vertical-align: top;
  height: auto !important;
  font-family: Helvetica,sans-serif;
  font-size: 20px;
  font-weight: bold;
  margin: 10px;
  padding: 10px;
  background-color: white;
  width: auto;
  border-radius: 10px;
}

.texttohide {
  font-family: Helvetica,sans-serif;
  font-size: 14px;
  font-weight: normal;
}


</style>
</head>
<body>

<div>
<a href="Documentation.html"><img src="VeraCrypt128x128.png" alt="VeraCrypt"/></a>
</div>

<div id="menu">
	<ul>
	  <li><a href="Home.html">Home</a></li>
	  <li><a href="Code.html">Source Code</a></li>
	  <li><a href="Downloads.html">Downloads</a></li>
	  <li><a class="active" href="Documentation.html">Documentation</a></li>
	  <li><a href="Donation.html">Donate</a></li>
	  <li><a href="https://sourceforge.net/p/veracrypt/discussion/" target="_blank">Forums</a></li>
	</ul>
</div>

<div>
<p>
<a href="Documentation.html">Documentation</a>
<img src="arrow_right.gif" alt=">>" style="margin-top: 5px">
<a href="Technical%20Details.html">Technical Details</a>
<img src="arrow_right.gif" alt=">>" style="margin-top: 5px">
<a href="CompilingGuidelines.html">Building VeraCrypt From Source</a>
<img src="arrow_right.gif" alt=">>" style="margin-top: 5px">
<a href="CompilingGuidelineLinux.html">Linux Build Guide</a>
</p></div>

<div class="wikidoc">
This guide describes how to set up a Linux System to build VeraCrypt from source and how to perform compilation. <br>
The procedure for a Ubuntu 22.04 LTS system is described here as an example, the procedure for other Linux systems is analogous.
</div>

<div class="wikidoc">
<br>
<br>
The following components are required for compiling VeraCrypt:
<ol>
	<li>GNU Make</li>
	<li>GNU C/C++ Compiler</li>
	<li>YASM 1.3.0</li>
	<li>pkg-config</li>
	<li>wxWidgets 3.x shared library and header files installed by the system or wxWidgets 3.x library source code </li>
	<li>FUSE library and header files</li>
	<li>PCSC-lite library and header files</li>
</ol>
</div>

<div class="wikidoc">
<p>Below are the procedure steps. Clicking on any of the link takes directly to the related step:
<ul>
<li><strong><a href="#InstallationOfGNUMake">Installation of GNU Make</a></strong></li>
<li><strong><a href="#InstallationOfGNUCompiler">Installation of GNU C/C++ Compiler</a></strong></li>
<li><strong><a href="#InstallationOfYASM">Installation of YASM</a></strong></li>
<li><strong><a href="#InstallationOfPKGConfig">Installation of pkg-config</a></strong></li>
<li><strong><a href="#InstallationOfwxWidgets">Installation of wxWidgets 3.2</a></strong></li>
<li><strong><a href="#InstallationOfFuse">Installation of libfuse</a></strong></li>
<li><strong><a href="#InstallationOfPCSCLite">Installation of libpcsclite</a></strong></li>
<li><strong><a href="#DownloadVeraCrypt">Download VeraCrypt</a></strong></li>
<li><strong><a href="#CompileVeraCrypt">Compile VeraCrypt</a></strong></li>
</ul>
</p>
<p>They can also be performed by running the below list of commands in a terminal or by copying them to a script:<br>
<code>
sudo apt update <br>
sudo apt install -y build-essential yasm pkg-config libwxgtk3.0-gtk3-dev <br>
sudo apt install -y libfuse-dev git libpcsclite-dev <br>
git clone https://github.com/veracrypt/VeraCrypt.git <br>
cd ~/VeraCrypt/src <br>
make
</code>
</p>
</div>

<div class="wikidoc">
 <div class="textbox" id="InstallationOfGNUMake">
  <a href="#InstallationOfGNUMake">Installation of GNU Make</a>
  <div class="texttohide">
    <p>
		<ol>
			<li>
				Open a terminal
			</li>
			<li>
				Execute the following commands: <br>
				<code>
				sudo apt update <br>
				sudo apt install build-essential
				</code>
			</li>
		</ol>
	</p>
  </div>
 </div>
 
 <div class="textbox" id="InstallationOfGNUCompiler">
  <a href="#InstallationOfGNUCompiler">Installation of GNU C/C++ Compiler</a>
  <div class="texttohide">
    <p> If the build-essential were already installed in the step before, this step can be skipped.
		<ol>
			<li>
				Open a terminal
			</li>
			<li>
				Execute the following commands: <br>
				<code>
				sudo apt update <br>
				sudo apt install build-essential
				</code>
			</li>
		</ol>
	</p>
  </div>
 </div>

 <div class="textbox" id="InstallationOfYASM">
  <a href="#InstallationOfYASM">Installation of YASM</a>
  <div class="texttohide">
    <p>
		<ol>
			<li>
				Open a terminal
			</li>
			<li>
				Execute the following commands: <br>
				<code>
				sudo apt update <br>
				sudo apt install yasm
				</code>
			</li>
		</ol>
	</p>
  </div>
 </div>

 <div class="textbox" id="InstallationOfPKGConfig">
  <a href="#InstallationOfPKGConfig">Installation of pkg-config</a>
  <div class="texttohide">
    <p>
		<ol>
			<li>
				Open a terminal
			</li>
			<li>
				Execute the following commands: <br>
				<code>
				sudo apt update <br>
				sudo apt install pkg-config
				</code>
			</li>
		</ol>
	</p>
  </div>
 </div>
 
 <div class="textbox" id="InstallationOfwxWidgets">
  <a href="#InstallationOfwxWidgets">Installation of wxWidgets 3.2</a>
  <div class="texttohide">
    <p>
		<ol>
			<li>
				Open a terminal
			</li>
			<li>
				Execute the following commands: <br>
				<code>		
				sudo apt update <br>
				sudo apt install libwxgtk3.0-gtk3-dev <br>
				</code>
			</li>
		</ol>
	</p>
  </div>
 </div>
 
 <div class="textbox" id="InstallationOfFuse">
  <a href="#InstallationOfFuse">Installation of libfuse</a>
  <div class="texttohide">
    <p>
		<ol>
			<li>
				Open a terminal
			</li>
			<li>
				Execute the following commands: <br>
				<code>
				sudo apt update <br>
				sudo apt install libfuse-dev
				</code>
			</li>
		</ol>
	</p>
  </div>
 </div>

<div class="textbox" id="InstallationOfPCSCLite">
	<a href="#InstallationOfPCSCLite">Installation of libpcsclite</a>
	<div class="texttohide">
		<p>
		<ol>
			<li>
				Open a terminal
			</li>
			<li>
				Execute the following commands: <br>
				<code>
					sudo apt update <br>
					sudo apt install libpcsclite-dev
				</code>
			</li>
		</ol>
		</p>
	</div>
</div>
 
 <div class="textbox" id="DownloadVeraCrypt">
  <a href="#DownloadVeraCrypt">Download VeraCrypt</a>
  <div class="texttohide">
    <p>
		<ol>
			<li>
				Open a terminal
			</li>
			<li>
				Execute the following commands: <br>
				<code>
				sudo apt update <br>
				sudo apt install git <br>
				git clone https://github.com/veracrypt/VeraCrypt.git
				</code>
			</li>
		</ol>
	</p>
  </div>
 </div>
 
 <div class="textbox" id="CompileVeraCrypt">
  <a href="#CompileVeraCrypt">Compile VeraCrypt</a>
  <div class="texttohide">
    <p> Remarks: <br>
	<ul>
		<li>
			By default, a universal executable supporting both graphical and text user interface (through the switch --text) is built. <br>
			On Linux, a console-only executable, which requires no GUI library, can be built using the 'NOGUI' parameter. <br>
			For that, you need to dowload wxWidgets sources, extract them to a location of your choice and then run the following commands: <br>
			<code>
			make NOGUI=1 WXSTATIC=1 WX_ROOT=/path/to/wxWidgetsSources wxbuild <br>
			make NOGUI=1 WXSTATIC=1 WX_ROOT=/path/to/wxWidgetsSources
			</code>
		</li>
		<li>
			If you are not using the system wxWidgets library, you will have to download and use wxWidgets sources like the remark above but this time the following commands should be run to build GUI version of VeraCrypt (NOGUI is not specified): <br>
			<code>
			make WXSTATIC=1 WX_ROOT=/path/to/wxWidgetsSources wxbuild <br>
			make WXSTATIC=1 WX_ROOT=/path/to/wxWidgetsSources
			</code>
		</li>
	</ul>
	Steps:
		<ol>
			<li>
				Open a terminal
			</li>
			<li>
				Execute the following commands: <br>
				<code>		
				cd ~/VeraCrypt/src <br>
				make
				</code>
			</li>
			<li>
				If successful, the VeraCrypt executable should be located in the directory 'Main'.
			</li>
		</ol>
	</p>
  </div>
 </div>
 
</div>
</body></html>
