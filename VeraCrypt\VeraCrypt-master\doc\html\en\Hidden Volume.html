<!DOCTYPE html>
<html lang="en">
<head>
<meta http-equiv="content-type" content="text/html; charset=utf-8" />
<title>VeraCrypt - Free Open source disk encryption with strong security for the Paranoid</title>
<meta name="description" content="VeraCrypt is free open-source disk encryption software for Windows, Mac OS X and Linux. In case an attacker forces you to reveal the password, VeraCrypt provides plausible deniability. In contrast to file encryption, data encryption performed by VeraCrypt is real-time (on-the-fly), automatic, transparent, needs very little memory, and does not involve temporary unencrypted files."/>
<meta name="keywords" content="encryption, security"/>
<link href="styles.css" rel="stylesheet" type="text/css" />
</head>
<body>

<div>
<a href="Documentation.html"><img src="VeraCrypt128x128.png" alt="VeraCrypt"/></a>
</div>

<div id="menu">
	<ul>
	  <li><a href="Home.html">Home</a></li>
	  <li><a href="Code.html">Source Code</a></li>
	  <li><a href="Downloads.html">Downloads</a></li>
	  <li><a class="active" href="Documentation.html">Documentation</a></li>
	  <li><a href="Donation.html">Donate</a></li>
	  <li><a href="https://sourceforge.net/p/veracrypt/discussion/" target="_blank">Forums</a></li>
	</ul>
</div>

<div>
<p>
<a href="Documentation.html">Documentation</a>
<img src="arrow_right.gif" alt=">>" style="margin-top: 5px">
<a href="Plausible%20Deniability.html">Plausible Deniability</a>
<img src="arrow_right.gif" alt=">>" style="margin-top: 5px">
<a href="Hidden%20Volume.html">Hidden Volume</a>
</p></div>

<div class="wikidoc">
<h1>Hidden Volume</h1>
<div style="text-align:left; margin-top:19px; margin-bottom:19px; padding-top:0px; padding-bottom:0px">
It may happen that you are forced by somebody to reveal the password to an encrypted volume. There are many situations where you cannot refuse to reveal the password (for example, due to extortion). Using a so-called hidden volume allows you to solve such situations
 without revealing the password to your volume.</div>
<div style="text-align:left; margin-top:19px; margin-bottom:19px; padding-top:0px; padding-bottom:0px">
<img src="Beginner's Tutorial_Image_024.gif" alt="The layout of a standard VeraCrypt volume before and after a hidden volume was created within it." width="606" height="412"></div>
<div style="text-align:left; margin-top:19px; margin-bottom:19px; padding-top:0px; padding-bottom:0px">
<em style="text-align:left">The layout of a standard VeraCrypt volume before and after a hidden volume was created within it.</em></div>
<div style="text-align:left; margin-top:19px; margin-bottom:19px; padding-top:0px; padding-bottom:0px">
<br style="text-align:left">
The principle is that a VeraCrypt volume is created within another VeraCrypt volume (within the free space on the volume). Even when the outer volume is mounted, it should be impossible to prove whether there is a hidden volume within it or not*, because free
 space on <em style="text-align:left">any </em>VeraCrypt volume is always filled with random data when the volume is created** and no part of the (unmounted) hidden volume can be distinguished from random data. Note that VeraCrypt does not modify the file
 system (information about free space, etc.) within the outer volume in any way.</div>
<div style="text-align:left; margin-top:19px; margin-bottom:19px; padding-top:0px; padding-bottom:0px">
<br style="text-align:left">
The password for the hidden volume must be substantially different from the password for the outer volume. To the outer volume, (before creating the hidden volume within it) you should copy some sensitive-looking files that you actually do NOT want to hide.
 These files will be there for anyone who would force you to hand over the password. You will reveal only the password for the outer volume, not for the hidden one. Files that really are sensitive will be stored on the hidden volume.</div>
<div style="text-align:left; margin-top:19px; margin-bottom:19px; padding-top:0px; padding-bottom:0px">
A hidden volume can be mounted the same way as a standard VeraCrypt volume: Click
<em style="text-align:left">Select File</em> or <em style="text-align:left">Select Device
</em>to select the outer/host volume (important: make sure the volume is <em style="text-align:left">
not</em> mounted). Then click <em style="text-align:left">Mount</em>, and enter the password for the hidden volume. Whether the hidden or the outer volume will be mounted is determined by the entered password (i.e., when you enter the password for the outer
 volume, then the outer volume will be mounted; when you enter the password for the hidden volume, the hidden volume will be mounted).</div>
<div style="text-align:left; margin-top:19px; margin-bottom:19px; padding-top:0px; padding-bottom:0px">
VeraCrypt first attempts to decrypt the standard volume header using the entered password. If it fails, it loads the area of the volume where a hidden volume header can be stored (i.e. bytes 65536&ndash;131071, which contain solely random data when there is
 no hidden volume within the volume) to RAM and attempts to decrypt it using the entered password. Note that hidden volume headers cannot be identified, as they appear to consist entirely of random data. If the header is successfully decrypted (for information
 on how VeraCrypt determines that it was successfully decrypted, see the section <a href="Encryption%20Scheme.html" style="text-align:left; color:#0080c0; text-decoration:none">
Encryption Scheme</a>), the information about the size of the hidden volume is retrieved from the decrypted header (which is still stored in RAM), and the hidden volume is mounted (its size also determines its offset).</div>
<div style="text-align:left; margin-top:19px; margin-bottom:19px; padding-top:0px; padding-bottom:0px">
A hidden volume can be created within any type of VeraCrypt volume, i.e., within a file-hosted volume or partition/device-hosted volume (requires administrator privileges). To create a hidden VeraCrypt volume, click on
<em style="text-align:left">Create Volume </em>in the main program window and select
<em style="text-align:left">Create a hidden VeraCrypt volume</em>. The Wizard will provide help and all information necessary to successfully create a hidden VeraCrypt volume.</div>
<div id="hidden_volume_size_issue" style="text-align:left; margin-top:19px; margin-bottom:19px; padding-top:0px; padding-bottom:0px">
When creating a hidden volume, it may be very difficult or even impossible for an inexperienced user to set the size of the hidden volume such that the hidden volume does not overwrite data on the outer volume. Therefore, the Volume Creation Wizard automatically
 scans the cluster bitmap of the outer volume (before the hidden volume is created within it) and determines the maximum possible size of the hidden volume.***</div>
<div style="text-align:left; margin-top:19px; margin-bottom:19px; padding-top:0px; padding-bottom:0px">
If there are any problems when creating a hidden volume, refer to the chapter <a href="Troubleshooting.html" style="text-align:left; color:#0080c0; text-decoration:none">
Troubleshooting</a> for possible solutions.<br style="text-align:left">
<br style="text-align:left">
<br style="text-align:left">
Note that it is also possible to create and boot an operating system residing in a hidden volume (see the section
<a href="Hidden%20Operating%20System.html" style="text-align:left; color:#0080c0; text-decoration:none">
Hidden Operating System</a> in the chapter <a href="Plausible%20Deniability.html">
Plausible Deniability</a>).</div>
<hr align="left" size="1" width="189" style="text-align:left; height:0px; border-width:0px 1px 1px; border-style:solid; border-color:#000000">
<p><span style="text-align:left; font-size:10px; line-height:12px">* Provided that all the instructions in the VeraCrypt Volume Creation Wizard have been followed and provided that the requirements and precautions listed in the subsection
<a href="Security%20Requirements%20for%20Hidden%20Volumes.html" style="text-align:left; color:#0080c0; text-decoration:none">
Security Requirements and Precautions Pertaining to Hidden Volumes</a> are followed<em style="text-align:left">.</em></span><br style="text-align:left">
<span style="text-align:left; font-size:10px; line-height:12px">** Provided that the options
<em style="text-align:left">Quick Format</em> and <em style="text-align:left">Dynamic</em> are disabled and provided that the volume does not contain a filesystem that has been encrypted in place (VeraCrypt does not allow the user to create a hidden volume
 within such a volume). For information on the method used to fill free volume space with random data, see chapter
<a href="Technical%20Details.html" style="text-align:left; color:#0080c0; text-decoration:none">
Technical Details</a>, section <a href="VeraCrypt%20Volume%20Format%20Specification.html" style="text-align:left; color:#0080c0; text-decoration:none">
VeraCrypt Volume Format Specification</a><em style="text-align:left">.</em></span><br style="text-align:left">
<span style="text-align:left; font-size:10px; line-height:12px">*** The wizard scans the cluster bitmap to determine the size of the uninterrupted area of free space (if there is any) whose end is aligned with the end of the outer volume. This area accommodates
 the hidden volume and therefore the size of this area limits the maximum possible size of the hidden volume. On Linux and Mac OS X, the wizard actually does not scan the cluster bitmap, but the driver detects any data written to the outer volume and uses their
 position as previously described.</span></p>
<p>&nbsp;</p>
<p><a href="Protection%20of%20Hidden%20Volumes.html" style="text-align:left; color:#0080c0; text-decoration:none; font-weight:bold">Next Section &gt;&gt;</a></p>
</div><div class="ClearBoth"></div></body></html>
