﻿<!DOCTYPE html>
<html lang="ru">
<head>
<meta http-equiv="content-type" content="text/html; charset=utf-8" />
<title>VeraCrypt - Бесплатное надёжное шифрование дисков с открытым исходным кодом</title>
<meta name="description" content="VeraCrypt это бесплатное программное обеспечение для шифрования дисков с открытым исходным кодом для Windows, Mac OS X (macOS) и Linux. В случае, если злоумышленник вынуждает вас раскрыть пароль, VeraCrypt обеспечивает правдоподобное отрицание наличия шифрования. В отличие от пофайлового шифрования, VeraCrypt шифрует данные в реальном времени (на лету), автоматически, прозрачно, требует очень мало памяти и не использует временные незашифрованные файлы."/>
<meta name="keywords" content="encryption, security, шифрование, безопасность"/>
<link href="styles.css" rel="stylesheet" type="text/css" />
</head>
<body>

<div>
<a href="Documentation.html"><img src="VeraCrypt128x128.png" alt="VeraCrypt"/></a>
</div>

<div id="menu">
	<ul>
	  <li><a href="Home.html">Начало</a></li>
	  <li><a href="Code.html">Исходный код</a></li>
	  <li><a href="Downloads.html">Загрузить</a></li>
	  <li><a class="active" href="Documentation.html">Документация</a></li>
	  <li><a href="Donation.html">Поддержать разработку</a></li>
	  <li><a href="https://sourceforge.net/p/veracrypt/discussion/" target="_blank">Форум</a></li>
	</ul>
</div>

<div>
<p>
<a href="Documentation.html">Документация</a>
<img src="arrow_right.gif" alt=">>" style="margin-top: 5px">
<a href="Normal%20Unmount%20vs%20Force%20Unmount.html">Чем обычное размонтирование отличается от принудительного</a>
</p></div>

<div class="wikidoc">
<h1>Чем обычное размонтирование отличается от принудительного</h1>
<p>Важно понимать различия между операциями <em>Обычное размонтирование</em> и <em>Принудительное размонтирование</em>, так как это потенциально влияет на пользовательские данные.</p>

<h2>Обычное размонтирование</h2>

<p>Во время обычного размонтирования VeraCrypt выполняет следующие действия:</p>

<ol>
    <li>Даёт запрос операционной системе Windows заблокировать том, запрещая дальнейшие операции ввода-вывода.</li>
    <li>Даёт запрос Windows аккуратно изъять том из системы. Этот шаг аналогичен выполняемому пользователем извлечению устройства через область уведомлений в панели задач.</li>
    <li>Указывает диспетчеру монтирования Windows размонтировать том.</li>
    <li>Удаляет связь между буквой диска и виртуальным устройством тома.</li>
    <li>Удаляет виртуальное устройство тома и стирает ключи шифрования из ОЗУ.</li>
</ol>

<p>В этой последовательности действий шаги 1 и 2 могут завершиться ошибкой, если в томе есть открытые файлы. Имейте в виду, что даже если все пользовательские приложения, обращающиеся к файлам на томе, закрыты, Windows может по-прежнему держать файлы открытыми до тех пор, пока не будет полностью очищен кэш ввода-вывода.</p>

<h2>Принудительное размонтирование</h2>

<p>Процесс принудительного размонтирования хотя и отличается, но во многом он похож на обычное размонтирование. По сути, выполняются те же действия, но игнорируются любые сбои, которые могут возникнуть на шагах 1 и 2, после чего продолжается остальная часть процедуры. Однако если есть файлы, открытые пользователем, или ещё не очищен кэш ввода-вывода тома, это может привести к потенциальной потере данных. Эта ситуация аналогична принудительному удалению USB-устройства из компьютера, когда Windows всё ещё сообщает, что оно используется.</p>

<p>Если все приложения, использующие файлы на подключённом томе, были успешно закрыты, а кэш ввода-вывода полностью очищен, то при выполнении принудительного размонтирования не должно происходить ни потери данных, ни повреждения данных или файловой системы. Как и при обычном размонтировании, после успешного завершения принудительного размонтирования ключи шифрования стираются из ОЗУ.</p>

<h2>Как выполнить принудительное размонтирование</h2>

<p>В VeraCrypt есть три способа выполнить принудительное размонтирование:</p>

<ol>
    <li>Через всплывающее окно, которое появляется, если не удалась попытка обычного размонтирования.</li>
    <li>Через настройки программы, включив опцию <em>Принудительное авторазмонтирование даже при открытых файлах или папках</em> в группе параметров <em>Автоматическое размонтирование</em>.</li>
    <li>Через командную строку, указав ключ /force или /f вместе с ключом /u или /unmount.</li>
</ol>

<p>Во избежание непреднамеренной потери или повреждения данных всегда соблюдайте следующие меры предосторожности при размонтировании тома VeraCrypt:</p>
<ol>
    <li>Перед размонтированием убедитесь, что все файлы на томе закрыты.</li>
    <li>После закрытия всех файлов не спешите, дайте Windows некоторое время, чтобы полностью очистился кэш ввода-вывода.</li>
    <li>Учтите, что некоторые антивирусные программы после сканирования могут оставлять дескрипторы файлов в томе открытыми, препятствуя обычному размонтированию. Если возникает такая проблема, попробуйте исключить том VeraCrypt из сканирования антивирусным ПО. Кроме того, проконсультируйтесь с поставщиком вашего антивируса, чтобы понять, как его продукт взаимодействует с томами VeraCrypt и как убедиться, что он не удерживает открытыми дескрипторы файлов.</li>
</ol>


</div><div class="ClearBoth"></div></body></html>
