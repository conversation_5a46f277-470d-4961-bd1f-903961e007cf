﻿<!DOCTYPE html>
<html lang="ru">
<head>
<meta http-equiv="content-type" content="text/html; charset=utf-8" />
<title>VeraCrypt - Бесплатное надёжное шифрование дисков с открытым исходным кодом</title>
<meta name="description" content="VeraCrypt это бесплатное программное обеспечение для шифрования дисков с открытым исходным кодом для Windows, Mac OS X (macOS) и Linux. В случае, если злоумышленник вынуждает вас раскрыть пароль, VeraCrypt обеспечивает правдоподобное отрицание наличия шифрования. В отличие от пофайлового шифрования, VeraCrypt шифрует данные в реальном времени (на лету), автоматически, прозрачно, требует очень мало памяти и не использует временные незашифрованные файлы."/>
<meta name="keywords" content="encryption, security, шифрование, безопасность"/>
<link href="styles.css" rel="stylesheet" type="text/css" />
</head>
<body>

<div>
<a href="Documentation.html"><img src="VeraCrypt128x128.png" alt="VeraCrypt"/></a>
</div>

<div id="menu">
	<ul>
	  <li><a href="Home.html">Начало</a></li>
	  <li><a href="Code.html">Исходный код</a></li>
	  <li><a href="Downloads.html">Загрузить</a></li>
	  <li><a class="active" href="Documentation.html">Документация</a></li>
	  <li><a href="Donation.html">Поддержать разработку</a></li>
	  <li><a href="https://sourceforge.net/p/veracrypt/discussion/" target="_blank">Форум</a></li>
	</ul>
</div>

<div>
<p>
<a href="Documentation.html">Документация</a>
<img src="arrow_right.gif" alt=">>" style="margin-top: 5px">
<a href="Security%20Requirements%20and%20Precautions.html">Требования безопасности и меры предосторожности</a>
<img src="arrow_right.gif" alt=">>" style="margin-top: 5px">
<a href="Multi-User%20Environment.html">Многопользовательская среда</a>
</p></div>

<div class="wikidoc">
<div>
<h1>Многопользовательская среда</h1>
<p>Не забывайте, что содержимое смонтированного тома VeraCrypt видно (доступно) всем пользователям, вошедшим в систему.
Чтобы этого избежать, можно воспользоваться правами NTFS на файлы/папки, если только том не был смонтирован как сменный
носитель (см. раздел <a href="Removable%20Medium%20Volume.html">
<em>Том, смонтированный как сменный носитель</em></a>) в настольной редакции Windows Vista или более новых версий Windows
(сектора тома, смонтированного как сменный носитель, могут быть доступны на уровне томов пользователям без привилегий
администратора, вне зависимости от того, доступен ли он им на уровне файловой системы).<br>
<br>
Более того, в Windows всем вошедшим в систему пользователям доступен кэш паролей (см. подробности в разделе
<em>Настройки &gt; Параметры</em>, подраздел <em>Кэшировать пароли в памяти драйвера</em>).<br>
<br>
Обратите также внимание, что при переключении пользователей в Windows XP или более новой версии Windows
(функция <em>Быстрое переключение пользователей</em>) размонтирование успешно смонтированного тома VeraCrypt
<em>не</em> выполняется (в отличие от перезагрузки системы, при которой размонтируются все смонтированные тома VeraCrypt).<br>
<br>
В Windows 2000 права доступа к файлам-контейнерам игнорируются при монтировании тома VeraCrypt на основе файла.
Во всех поддерживаемых версиях Windows пользователи без привилегий администратора могут монтировать любой том VeraCrypt
на основе раздела/устройства (если указаны правильные пароль и/или ключевые файлы). Пользователь без привилегий
администратора может демонтировать только те тома, которые монтировал он сам. Это, однако, не относится к системным
избранным томам, если только вы не включили опцию (по умолчанию она выключена) 
<em>Настройки</em> &gt; <em>Системные избранные тома</em> &gt; <em>Просматривать/размонтировать системные избранные тома
могут лишь администраторы</em>.</p>
</div>
</div><div class="ClearBoth"></div></body></html>
