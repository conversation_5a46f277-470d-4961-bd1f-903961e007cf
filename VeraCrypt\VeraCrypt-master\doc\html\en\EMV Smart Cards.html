<!DOCTYPE html>
<html lang="en">
  <head>
    <meta http-equiv="content-type" content="text/html; charset=utf-8" />
    <title>
      VeraCrypt - Free Open source disk encryption with strong security for the
      Paranoid
    </title>
    <meta
      name="description"
      content="VeraCrypt is free open-source disk encryption software for Windows, Mac OS X and Linux. In case an attacker forces you to reveal the password, VeraCrypt provides plausible deniability. In contrast to file encryption, data encryption performed by VeraCrypt is real-time (on-the-fly), automatic, transparent, needs very little memory, and does not involve temporary unencrypted files."
    />
    <meta name="keywords" content="encryption, security" />
    <link href="styles.css" rel="stylesheet" type="text/css" />
  </head>
  <body>
    <div>
      <a href="Documentation.html"
        ><img src="VeraCrypt128x128.png" alt="VeraCrypt"
      /></a>
    </div>

    <div id="menu">
      <ul>
        <li><a href="Home.html">Home</a></li>
        <li><a href="Code.html">Source Code</a></li>
        <li><a href="Downloads.html">Downloads</a></li>
        <li><a class="active" href="Documentation.html">Documentation</a></li>
        <li><a href="Donation.html">Donate</a></li>
        <li>
          <a
            href="https://sourceforge.net/p/veracrypt/discussion/"
            target="_blank"
            >Forums</a
          >
        </li>
      </ul>
    </div>

    <div>
      <p>
        <a href="Documentation.html">Documentation</a>
        <img src="arrow_right.gif" alt=">>" style="margin-top: 5px" />
        <a href="EMV%20Smart%20Cards.html">EMV Smart Cards</a>
      </p>
    </div>

    <div class="wikidoc">
      <h1>EMV Smart Cards</h1>
      <div
        style="
          text-align: left;
          margin-top: 19px;
          margin-bottom: 19px;
          padding-top: 0px;
          padding-bottom: 0px;
        "
      >
        <p>
          Windows and Linux versions of VeraCrypt offer to use EMV compliant
          smart cards as a feature. Indeed, the use of PKCS#11 compliant smart
          cards is dedicated to users with more or less cybersecurity skills.
          However, in some situations, having such a card strongly reduces the
          plausible deniability of the user.
        </p>
        <p>
          To overcome this problem, the idea is to allow the use of a type of
          smart card owned by anyone: EMV compliant smart cards. According to
          the standard of the same name, these cards spread all over the world
          are used to carry out banking operations. Using internal data of the
          user's EMV card as keyfiles will strengthen the security of his volume
          while keeping his denial plausible.
        </p>
        <p>
          For more technical information, please see the section
          <em style="text-align: left">EMV Smart Cards</em> in the chapter
          <a
            href="Keyfiles%20in%20VeraCrypt.html"
            style="text-align: left; color: #0080c0; text-decoration: none"
          >
            <em style="text-align: left">Keyfiles</em></a
          >.
        </p>
      </div>
    </div>
  </body>
</html>
