﻿<!DOCTYPE html>
<html lang="ru">
<head>
<meta http-equiv="content-type" content="text/html; charset=utf-8" />
<title>VeraCrypt - Бесплатное надёжное шифрование дисков с открытым исходным кодом</title>
<meta name="description" content="VeraCrypt это бесплатное программное обеспечение для шифрования дисков с открытым исходным кодом для Windows, Mac OS X (macOS) и Linux. В случае, если злоумышленник вынуждает вас раскрыть пароль, VeraCrypt обеспечивает правдоподобное отрицание наличия шифрования. В отличие от пофайлового шифрования, VeraCrypt шифрует данные в реальном времени (на лету), автоматически, прозрачно, требует очень мало памяти и не использует временные незашифрованные файлы."/>
<meta name="keywords" content="encryption, security, шифрование, безопасность"/>
<link href="styles.css" rel="stylesheet" type="text/css" />
</head>
<body>

<div>
<a href="Documentation.html"><img src="VeraCrypt128x128.png" alt="VeraCrypt"/></a>
</div>

<div id="menu">
	<ul>
	  <li><a href="Home.html">Начало</a></li>
	  <li><a href="Code.html">Исходный код</a></li>
	  <li><a href="Downloads.html">Загрузить</a></li>
	  <li><a class="active" href="Documentation.html">Документация</a></li>
	  <li><a href="Donation.html">Поддержать разработку</a></li>
	  <li><a href="https://sourceforge.net/p/veracrypt/discussion/" target="_blank">Форум</a></li>
	</ul>
</div>

<div>
<p>
<a href="Documentation.html">Документация</a>
<img src="arrow_right.gif" alt=">>" style="margin-top: 5px">
<a href="Security%20Requirements%20and%20Precautions.html">Требования безопасности и меры предосторожности</a>
<img src="arrow_right.gif" alt=">>" style="margin-top: 5px">
<a href="Choosing%20Passwords%20and%20Keyfiles.html">Выбор паролей и ключевых файлов</a>
</p></div>

<div class="wikidoc">
<div>
<h1>Выбор паролей и ключевых файлов</h1>
<p>Выбрать хороший пароль – это очень важно! Ни в коем случае не используйте пароль из одного слова, которое
можно найти в каком-нибудь словаре (или сочетание из таких слов). В пароле не должно быть никаких имён, дней рождения,
номеров телефонов и учётных записей (аккаунтов) и любых других элементов, которые можно угадать. Хороший пароль
это случайная комбинация из букв в верхнем и нижнем регистрах, цифр и специальных символов, таких как @ ^ = $ * &#43;
и др. Настоятельно рекомендуется выбирать пароль, состоящий не менее чем из 20 символов (чем длиннее, тем лучше),
так как короткие пароли несложно взломать методом перебора (brute-force).<br>
<br>
Чтобы сделать атаки перебором невозможными, размер ключевого файла должен быть не менее 30 байт. Если для тома
используется несколько ключевых файлов, хотя бы один из них должен иметь размер 30 байт или больше. Обратите
внимание, что 30-байтовое ограничение предполагает большой объём энтропии в ключевом файле. Если первые 1024 килобайта
файла содержат лишь небольшой объём энтропии, такой файл нельзя использовать как ключевой (вне зависимости от
размера файла). Если вы не понимаете, что такое энтропия, рекомендуем доверить VeraCrypt создание файла со
случайным содержимым и использовать этот файл как ключевой (выберите <em>Сервис &gt; Генератор ключевых файлов</em>).</p>
<p>При создании тома, шифровании системного раздела/диска или изменении паролей/ключевых файлов нельзя позволять
никому другому выбирать или изменять пароли/ключевые файлы до тех пор, пока не будет создан том или изменены
пароль/ключевые файлы. Например, нельзя использовать никакие генераторы паролей (будь то приложения в Интернете
или программы у вас в компьютере), если вы не уверены в их высоком качестве и в том, что они не подконтрольны
неприятелю, а в качестве ключевых файлов нельзя использовать файлы, загруженные из Интернета, или которые
доступны другим пользователям данного компьютера (неважно, администраторы они или нет).</p>
</div>
</div><div class="ClearBoth"></div></body></html>
