﻿<!DOCTYPE html>
<html lang="ru">
<head>
<meta http-equiv="content-type" content="text/html; charset=utf-8" />
<title>VeraCrypt - Бесплатное надёжное шифрование дисков с открытым исходным кодом</title>
<meta name="description" content="VeraCrypt это бесплатное программное обеспечение для шифрования дисков с открытым исходным кодом для Windows, Mac OS X (macOS) и Linux. В случае, если злоумышленник вынуждает вас раскрыть пароль, VeraCrypt обеспечивает правдоподобное отрицание наличия шифрования. В отличие от пофайлового шифрования, VeraCrypt шифрует данные в реальном времени (на лету), автоматически, прозрачно, требует очень мало памяти и не использует временные незашифрованные файлы."/>
<meta name="keywords" content="encryption, security, шифрование, безопасность"/>
<link href="styles.css" rel="stylesheet" type="text/css" />
</head>
<body>

<div>
<a href="Documentation.html"><img src="VeraCrypt128x128.png" alt="VeraCrypt"/></a>
</div>

<div id="menu">
	<ul>
	  <li><a href="Home.html">Начало</a></li>
	  <li><a href="Code.html">Исходный код</a></li>
	  <li><a href="Downloads.html">Загрузить</a></li>
	  <li><a class="active" href="Documentation.html">Документация</a></li>
	  <li><a href="Donation.html">Поддержать разработку</a></li>
	  <li><a href="https://sourceforge.net/p/veracrypt/discussion/" target="_blank">Форум</a></li>
	</ul>
</div>

<div>
<p>
<a href="Documentation.html">Документация</a>
<img src="arrow_right.gif" alt=">>" style="margin-top: 5px">
<a href="Plausible%20Deniability.html">Правдоподобное отрицание наличия шифрования</a>
<img src="arrow_right.gif" alt=">>" style="margin-top: 5px">
<a href="VeraCrypt%20Hidden%20Operating%20System.html">Скрытая операционная система</a>
</p></div>

<div class="wikidoc">
<h1>Скрытая операционная система</h1>
<div style="text-align:left; margin-top:19px; margin-bottom:19px; padding-top:0px; padding-bottom:0px">
Если системный раздел или системный диск зашифрован с помощью VeraCrypt, то при каждом включении или при
каждой перезагрузке компьютера требуется вводить
<a href="System%20Encryption.html" style="text-align:left; color:#0080c0; text-decoration:none">
предзагрузочной аутентификации</a> на экране загрузчика VeraCrypt. Может случиться, что кто-то вынудит
вас расшифровать операционную систему или сообщить пароль от предзагрузочной аутентификации. Во многих
ситуациях вы просто не сможете отказаться это сделать (например, при вымогательстве). VeraCrypt позволяет
создать скрытую операционную систему, существование которой невозможно доказать (при условии выполнения
некоторых рекомендаций &mdash; см. ниже). Таким образом, вам не придётся расшифровывать скрытую операционную
систему или сообщать от неё пароль.</div>
<div style="text-align:left; margin-top:19px; margin-bottom:19px; padding-top:0px; padding-bottom:0px">
Прежде чем продолжить чтение, вам следует ознакомиться с разделом <a href="Hidden%20Volume.html" style="text-align:left; color:#0080c0; text-decoration:none">
<strong style="text-align:left">Скрытый том</strong></a> и убедиться, что вы понимаете, что такое
<a href="Hidden%20Volume.html" style="text-align:left; color:#0080c0; text-decoration:none">
скрытый том VeraCrypt</a>.</div>
<div style="text-align:left; margin-top:19px; margin-bottom:19px; padding-top:0px; padding-bottom:0px">
<strong style="text-align:left">Скрытая операционная система</strong> это система (например, Windows 7 или
Windows XP), установленная в
<a href="Hidden%20Volume.html" style="text-align:left; color:#0080c0; text-decoration:none">
скрытом томе VeraCrypt</a>. Доказать, что <a href="Hidden%20Volume.html" style="text-align:left; color:#0080c0; text-decoration:none">
скрытый том VeraCrypt</a> существует, должно быть невозможно (при соблюдении определённых правил; см. подробности в разделе
<a href="Hidden%20Volume.html" style="text-align:left; color:#0080c0; text-decoration:none">
Скрытый том</a>), и, следовательно, должно быть невозможно доказать, что существует скрытая операционная система.</div>
<div style="text-align:left; margin-top:19px; margin-bottom:19px; padding-top:0px; padding-bottom:0px">
Однако для загрузки системы, зашифрованной VeraCrypt, необходимо, чтобы на системном диске или на
<a href="VeraCrypt%20Rescue%20Disk.html" style="text-align:left; color:#0080c0; text-decoration:none">
Диске восстановления VeraCrypt</a> (Rescue Disk) находилась незашифрованная копия
<a href="System%20Encryption.html" style="text-align:left; color:#0080c0; text-decoration:none">
загрузчика VeraCrypt</a>. Очевидно, что одно только присутствие загрузчика VeraCrypt говорит о том, что
в компьютере имеется система, зашифрованная с помощью VeraCrypt. Таким образом, чтобы можно было правдоподобно
объяснить наличие загрузчика VeraCrypt, мастер VeraCrypt в процессе создания скрытой операционной системы
поможет вам создать вторую зашифрованную операционную систему – так называемую
<strong style="text-align:left">обманную ОС</strong>. Обманная операционная система не должна содержать
никаких конфиденциальных файлов. Её наличие не составляет секрета (она
<em style="text-align:left">не</em> установлена в <a href="Hidden%20Volume.html" style="text-align:left; color:#0080c0; text-decoration:none">
скрытом томе</a>). Пароль от обманной операционной системы можно без опасений сообщить любому, кто станет
вынуждать вас выдать пароль от предзагрузочной аутентификации.*</div>
<div style="text-align:left; margin-top:19px; margin-bottom:19px; padding-top:0px; padding-bottom:0px">
Загружать обманную операционную систему следует так же часто, как вы используете свой компьютер. В идеале
вы должны использовать её для всех действий, которые не связаны с конфиденциальными данными. В противном случае
может пострадать правдоподобность отрицания наличия скрытой операционной системы (если вы сообщили неприятелю
пароль от обманной операционной системы, он сможет выяснить, что эта система использовалась не слишком часто,
а это может навести на мысль о существовании в компьютере скрытой операционной системы). Обратите внимание,
что вы можете свободно сохранять данные в разделе с обманной ОС без риска повреждения скрытого тома (так как обманная ОС
<em style="text-align:left">не</em> установлена во внешнем томе – см. ниже).</div>
<div style="text-align:left; margin-top:19px; margin-bottom:19px; padding-top:0px; padding-bottom:0px">
У вас будет два пароля предзагрузочной аутентификации – один для скрытой системы, а другой для обманной.
Если вы хотите загрузить скрытую систему, то просто вводите пароль для скрытой системы на экране загрузчика
(который появляется при включении или перезагрузке компьютера). Аналогично, если вам нужно загрузить обманную
операционную систему (например, когда вас вынуждает это сделать неприятель), то на экране загрузчика VeraCrypt
вы вводите пароль от обманной системы.</div>
<div style="text-align:left; margin-top:19px; margin-bottom:19px; padding-top:0px; padding-bottom:0px">
Примечание. Когда вы вводите пароль предзагрузочной аутентификации, загрузчик VeraCrypt Boot сначала пытается
расшифровать (с помощью указанного пароля) последние 512 байт первой логической дорожки системного диска
(где обычно хранятся зашифрованные данные мастер-ключа для нескрытых зашифрованных системных разделов/дисков).
Если это сделать не удаётся и если имеется раздел, следующий за активным разделом, загрузчик В случае неудачи
и если после активного раздела находится ещё один раздел, загрузчик VeraCrypt (даже если на самом деле на диске
нет скрытого тома) автоматически пытается расшифровать (снова используя тот же введённый пароль) область первого
раздела, идущего за активным разделом, где может храниться зашифрованный заголовок возможного скрытого тома
(однако если размер активного раздела меньше 256 МБ, то данные считываются со <i>второго</i> раздела после активного,
поскольку Windows 7 и новее по умолчанию не загружаются с раздела, на котором они установлены).
Обратите внимание, что VeraCrypt никогда заранее не знает, существует ли скрытый том (заголовок скрытого тома
не может быть идентифицирован, так как он выглядит как состоящий полностью из случайных данных). Если заголовок
успешно расшифрован (о том, как VeraCrypt определяет, что он был успешно расшифрован, см. в разделе
<a href="Encryption%20Scheme.html" style="text-align:left; color:#0080c0; text-decoration:none">
Схема шифрования</a>), из расшифрованного заголовка (который всё ещё хранится в ОЗУ) извлекается информация
о размере скрытого тома и монтируется скрытый том (его размер также определяет его смещение). Технические детали
см. в разделе <a href="Encryption%20Scheme.html" style="text-align:left; color:#0080c0; text-decoration:none">
Схема шифрования</a>, глава <a href="Technical%20Details.html" style="text-align:left; color:#0080c0; text-decoration:none">
Технические подробности</a>.</div>
<div style="text-align:left; margin-top:19px; margin-bottom:19px; padding-top:0px; padding-bottom:0px">
При работе скрытая операционная система выглядит так, как будто она установлена в том же разделе, что и исходная ОС
(обманная система). Однако на самом деле она установлена в разделе позади него (в скрытом томе). Все операции
чтения/записи прозрачно перенаправляются из системного раздела в скрытый том. Ни операционная система,
ни приложения не будут знать, что данные, записываемые и считываемые из системного раздела, на самом деле
записываются и считываются из раздела, находящегося за ним (из/на скрытый том). Любые такие данные шифруются
и расшифровываются на лету, как обычно (с ключом шифрования, отличным от того, который используется для обманной
операционной системы).</div>
<div style="text-align:left; margin-top:19px; margin-bottom:19px; padding-top:0px; padding-bottom:0px">
Обратите внимание, что будет также третий пароль – для <a href="Hidden%20Volume.html" style="text-align:left; color:#0080c0; text-decoration:none">
<strong style="text-align:left">внешнего тома</strong></a>. Это не пароль предзагрузочной аутентификации, а
обычный пароль тома VeraCrypt. Его можно без опаски сообщать любому, кто станет вынуждать вас выдать пароль
от зашифрованного раздела, где находится скрытый том (содержащий скрытую операционную систему). Таким образом,
существование скрытого тома (и скрытой операционной системы) останется тайной. Если вы не вполне понимаете,
как это возможно, или что такое внешний том, прочтите раздел <a href="Hidden%20Volume.html" style="text-align:left; color:#0080c0; text-decoration:none">
Скрытый том</a>. Внешний том должен содержать некоторое количество файлов, похожих на конфиденциальные,
которые на самом деле вам скрывать <em style="text-align:left">не</em> нужно.</div>
<div style="text-align:left; margin-top:19px; margin-bottom:19px; padding-top:0px; padding-bottom:0px">
Итак, всего будет три пароля. Два из них можно сообщать неприятелю (для обманной системы и для внешнего тома).
Третий пароль, для скрытой системы, должен оставаться в тайне.</div>
<div style="text-align:left; margin-top:19px; margin-bottom:19px; padding-top:0px; padding-bottom:0px">
<img src="Beginner's Tutorial_Image_034.png" alt="Example Layout of System Drive Containing Скрытая операционная система"></div>
<div style="text-align:left; margin-top:19px; margin-bottom:19px; padding-top:0px; padding-bottom:0px">
<em style="text-align:left">Пример макета системного диска, содержащего скрытую операционную систему</em></div>
<p>&nbsp;</p>
<h4 id="CreationProcess" style="text-align:left; font-family:Arial,Helvetica,Verdana,sans-serif; font-weight:bold; margin-top:0px; font-size:12px; margin-bottom:1px">
Создание скрытой операционной системы</h4>
<div style="text-align:left; margin-top:19px; margin-bottom:19px; padding-top:0px; padding-bottom:0px">
Чтобы приступить к созданию скрытой операционной системы, выберите <em style="text-align:left">
Система</em> &gt; <em style="text-align:left">Создать скрытую ОС</em> и следуйте инструкциям мастера.</div>
<div style="text-align:left; margin-top:19px; margin-bottom:19px; padding-top:0px; padding-bottom:0px">
Сначала мастер проверяет наличие на системном диске раздела, подходящего для скрытой операционной системы.
Обратите внимание, что прежде чем вы сможете создать скрытую операционную систему, необходимо создать
для неё раздел на системном диске. Это должен быть первый раздел, расположенный после системного раздела,
и он должен быть как минимум на 5% больше, чем системный раздел (системный раздел – это тот, на котором
установлена ​​работающая в данный момент операционная система).
Однако если внешний том (не путать с системным разделом) отформатирован как NTFS, раздел для скрытой операционной
системы должен быть, по крайней мере, на 110% (в 2,1 раза) больше, чем системный раздел (причина в том, что
файловая система NTFS всегда сохраняет внутренние данные точно в центре тома, и потому скрытый том, который
должен содержать клон системного раздела, может находиться только во второй половине раздела).</div>
<div style="text-align:left; margin-top:19px; margin-bottom:19px; padding-top:0px; padding-bottom:0px">
На следующих этапах мастер создаст два тома VeraCrypt (<a href="Hidden%20Volume.html" style="text-align:left; color:#0080c0; text-decoration:none">внешний и скрытый</a>)
внутри раздела, следующего первым за системным разделом. <a href="Hidden%20Volume.html" style="text-align:left; color:#0080c0; text-decoration:none">
Скрытый том</a> будет содержать скрытую операционную систему. Размер скрытого тома всегда равен размеру
системного раздела. Причина в том, что в скрытом томе должен находиться клон содержимого системного раздела
(см. ниже). Обратите внимание, что клон будет зашифрован с использованием ключа шифрования, отличного от
исходного. Прежде чем вы начнёте копировать во внешний том файлы, напоминающие конфиденциальные, мастер
сообщит максимально рекомендуемый размер дискового пространства, который могут занимать такие файлы, чтобы
во внешнем томе оставалось достаточно места для скрытого тома.</div>
<div style="text-align:left; margin-top:19px; margin-bottom:19px; padding-top:0px; padding-bottom:0px">
Замечание. После того, как вы скопируете на внешний том некоторое количество файлов, похожих на конфиденциальные,
будет просканирована карта кластеров тома, чтобы определить размер непрерывной свободной области, конец
которой выровнен по концу внешнего тома. Эта область будет отведена под скрытый том, поэтому она ограничивает
его максимально возможный размер. При определении максимально возможного размера скрытого тома будет проверено,
что он больше, чем системный раздел (что необходимо, так как в скрытый том нужно будет скопировать всё
содержимое системного раздела – см. ниже). Это гарантирует, что никакие данные, хранящиеся на внешнем томе,
не будут перезаписаны данными, записанными в область скрытого тома (например, при копировании в него системы).
Размер скрытого тома всегда равен размеру системного раздела.</div>
<div style="text-align:left; margin-top:19px; margin-bottom:19px; padding-top:0px; padding-bottom:0px">
Затем VeraCrypt создаст скрытую операционную систему, скопировав содержимое системного раздела в скрытый том.
Копируемые данные будут зашифрованы на лету ключом шифрования, отличным от того, который будет использоваться
для обманной операционной системы. Процесс копирования системы выполняется в предзагрузочной среде (до запуска
Windows) и может занять длительное время – несколько часов или даже дней (в зависимости от размера системного
раздела и производительности компьютера). Этот процесс можно будет прервать, выключить компьютер, загрузить
операционную систему, а затем снова продолжить. Однако если его прервать, весь процесс копирования системы
придётся начинать сначала (потому что содержимое системного раздела не должно меняться во время клонирования).
Скрытая операционная система изначально будет клоном той операционной системы, в которой вы запускали мастер.</div>
<div id="SecureEraseOS" style="text-align:left; margin-top:19px; margin-bottom:19px; padding-top:0px; padding-bottom:0px">
Windows создаёт (как правило, без вашего ведома или согласия) на системном разделе множество файлов-журналов,
временных файлов и т. п. Кроме того, в файлах гибернации (сна) и подкачки, также находящихся в системном разделе,
сохраняется содержимое ОЗУ. Поэтому если неприятель проанализирует файлы в разделе, где находится исходная система
(клоном которой является скрытая система), он сможет определить, что, например, вы использовали мастер VeraCrypt
в режиме создания скрытой системы (что может навести на мысль о существовании в вашем компьютере скрытой ОС).
Чтобы избежать подобных проблем, VeraCrypt после создания скрытой системы надёжно удаляет (затирает) всё содержимое
раздела, в котором находится исходная система. Затем, чтобы обеспечить возможность правдоподобного отрицания
наличия шифрования, VeraCrypt предложит установить новую систему на раздел и зашифровать её. Таким образом, вы
создадите обманную систему, и на этом весь процесс создания скрытой операционной системы будет завершён.</div>
<div style="text-align:left; margin-top:19px; margin-bottom:19px; padding-top:0px; padding-bottom:0px">
Примечание. VeraCrypt удалит содержимое раздела, в котором находится исходная система, полностью заполнив его
случайными данными. Если бы вы раскрыли пароль от обманной системы противнику и он спросил вас, почему свободное
место системного раздела (обманного) содержит случайные данные, вы могли бы ответить, например, так:
&quot;Раньше этот раздел содержал систему, зашифрованную VeraCrypt, но я забыл пароль предзагрузочной аутентификации
(или система была повреждена и перестала загружаться), поэтому мне пришлось переустановить Windows и снова зашифровать раздел&quot;.</div>
<h4 id="data_leak_protection" style="text-align:left; font-family:Arial,Helvetica,Verdana,sans-serif; font-weight:bold; margin-top:0px; font-size:12px; margin-bottom:1px">
Правдоподобное отрицание наличия шифрования и защита от утечек данных</h4>
<div style="text-align:left; margin-top:19px; margin-bottom:19px; padding-top:0px; padding-bottom:0px">
По соображениям безопасности, когда работает скрытая операционная система, VeraCrypt гарантирует, что все
локальные незашифрованные файловые системы и нескрытые тома VeraCrypt доступны только для чтения (то есть никакие
файлы не могут быть записаны в такие файловые системы или тома VeraCrypt).&dagger; Запись данных разрешена в
любую файловую систему, находящуюся внутри <a href="Hidden%20Volume.html" style="text-align:left; color:#0080c0; text-decoration:none">
скрытого тома VeraCrypt</a> (при условии, что этот скрытый том расположен не в контейнере, хранящемся в
незашифрованной файловой системе или в любой другой файловой системе, доступной только для чтения).</div>
<div style="text-align:left; margin-top:19px; margin-bottom:19px; padding-top:0px; padding-bottom:0px">
Существуют три основные причины, по которым были приняты такие контрмеры:</div>
<ol style="text-align:left; margin-top:18px; margin-bottom:19px; padding-top:0px; padding-bottom:0px">
<li style="text-align:left; margin-top:0px; margin-bottom:0px; padding-top:0px; padding-bottom:0px">
Возможность создать безопасную платформу для монтирования скрытых томов VeraCrypt. Обратите внимание, что мы официально рекомендуем монтировать скрытые тома только при работающей скрытой операционной системе. См. подробности в подразделе
<a href="Security%20Requirements%20for%20Hidden%20Volumes.html" style="text-align:left; color:#0080c0; text-decoration:none">
Требования безопасности и меры предосторожности, относящиеся к скрытым томам</a>. </li><li style="text-align:left; margin-top:0px; margin-bottom:0px; padding-top:0px; padding-bottom:0px">
В некоторых случаях можно определить, что в какое-то время определённая файловая система не была смонтирована
(или что какой-то файл в файловой системе не был сохранён или не был доступен изнутри) в определённом экземпляре
операционной системы (например, с помощью анализа и сравнения журналов файловой системы, временных меток файлов,
журналов приложений, журналов ошибок и т. д.). Это может указывать на то, что в компьютере установлена ​​скрытая
операционная система. Подобные проблемы предотвращаются контрмерами.
</li><li style="text-align:left; margin-top:0px; margin-bottom:0px; padding-top:0px; padding-bottom:0px">
Предотвращение повреждения данных и возможность безопасной гибернации ("сна"). Когда Windows выходит из гибернации,
она подразумевает, что все смонтированные файловые системы находятся в том же состоянии, в котором они были
на момент входа в гибернацию. VeraCrypt обеспечивает это, защищая от записи любую файловую систему, доступную как
из обманных, так и из скрытых систем. Без такой защиты файловая система может быть повреждена при монтировании
одной системой, когда другая находится в состоянии гибернации.
</li></ol>
<p>Если вам нужно безопасно перенести файлы из обманной системы в скрытую, выполните следующее:</p>
<ol style="text-align:left; margin-top:18px; margin-bottom:19px; padding-top:0px; padding-bottom:0px">
<li style="text-align:left; margin-top:0px; margin-bottom:0px; padding-top:0px; padding-bottom:0px">
Загрузите обманную систему.</li><li style="text-align:left; margin-top:0px; margin-bottom:0px; padding-top:0px; padding-bottom:0px">
Сохраните файлы в незашифрованном томе или во внешнем/обычном томе VeraCrypt. </li><li style="text-align:left; margin-top:0px; margin-bottom:0px; padding-top:0px; padding-bottom:0px">
Загрузите скрытую систему.</li><li style="text-align:left; margin-top:0px; margin-bottom:0px; padding-top:0px; padding-bottom:0px">
Если вы сохранили файлы в том VeraCrypt, смонтируйте его (он будет автоматически смонтирован как доступный только для чтения).
</li><li style="text-align:left; margin-top:0px; margin-bottom:0px; padding-top:0px; padding-bottom:0px">
Скопируйте файлы в скрытый системный раздел или в другой скрытый том.</li></ol>
<p>&nbsp;</p>
<h4 style="text-align:left; font-family:Arial,Helvetica,Verdana,sans-serif; font-weight:bold; margin-top:0px; font-size:12px; margin-bottom:1px">
Возможные объяснения существования двух разделов VeraCrypt на одном диске</h4>
<div style="text-align:left; margin-top:19px; margin-bottom:19px; padding-top:0px; padding-bottom:0px">
Неприятель может поинтересоваться, зачем вам понадобилось создавать на одном диске два зашифрованных VeraCrypt
раздела (системный и несистемный), когда можно было бы вместо этого зашифровать весь диск с помощью одного
ключа шифрования. На то может быть множество причин. Однако если вам не приходит в голову никакая (кроме
создания скрытой операционной системы), вы можете воспользоваться, например, одним из следующих объяснений:</div>
<ul style="text-align:left; margin-top:18px; margin-bottom:19px; padding-top:0px; padding-bottom:0px">
<li style="text-align:left; margin-top:0px; margin-bottom:0px; padding-top:0px; padding-bottom:0px">
Если на системном диске более двух разделов, и вам нужно зашифровать только два из них (системный раздел и раздел,
следующий за ним), оставив другие разделы незашифрованными (например, чтобы обеспечить на этих незашифрованных
разделах максимально возможную скорость чтения и записи данных, не нуждающихся в шифровании), единственный способ
это сделать – зашифровать два раздела по-отдельности (обратите внимание, что с помощью одного ключа шифрования
VeraCrypt может зашифровать весь системный диск и <em style="text-align:left">все</em> находящиеся на нём разделы,
но не может зашифровать только два из них – с помощью одного ключа можно зашифровать либо один, либо все разделы).
В результате на системном диске будут два расположенных рядом раздела VeraCrypt (первый – системный, второй – несистемный),
каждый зашифрованный своим собственным ключом (что также имеет место при создании скрытой операционной системы,
и потому тоже может быть объяснено таким же образом).<br style="text-align:left">
<br style="text-align:left">
Если вы не знаете ни одной веской причины, почему на системном диске может быть более одного раздела, примите к сведению следующее:<br style="text-align:left">
<br style="text-align:left">
Как правило, несистемные файлы (документы) рекомендуется хранить отдельно от системных файлов. Один из наиболее
простых и надёжных способов этого добиться – создать два раздела на системном диске: один раздел для операционной
системы, а другой для документов (несистемных файлов). Такая практика рекомендуется по следующим причинам:
<ul style="text-align:left; margin-top:18px; margin-bottom:19px; padding-top:0px; padding-bottom:0px">
<li style="text-align:left; margin-top:0px; margin-bottom:0px; padding-top:0px; padding-bottom:0px">
Если повредится файловая система одного из разделов, файлы на этом разделе могут испортиться или стать недоступными,
в то время как файлов на другом разделе это не коснётся.
</li><li style="text-align:left; margin-top:0px; margin-bottom:0px; padding-top:0px; padding-bottom:0px">
Так проще выполнить переустановку системы без потери документов (полная повторная установка ОС включает в себя
форматирование системного раздела, что приводит к уничтожению всех хранящихся на нём файлов). При повреждении
системы полная её переустановка это, зачастую, единственно возможный путь.</li></ul>
</li><li style="text-align:left; margin-top:0px; margin-bottom:0px; padding-top:0px; padding-bottom:0px">
<a href="Cascades.html" style="text-align:left; color:#0080c0; text-decoration:none">
Каскадное шифрование</a> (например, AES-Twofish-Serpent) может быть во много раз медленнее, чем шифрование без
каскадирования (например, <a href="AES.html" style="text-align:left; color:#0080c0; text-decoration:none">
AES</a>). Однако каскадное шифрование более надёжно, чем некаскадное (например, вероятность взлома трёх разных
алгоритмов шифрования, скажем, вследствие развития криптоанализа, значительно ниже, чем только одного из них).
Поэтому если вы зашифруете внешний том с применением каскадного шифрования, а обманную систему с помощью некаскадного,
то сможете ответить, что вы хотели добиться максимальной производительности (и достаточной защиты) для системного
раздела, а для несистемного раздела (то есть для внешнего тома), где у вас хранятся самые конфиденциальные данные
и куда вы обращаетесь не слишком часто (в отличие от операционной системы, которая используется очень часто и
потому нуждается в наиболее высокой скорости), вам была нужна максимальная защита (пусть и ценой потери
производительности). На системном разделе вы храните менее секретные данные (но которые вам нужны очень часто),
чем данные на несистемном разделе (то есть во внешнем томе).</li><li style="text-align:left; margin-top:0px; margin-bottom:0px; padding-top:0px; padding-bottom:0px">
При условии, что внешний том у вас зашифрован с помощью каскадного шифрования (например, AES-Twofish-Serpent),
а обманная система – некаскадным алгоритмом (скажем, AES), вы также можете ответить, что хотели избежать проблем,
о которых предупреждает VeraCrypt, когда пользователь пытается выбрать каскадный алгоритм для шифрования системы
(список проблем приведён ниже). Поэтому, чтобы не осложнять себе жизнь такими проблемами, вы решили зашифровать
системный раздел с помощью некаскадного алгоритма. Вместе с тем, для своих самых конфиденциальных данных вы
по-прежнему захотели воспользоваться каскадным шифрованием (как более надёжным, чем некаскадный алгоритм),
и потому решили создать второй раздел, которого эти проблемы <em style="text-align:left">не</em> касаются
(поскольку он не системный), и зашифровать его каскадным алгоритмом. В системном разделе вы храните менее важные
данные, чем те, которые хранятся в несистемном разделе (то есть во внешнем томе).
<br style="text-align:left">
<br style="text-align:left">
Примечание. Если пользователь пытается зашифровать системный раздел каскадным алгоритмом, VeraCrypt предупреждает,
что это может повлечь за собой следующие проблемы (и неявно рекомендует вместо этого выбрать некаскадный алгоритм шифрования):
<ul style="text-align:left; margin-top:18px; margin-bottom:19px; padding-top:0px; padding-bottom:0px">
<li style="text-align:left; margin-top:0px; margin-bottom:0px; padding-top:0px; padding-bottom:0px; font-size:10px; line-height:12px">
При использовании каскадных алгоритмов шифрования размер загрузчика VeraCrypt больше, чем обычно, поэтому на первой
дорожке диска недостаточно места для его резервной копии. Следовательно, при
<em style="text-align:left">любом</em> повреждении загрузчика (что часто случается, например, из-за неудачно
реализованных антипиратских процедур активации некоторых программ), пользователю нужно прибегать к помощи диска
восстановления VeraCrypt, чтобы восстановить загрузчик VeraCrypt или загрузить систему.</li><li style="text-align:left; margin-top:0px; margin-bottom:0px; padding-top:0px; padding-bottom:0px; font-size:10px; line-height:12px">
На некоторых компьютерах выход из состояния гибернации ("сна") занимает больше времени. </li></ul>
</li><li style="text-align:left; margin-top:0px; margin-bottom:0px; padding-top:0px; padding-bottom:0px">
В отличие от пароля для несистемного тома VeraCrypt, пароль предзагрузочной аутентификации требуется вводить при
каждом включении или перезагрузке компьютера. Поэтому если пароль предзагрузочной аутентификации длинный (что
необходимо в целях безопасности), вводить его так часто может быть очень утомительно. Следовательно, вы можете
ответить, что вам было удобнее вводить короткий (и потому менее надёжный) пароль для системного раздела (то есть для
обманной системы), а более секретные документы (доступ к которым нужен не так часто) вы предпочли хранить в
несистемном разделе VeraCrypt (то есть во внешнем томе), для которого выбрали очень длинный пароль.
<br style="text-align:left">
<br style="text-align:left">
Поскольку пароль для системного раздела не слишком надёжный (потому что он короткий), вы намеренно не храните
важные конфиденциальные данные в системном разделе. Тем не менее, вы предпочитаете, чтобы системный раздел был
зашифрован, так как храните на нём потенциально важные и умеренно конфиденциальные данные, с которыми работаете
ежедневно (например, автоматически запоминаемые браузером пароли от интернет-форумов, историю посещаемых сайтов,
запускаемых приложений и т. п.).
</li><li style="text-align:left; margin-top:0px; margin-bottom:0px; padding-top:0px; padding-bottom:0px">
Если неприятель завладеет вашим компьютером в тот момент, когда смонтирован том VeraCrypt (например, когда вы
пользуетесь ноутбуком на улице), он в большинстве случаев сможет прочитать любые хранящиеся в томе данные (данные
расшифровываются на лету при их считывании). Поэтому имеет смысл ограничить до минимума время, в течение которого
том остаётся смонтированным. Очевидно, что это сделать невозможно или затруднительно, если конфиденциальные данные
хранятся в зашифрованном системном разделе или на полностью зашифрованном системном диске (потому что при этом вам
пришлось бы ограничить до минимума и время работы с компьютером). Следовательно, вы можете ответить, что для
хранения особо важных данных вы создали отдельный раздел (зашифрованный другим ключом, нежели системный раздел),
монтируете его только при необходимости, а затем как можно скорее размонтируете (поскольку время монтирования этого
тома ограничено до минимума). В системном разделе вы храните данные менее важные (но которые вам часто нужны), чем
в несистемном разделе (то есть во внешнем томе).
</li></ul>
<div style="text-align:left; margin-top:19px; margin-bottom:19px; padding-top:0px; padding-bottom:0px">
&nbsp;</div>
<h4 style="text-align:left; font-family:Arial,Helvetica,Verdana,sans-serif; font-weight:bold; margin-top:0px; font-size:12px; margin-bottom:1px">
Меры предосторожности и требования безопасности, относящиеся к скрытым операционным системам</h4>
<div style="text-align:left; margin-top:19px; margin-bottom:19px; padding-top:0px; padding-bottom:0px">
Поскольку скрытая операционная система расположена в скрытом томе VeraCrypt, пользователь скрытой ОС должен
соблюдать все правила и меры предосторожности, относящиеся к обычным скрытым томам VeraCrypt. Эти требования,
а также дополнительные меры предосторожности, относящиеся именно к скрытым операционным системам, приведены в подразделе
<a href="Security%20Requirements%20for%20Hidden%20Volumes.html" style="text-align:left; color:#0080c0; text-decoration:none">
Требования безопасности и меры предосторожности, относящиеся к скрытым томам</a>.</div>
<div style="text-align:left; margin-top:19px; margin-bottom:19px; padding-top:0px; padding-bottom:0px">
ВНИМАНИЕ: Если вы не защищаете скрытый том (о том, как это сделать, см. раздел
<a href="Protection%20of%20Hidden%20Volumes.html" style="text-align:left; color:#0080c0; text-decoration:none">
Защита скрытых томов от повреждения</a>), <em style="text-align:left">не</em> записывайте ничего во внешний том
(обратите внимание, что обманная операционная система установлена <em style="text-align:left">
не</em> во внешнем томе). Иначе вы можете перезаписать и повредить скрытый том (и находящуюся внутри него скрытую ОС)!</div>
<div style="text-align:left; margin-top:19px; margin-bottom:19px; padding-top:0px; padding-bottom:0px">
Если выполнены все инструкции мастера и соблюдены меры предосторожности, указанные в подразделе
<a href="Security%20Requirements%20for%20Hidden%20Volumes.html" style="text-align:left; color:#0080c0; text-decoration:none">
Требования безопасности и меры предосторожности, относящиеся к скрытым томам</a>, гарантированно утверждать,
что в ПК имеются скрытый том и скрытая операционная система, должно быть невозможно, даже если смонтирован
внешний том или расшифрована/запущена обманная ОС.</div>
<p>&nbsp;</p>
<hr align="left" size="1" width="189" style="text-align:left; height:0px; border-width:0px 1px 1px; border-style:solid; border-color:#000000">
<p><span style="text-align:left; font-size:10px; line-height:12px">* Нецелесообразно (и поэтому не поддерживается)
устанавливать операционные системы в два тома VeraCrypt, встроенных в один раздел, потому что использование
внешней операционной системы часто требует записи данных в область скрытой операционной системы (и если бы такие
операции записи были предотвращены с помощью функции
<a href="Protection%20of%20Hidden%20Volumes.html" style="text-align:left; color:#0080c0; text-decoration:none">
защиты скрытого тома</a>, это неизбежно вызвало бы сбои системы, то есть ошибки "синего экрана").<br style="text-align:left">
&dagger; Это не относится к файловым системам на CD/DVD-подобных носителях, а также к пользовательским, нетипичным или нестандартным устройствам/носителям.</span><br style="text-align:left">
<br style="text-align:left">
<br style="text-align:left">
<br style="text-align:left">
<br style="text-align:left">
&nbsp;&nbsp;См. также: <strong style="text-align:left"><a href="System%20Encryption.html" style="text-align:left; color:#0080c0; text-decoration:none">Шифрование системы</a></strong>, &nbsp;<strong style="text-align:left"><a href="Hidden%20Volume.html" style="text-align:left; color:#0080c0; text-decoration:none">Скрытый
том</a></strong></p>
</div>
</body></html>
