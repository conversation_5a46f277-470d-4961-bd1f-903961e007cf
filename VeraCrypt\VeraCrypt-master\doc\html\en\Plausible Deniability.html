<!DOCTYPE html>
<html lang="en">
<head>
<meta http-equiv="content-type" content="text/html; charset=utf-8" />
<title>VeraCrypt - Free Open source disk encryption with strong security for the Paranoid</title>
<meta name="description" content="VeraCrypt is free open-source disk encryption software for Windows, Mac OS X and Linux. In case an attacker forces you to reveal the password, VeraCrypt provides plausible deniability. In contrast to file encryption, data encryption performed by VeraCrypt is real-time (on-the-fly), automatic, transparent, needs very little memory, and does not involve temporary unencrypted files."/>
<meta name="keywords" content="encryption, security"/>
<link href="styles.css" rel="stylesheet" type="text/css" />
</head>
<body>

<div>
<a href="Documentation.html"><img src="VeraCrypt128x128.png" alt="VeraCrypt"/></a>
</div>

<div id="menu">
	<ul>
	  <li><a href="Home.html">Home</a></li>
	  <li><a href="Code.html">Source Code</a></li>
	  <li><a href="Downloads.html">Downloads</a></li>
	  <li><a class="active" href="Documentation.html">Documentation</a></li>
	  <li><a href="Donation.html">Donate</a></li>
	  <li><a href="https://sourceforge.net/p/veracrypt/discussion/" target="_blank">Forums</a></li>
	</ul>
</div>

<div>
<p>
<a href="Documentation.html">Documentation</a>
<img src="arrow_right.gif" alt=">>" style="margin-top: 5px">
<a href="Plausible%20Deniability.html">Plausible Deniability</a>
</p></div>

<div class="wikidoc">
<h1>Plausible Deniability</h1>
<div style="text-align:left; margin-top:19px; margin-bottom:19px; padding-top:0px; padding-bottom:0px">
In case an adversary forces you to reveal your password, VeraCrypt provides and supports two kinds of plausible deniability:</div>
<ol style="text-align:left; margin-top:18px; margin-bottom:19px; padding-top:0px; padding-bottom:0px">
<li style="text-align:left; margin-top:0px; margin-bottom:0px; padding-top:0px; padding-bottom:0px">
Hidden volumes (see the section <a href="Hidden%20Volume.html" style="text-align:left; color:#0080c0; text-decoration:none; font-weight:bold">
Hidden Volume</a>) and hidden operating systems (see the section <a href="Hidden%20Operating%20System.html" style="text-align:left; color:#0080c0; text-decoration:none">
<strong style="text-align:left">Hidden Operating System</strong></a>). </li><li style="text-align:left; margin-top:0px; margin-bottom:0px; padding-top:0px; padding-bottom:0px">
Until decrypted, a VeraCrypt partition/device appears to consist of nothing more than random data (it does not contain any kind of &quot;signature&quot;). Therefore, it should be impossible to prove that a partition or a device is a VeraCrypt volume or that it has been
 encrypted (provided that the security requirements and precautions listed in the chapter
<a href="Security%20Requirements%20and%20Precautions.html" style="text-align:left; color:#0080c0; text-decoration:none">
Security Requirements and Precautions</a> are followed). A possible plausible explanation for the existence of a partition/device containing solely random data is that you have wiped (securely erased) the content of the partition/device using one of the tools
 that erase data by overwriting it with random data (in fact, VeraCrypt can be used to securely erase a partition/device too, by creating an empty encrypted partition/device-hosted volume within it). However, you need to prevent data leaks (see the section
<a href="Data%20Leaks.html" style="text-align:left; color:#0080c0; text-decoration:none">
Data Leaks</a>) and also note that, for <a href="System%20Encryption.html" style="text-align:left; color:#0080c0; text-decoration:none">
system encryption</a>, the first drive track contains the (unencrypted) VeraCrypt Boot Loader, which can be easily identified as such (for more information, see the chapter
<a href="System%20Encryption.html" style="text-align:left; color:#0080c0; text-decoration:none">
System Encryption</a>). When using <a href="System%20Encryption.html" style="text-align:left; color:#0080c0; text-decoration:none">
system encryption</a>, plausible deniability can be achieved by creating a hidden operating system (see the section
<a href="Hidden%20Operating%20System.html" style="text-align:left; color:#0080c0; text-decoration:none">
Hidden Operating System</a>).<br style="text-align:left">
<br style="text-align:left">
Although file-hosted VeraCrypt volumes (containers) do not contain any kind of &quot;signature&quot; either (until decrypted, they appear to consist solely of random data), they cannot provide this kind of plausible deniability, because there is practically no plausible
 explanation for the existence of a file containing solely random data. However, plausible deniability can still be achieved with a file-hosted VeraCrypt volume (container) by creating a hidden volume within it (see above).
</li></ol>
<h4 style="text-align:left; font-family:Arial,Helvetica,Verdana,sans-serif; font-weight:bold; margin-top:0px; font-size:12px; margin-bottom:1px">
<br style="text-align:left">
Notes</h4>
<ul style="text-align:left; margin-top:18px; margin-bottom:19px; padding-top:0px; padding-bottom:0px">
<li style="text-align:left; margin-top:0px; margin-bottom:0px; padding-top:0px; padding-bottom:0px">
When formatting a hard disk partition as a VeraCrypt volume (or encrypting a partition in place), the partition table (including the partition type) is
<em style="text-align:left">never</em> modified (no VeraCrypt &quot;signature&quot; or &quot;ID&quot; is written to the partition table).
</li><li style="text-align:left; margin-top:0px; margin-bottom:0px; padding-top:0px; padding-bottom:0px">
There are methods to find files or devices containing random data (such as VeraCrypt volumes). Note, however, that this should
<em style="text-align:left">not </em>affect plausible deniability in any way. The adversary still should not be able to
<em style="text-align:left">prove</em> that the partition/device is a VeraCrypt volume or that the file, partition, or device, contains a hidden VeraCrypt volume (provided that you follow the security requirements and precautions listed in the chapter
<a href="Security%20Requirements%20and%20Precautions.html" style="text-align:left; color:#0080c0; text-decoration:none">
Security Requirements and Precautions</a> and in the subsection <a href="Security%20Requirements%20for%20Hidden%20Volumes.html" style="text-align:left; color:#0080c0; text-decoration:none">
Security Requirements and Precautions Pertaining to Hidden Volumes</a>). </li></ul>
<p>&nbsp;</p>
<p><a href="Hidden%20Volume.html" style="text-align:left; color:#0080c0; text-decoration:none; font-weight:bold">Next Section &gt;&gt;</a></p>
</div><div class="ClearBoth"></div></body></html>
