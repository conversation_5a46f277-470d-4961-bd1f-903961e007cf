<!DOCTYPE html>
<html lang="ru">
<head>
<meta http-equiv="content-type" content="text/html; charset=utf-8" />
<title>VeraCrypt - Руководство по преобразованию томов для версий 1.26 и новее</title>
<meta name="description" content="Как обращаться с устаревшими функциями и преобразовывать тома TrueCrypt в программе VeraCrypt версии 1.26 и новее."/>
<meta name="keywords" content="VeraCrypt, TrueCrypt, преобразование, шифрование, безопасность"/>
<link href="styles.css" rel="stylesheet" type="text/css" />
</head>
<body>

<div>
<a href="Documentation.html"><img src="VeraCrypt128x128.png" alt="VeraCrypt"/></a>
</div>

<div id="menu">
	<ul>
	  <li><a href="Home.html">Начало</a></li>
	  <li><a href="Code.html">Исходный код</a></li>
	  <li><a href="Downloads.html">Загрузить</a></li>
	  <li><a class="active" href="Documentation.html">Документация</a></li>
	  <li><a href="Donation.html">Поддержать разработку</a></li>
	  <li><a href="https://sourceforge.net/p/veracrypt/discussion/" target="_blank">Форум</a></li>
	</ul>
</div>

<div>
<p>
<a href="Documentation.html">Документация</a>
<img src="arrow_right.gif" alt=">>" style="margin-top: 5px">
<a href="Conversion_Guide_VeraCrypt_1.26_and_Later.html">Руководство по преобразованию томов для версий 1.26 и новее</a>
</p></div>

<div class="wikidoc">
<h1>Руководство по преобразованию томов для версий 1.26 и новее</h1>

<h2>1. Введение</h2>
<p>В VeraCrypt версии 1.26 и новее внесены значительные изменения, в том числе удалена поддержка некоторых функций. Если у вас возникли проблемы с монтированием томов, это руководство поможет вам разобраться и найти решение.</p>

<h2>2. Устаревшие функции в VeraCrypt 1.26 и новее</h2>
<p>Удалены следующие функции:</p>
<ul>
<li>Режим TrueCrypt</li>
<li>Хеш-алгоритм HMAC-RIPEMD-160</li>
<li>Алгоритм шифрования GOST89</li>
</ul>
<p>Если возникают ошибки при монтировании томов, созданных в VeraCrypt версии 1.25.9 или старее, используйте VeraCrypt 1.25.9, чтобы проверить, используются ли в томах устаревшие функции. Выделите том и нажмите "Свойства тома" в интерфейсе для проверки.</p>

<h2>3. Процедуры устранения проблем в зависимости от версии</h2>

<h3>3.1 Сценарий 1: Использование VeraCrypt 1.25.9 или старее</h3>
<p>Если вы используете VeraCrypt 1.25.9 или можете обновиться до этой версии, выполните следующие действия:</p>
<ul>
<li>Преобразуйте тома TrueCrypt в тома VeraCrypt</li>
<li>Измените устаревший хеш-алгоритм HMAC-RIPEMD-160 на другой</li>
<li>Воссоздайте том VeraCrypt заново, если используется алгоритм шифрования GOST89</li>
</ul>
<p>Загрузите версию 1.25.9 <a href="https://veracrypt.jp/en/Downloads_1.25.9.html">здесь</a>.</p>

<h3>3.2 Сценарий 2: Использование VeraCrypt версии 1.26 и новее</h3>
<p>Если вы уже используете VeraCrypt версии 1.26 или новее, выполните следующие действия:</p>
<ul>
<li>Преобразуйте тома TrueCrypt в тома VeraCrypt</li>
<li>Измените устаревший хеш-алгоритм HMAC-RIPEMD-160 на другой</li>
</ul>
<p>Если вы используете Linux или macOS, временно понизьте версию VeraCrypt до 1.25.9. Пользователи Windows могут использовать инструмент VCPassChanger, доступный для загрузки <a href="https://launchpad.net/veracrypt/trunk/1.25.9/+download/VCPassChanger_%28TrueCrypt_Convertion%29.zip">здесь</a>.</p>
<ul>
<li>Воссоздайте том VeraCrypt заново, если используется алгоритм шифрования GOST89</li>
</ul>
<p>Для всех операционных систем временно понизьте версию VeraCrypt до 1.25.9.</p>

<h2>4. Процедуры преобразования и устранения проблем</h2>

<h3>4.1 Преобразование файлов-контейнеров и разделов TrueCrypt в формат VeraCrypt</h3>
<p>Файлы-контейнеры и разделы TrueCrypt, созданные с помощью TrueCrypt версий 6.x и 7.x, можно преобразовать в формат VeraCrypt с помощью VeraCrypt версии 1.25.9 или инструмента VCPassChanger в Windows. См. дополнительную информацию в <a href="Converting%20TrueCrypt%20volumes%20and%20partitions.html">документации</a>.</p>
<p>После преобразования расширение файла останется тем же — <code>.tc</code>. Измените его на <code>.hc</code> вручную, если хотите, чтобы файл автоматически распознавался VeraCrypt версии 1.26 и новее.</p>

<h3>4.2 Изменение устаревшего хеш-алгоритма HMAC-RIPEMD-160</h3>
<p>Используйте функцию "Изменить алгоритм формирования ключа заголовка", чтобы заменить хеш-алгоритм HMAC-RIPEMD-160 на поддерживаемый в VeraCrypt версии 1.26. См. дополнительные сведения в <a href="Hash%20Algorithms.html">документации</a>.</p>

<h3>4.3 Повторное создание тома VeraCrypt, если использован алгоритм шифрования GOST89</h3>
<p>Если в томе используется шифрование GOST89, скопируйте данные в другое место и заново создайте том, выбрав поддерживаемый алгоритм шифрования. Дополнительные сведения по алгоритмам шифрования см. в <a href="Encryption%20Algorithms.html">документации</a>.</p>

<h2>5. Важные примечания</h2>
<p><strong>Примечание для пользователей, создавших тома с помощью VeraCrypt версии 1.17 или старее:</strong></p>
<blockquote>
<p>Чтобы избежать раскрытия информации о том, содержат ли ваши тома скрытый раздел, или если вы полагаетесь на правдоподобное отрицание, необходимо заново создать как внешний, так и скрытый тома, включая шифрование системы и скрытую ОС. Удалите тома, созданные в VeraCrypt версий до 1.18a.</p>
</blockquote>

<p>См. дополнительную информацию в разделах:</p>
<ul>
<li><a href="TrueCrypt%20Support.html">Поддержка TrueCrypt</a></li>
<li><a href="Converting%20TrueCrypt%20volumes%20and%20partitions.html">Преобразование томов и разделов TrueCrypt в формат VeraCrypt</a></li>
</ul>

</div>

<div class="ClearBoth"></div>
</body>
</html>
