﻿<!DOCTYPE html>
<html lang="ru">
<head>
<meta http-equiv="content-type" content="text/html; charset=utf-8" />
<title>VeraCrypt - Бесплатное надёжное шифрование дисков с открытым исходным кодом</title>
<meta name="description" content="VeraCrypt это бесплатное программное обеспечение для шифрования дисков с открытым исходным кодом для Windows, Mac OS X (macOS) и Linux. В случае, если злоумышленник вынуждает вас раскрыть пароль, VeraCrypt обеспечивает правдоподобное отрицание наличия шифрования. В отличие от пофайлового шифрования, VeraCrypt шифрует данные в реальном времени (на лету), автоматически, прозрачно, требует очень мало памяти и не использует временные незашифрованные файлы."/>
<meta name="keywords" content="encryption, security, шифрование, безопасность"/>
<link href="styles.css" rel="stylesheet" type="text/css" />
</head>
<body>

<div>
<a href="Documentation.html"><img src="VeraCrypt128x128.png" alt="VeraCrypt"/></a>
</div>

<div id="menu">
	<ul>
	  <li><a href="Home.html">Начало</a></li>
	  <li><a href="Code.html">Исходный код</a></li>
	  <li><a href="Downloads.html">Загрузить</a></li>
	  <li><a class="active" href="Documentation.html">Документация</a></li>
	  <li><a href="Donation.html">Поддержать разработку</a></li>
	  <li><a href="https://sourceforge.net/p/veracrypt/discussion/" target="_blank">Форум</a></li>
	</ul>
</div>

<div>
<p>
<a href="Documentation.html">Документация</a>
<img src="arrow_right.gif" alt=">>" style="margin-top: 5px">
<a href="Security%20Requirements%20and%20Precautions.html">Требования безопасности и меры предосторожности</a>
<img src="arrow_right.gif" alt=">>" style="margin-top: 5px">
<a href="Wear-Leveling.html">Wear-Leveling</a>
</p></div>

<div class="wikidoc">
<h1>Распределение износа блоков (Wear-Leveling)</h1>
<div style="text-align:left; margin-top:19px; margin-bottom:19px; padding-top:0px; padding-bottom:0px">
Некоторые устройства хранения (например, твердотельные SSD-накопители, включая флеш-накопители USB) и некоторые
файловые системы используют так называемые механизмы распределения износа, чтобы продлить срок службы устройства
хранения или носителя. Суть работы этих механизмов в том, что даже если какое-либо приложение многократно записывает
данные в один и тот же логический сектор, в действительности данные распределяются равномерно по всему носителю
(то есть логические сектора переназначаются на разные физические сектора). Отсюда следует, что неприятелю могут
оказаться доступны несколько "версий" одного сектора. А это может повлечь за собой различные проблемы с безопасностью.
Например, когда вы изменяете у тома пароль и/или ключевые файлы, то – в нормальных условиях – заголовок тома
перезаписывается новым, заново зашифрованным заголовком. Если же том находится на устройстве, в котором применяется
wear-leveling, VeraCrypt не может гарантировать, что старый заголовок окажется действительно перезаписан.
Если неприятель обнаружит в устройстве старый заголовок тома (который должен был быть перезаписан), он сможет
воспользоваться им для монтирования тома, указав старый, рассекреченный пароль (и/или скомпрометированные ключевые
файлы, служившие для монтирования этого тома до того, как был заново зашифрован заголовок тома). Из соображений
безопасности мы не рекомендуем создавать/хранить
<a href="VeraCrypt%20Volume.html" style="text-align:left; color:#0080c0; text-decoration:none">
тома VeraCrypt</a> на устройствах (или в файловых системах), в которых применяется механизм wear-leveling
(и не применять VeraCrypt для шифрования любых разделов в таких устройствах или файловых системах).</div>
<div style="text-align:left; margin-top:19px; margin-bottom:19px; padding-top:0px; padding-bottom:0px">
Если вы решили не следовать этой рекомендации и намереваетесь использовать шифрование "на месте" на устройстве,
использующем механизмы wear-leveling, то перед тем как полностью зашифровать раздел/устройство, убедитесь, что
в нём не содержится никаких секретных данных (на таком устройстве VeraCrypt не может надёжно выполнить безопасное
шифрование имеющихся данных "на месте"; тем не менее, после того, как раздел/диск будет полностью зашифрован,
любые записываемые на него новые данные будут надёжно шифроваться "на лету"). При этом нужно соблюдать следующие
меры предосторожности.<br>
Прежде чем запускать VeraCrypt для настройки предзагрузочной аутентификации, отключите файлы подкачки и
перезагрузите операционную систему (после того, как системный раздел/диск будет полностью зашифрован,
<a href="Paging%20File.html" style="text-align:left; color:#0080c0; text-decoration:none">
файлы подкачки</a> можно будет снова включить). На период между запуском VeraCrypt для настройки предзагрузочной
аутентификации и моментом, когда системный раздел/диск будет полностью зашифрован, необходимо отключить
<a href="Hibernation%20File.html" style="text-align:left; color:#0080c0; text-decoration:none">
гибернацию</a>. Учтите, однако, что даже при соблюдении этих мер предосторожности
<em style="text-align:left">нельзя</em> гарантировать отсутствие утечек данных и то, что содержащаяся в устройстве
секретная информация будет безопасно зашифрована. Более подробную информацию см. в разделах
<a href="Data%20Leaks.html" style="text-align:left; color:#0080c0; text-decoration:none">
Утечки данных</a>, <a href="Paging%20File.html" style="text-align:left; color:#0080c0; text-decoration:none">
Файл подкачки</a>, <a href="Hibernation%20File.html" style="text-align:left; color:#0080c0; text-decoration:none">
Файл гибернации</a> и <a href="Memory%20Dump%20Files.html" style="text-align:left; color:#0080c0; text-decoration:none">
Файлы дампа памяти</a>.</div>
<div style="text-align:left; margin-top:19px; margin-bottom:19px; padding-top:0px; padding-bottom:0px">
Если требуется возможность <a href="Plausible%20Deniability.html" style="text-align:left; color:#0080c0; text-decoration:none">
правдоподобного отрицания наличия шифрования</a>, нельзя использовать VeraCrypt ни для шифрования любой части
устройства (или файловой системы), ни для создания на нём зашифрованных контейнеров, если в этом устройстве
применяется механизм wear-leveling.</div>
<p>Выяснить, используется ли в устройстве механизм wear-leveling, можно в документации на это устройство или
у его поставщика/производителя.</p>
</div><div class="ClearBoth"></div></body></html>
