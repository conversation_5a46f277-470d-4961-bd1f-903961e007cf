<!DOCTYPE html>
<html lang="en">
<head>
<meta http-equiv="content-type" content="text/html; charset=utf-8" />
<title>VeraCrypt - Free Open source disk encryption with strong security for the Paranoid</title>
<meta name="description" content="VeraCrypt is free open-source disk encryption software for Windows, Mac OS X and Linux. In case an attacker forces you to reveal the password, VeraCrypt provides plausible deniability. In contrast to file encryption, data encryption performed by VeraCrypt is real-time (on-the-fly), automatic, transparent, needs very little memory, and does not involve temporary unencrypted files."/>
<meta name="keywords" content="encryption, security"/>
<link href="styles.css" rel="stylesheet" type="text/css" />
</head>
<body>

<div>
<a href="Documentation.html"><img src="VeraCrypt128x128.png" alt="VeraCrypt"/></a>
</div>

<div id="menu">
	<ul>
	  <li><a href="Home.html">Home</a></li>
	  <li><a href="Code.html">Source Code</a></li>
	  <li><a href="Downloads.html">Downloads</a></li>
	  <li><a class="active" href="Documentation.html">Documentation</a></li>
	  <li><a href="Donation.html">Donate</a></li>
	  <li><a href="https://sourceforge.net/p/veracrypt/discussion/" target="_blank">Forums</a></li>
	</ul>
</div>

<div>
<p>
<a href="Documentation.html">Documentation</a>
<img src="arrow_right.gif" alt=">>" style="margin-top: 5px">
<a href="Miscellaneous.html">Miscellaneous</a>
<img src="arrow_right.gif" alt=">>" style="margin-top: 5px">
<a href="Using%20VeraCrypt%20Without%20Administrator%20Privileges.html">Using Without Admin Rights</a>
</p></div>

<div class="wikidoc">
<div>
<h2>Using VeraCrypt Without Administrator Privileges</h2>
<p>In Windows, a user who does not have administrator privileges <em>can</em> use VeraCrypt, but only after a system administrator installs VeraCrypt on the system. The reason for that is that VeraCrypt needs a device driver to provide transparent on-the-fly
 encryption/decryption, and users without administrator privileges cannot install/start device drivers in Windows.<br>
<br>
After a system administrator installs VeraCrypt on the system, users without administrator privileges will be able to run VeraCrypt, mount/unmount any type of VeraCrypt volume, load/save data from/to it, and create file-hosted VeraCrypt volumes on the system.
 However, users without administrator privileges cannot encrypt/format partitions, cannot create NTFS volumes, cannot install/uninstall VeraCrypt, cannot change passwords/keyfiles for VeraCrypt partitions/devices, cannot backup/restore headers of VeraCrypt
 partitions/devices, and they cannot run VeraCrypt in &lsquo;portable&rsquo; mode.</p>
<div>
<table style="border-collapse:separate; border-spacing:0px; text-align:left; font-size:11px; line-height:13px; font-family:Verdana,Arial,Helvetica,sans-serif">
<tbody style="text-align:left">
<tr style="text-align:left">
<td style="text-align:left; font-size:11px; line-height:13px; font-family:Verdana,Arial,Helvetica,sans-serif; color:#ff0000; padding:15px; border:1px solid #000000">
Warning: No matter what kind of software you use, as regards personal privacy in most cases, it is
<em>not</em> safe to work with sensitive data under systems where you do not have administrator privileges, as the administrator can easily capture and copy your sensitive data, including passwords and keys.</td>
</tr>
</tbody>
</table>
<p>&nbsp;</p>
</div>
</div>
</div><div class="ClearBoth"></div></body></html>
