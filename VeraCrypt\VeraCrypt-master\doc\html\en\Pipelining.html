<!DOCTYPE html>
<html lang="en">
<head>
<meta http-equiv="content-type" content="text/html; charset=utf-8" />
<title>VeraCrypt - Free Open source disk encryption with strong security for the Paranoid</title>
<meta name="description" content="VeraCrypt is free open-source disk encryption software for Windows, Mac OS X and Linux. In case an attacker forces you to reveal the password, VeraCrypt provides plausible deniability. In contrast to file encryption, data encryption performed by VeraCrypt is real-time (on-the-fly), automatic, transparent, needs very little memory, and does not involve temporary unencrypted files."/>
<meta name="keywords" content="encryption, security"/>
<link href="styles.css" rel="stylesheet" type="text/css" />
</head>
<body>

<div>
<a href="Documentation.html"><img src="VeraCrypt128x128.png" alt="VeraCrypt"/></a>
</div>

<div id="menu">
	<ul>
	  <li><a href="Home.html">Home</a></li>
	  <li><a href="Code.html">Source Code</a></li>
	  <li><a href="Downloads.html">Downloads</a></li>
	  <li><a class="active" href="Documentation.html">Documentation</a></li>
	  <li><a href="Donation.html">Donate</a></li>
	  <li><a href="https://sourceforge.net/p/veracrypt/discussion/" target="_blank">Forums</a></li>
	</ul>
</div>

<div>
<p>
<a href="Documentation.html">Documentation</a>
<img src="arrow_right.gif" alt=">>" style="margin-top: 5px">
<a href="Pipelining.html">Pipelining</a>
</p></div>

<div class="wikidoc">
<h1>Pipelining</h1>
<div style="text-align:left; margin-top:19px; margin-bottom:19px; padding-top:0px; padding-bottom:0px">
When encrypting or decrypting data, VeraCrypt uses so-called pipelining (asynchronous processing). While an application is loading a portion of a file from a VeraCrypt-encrypted volume/drive, VeraCrypt is automatically decrypting it (in RAM). Thanks to pipelining,
 the application does not have wait for any portion of the file to be decrypted and it can start loading other portions of the file right away. The same applies to encryption when writing data to an encrypted volume/drive.</div>
<div style="text-align:left; margin-top:19px; margin-bottom:19px; padding-top:0px; padding-bottom:0px">
Pipelining allows data to be read from and written to an encrypted drive as fast as if the drive was not encrypted (the same applies to file-hosted and partition-hosted VeraCrypt
<a href="VeraCrypt%20Volume.html" style="text-align:left; color:#0080c0; text-decoration:none">
volumes</a>).*</div>
<div style="text-align:left; margin-top:19px; margin-bottom:19px; padding-top:0px; padding-bottom:0px">
Note: Pipelining is implemented only in the Windows versions of VeraCrypt.</div>
<p>&nbsp;</p>
<hr align="left" size="1" width="189" style="text-align:left; height:0px; border-width:0px 1px 1px; border-style:solid; border-color:#000000">
<p><span style="text-align:left; font-size:10px; line-height:12px">* Some solid-state drives compress data internally, which appears to increase the actual read/write speed when the data is compressible (for example, text files). However, encrypted data cannot
 be compressed (as it appears to consist solely of random &quot;noise&quot; without any compressible patterns). This may have various implications. For example, benchmarking software that reads or writes compressible data (such as sequences of zeroes) will report lower
 speeds on encrypted volumes than on unencrypted volumes (to avoid this, use benchmarking software that reads/writes random or other kinds of uncompressible data)</span><span style="text-align:left; font-size:10px; line-height:12px">.</span></p>
</div><div class="ClearBoth"></div></body></html>
