﻿<!DOCTYPE html>
<html lang="ru">
<head>
<meta http-equiv="content-type" content="text/html; charset=utf-8" />
<title>VeraCrypt - Бесплатное надёжное шифрование дисков с открытым исходным кодом</title>
<meta name="description" content="VeraCrypt это бесплатное программное обеспечение для шифрования дисков с открытым исходным кодом для Windows, Mac OS X (macOS) и Linux. В случае, если злоумышленник вынуждает вас раскрыть пароль, VeraCrypt обеспечивает правдоподобное отрицание наличия шифрования. В отличие от пофайлового шифрования, VeraCrypt шифрует данные в реальном времени (на лету), автоматически, прозрачно, требует очень мало памяти и не использует временные незашифрованные файлы."/>
<meta name="keywords" content="encryption, security, шифрование, безопасность"/>
<link href="styles.css" rel="stylesheet" type="text/css" />
</head>
<body>

<div>
<a href="Documentation.html"><img src="VeraCrypt128x128.png" alt="VeraCrypt"/></a>
</div>

<div id="menu">
	<ul>
	  <li><a href="Home.html">Начало</a></li>
	  <li><a href="Code.html">Исходный код</a></li>
	  <li><a href="Downloads.html">Загрузить</a></li>
	  <li><a class="active" href="Documentation.html">Документация</a></li>
	  <li><a href="Donation.html">Поддержать разработку</a></li>
	  <li><a href="https://sourceforge.net/p/veracrypt/discussion/" target="_blank">Форум</a></li>
	</ul>
</div>

<div>
<p>
<a href="Documentation.html">Документация</a>
<img src="arrow_right.gif" alt=">>" style="margin-top: 5px">
<a href="Hash%20Algorithms.html">Алгоритмы хеширования</a>
<img src="arrow_right.gif" alt=">>" style="margin-top: 5px">
<a href="BLAKE2s-256.html">BLAKE2s-256</a>
</p></div>

<div class="wikidoc">
<h1>BLAKE2s-256</h1>
<div style="text-align:left; margin-top:19px; margin-bottom:19px; padding-top:0px; padding-bottom:0px">
<p>
BLAKE2 – это основанная на BLAKE криптографическая хеш-функция (авторы: Jean-Philippe Aumasson,
Samuel Neves, Zooko Wilcox-O'Hearn и Christian Winnerlein, опубликована 21 декабря 2012 года).
Цель разработки состояла в том, чтобы заменить широко используемые, но взломанные алгоритмы
MD5 и SHA-1 в приложениях, требующих высокой производительности программного обеспечения.
BLAKE2 обеспечивает лучшую безопасность, чем SHA-2, и похож на SHA-3 (например, невосприимчив
к увеличению длины, имеет недифференцируемость от случайного оракула и т. д.).<br/>
BLAKE2 убирает добавление констант к словам сообщения из функции раунда BLAKE, изменяет две
константы вращения, упрощает заполнение, добавляет блок параметров, который подвергается
операции XOR с векторами инициализации, и уменьшает количество раундов с 16 до 12 для BLAKE2b
(преемника BLAKE- 512) и с 14 до 10 для BLAKE2s (преемник BLAKE-256).<br/>
BLAKE2b и BLAKE2s указаны в документе RFC 7693.
</p>
<p>
VeraCrypt использует только BLAKE2s с максимальным выходным размером 32 байта (256 бит).
</p>
</div>
<div style="text-align:left; margin-top:19px; margin-bottom:19px; padding-top:0px; padding-bottom:0px">
<a href="SHA-256.html" style="text-align:left; color:#0080c0; text-decoration:none; font-weight:bold">Следующий раздел &gt;&gt;</a></div>
</div><div class="ClearBoth"></div></body></html>
