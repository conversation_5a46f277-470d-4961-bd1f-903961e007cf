﻿<!DOCTYPE html>
<html lang="ru">
<head>
<meta http-equiv="content-type" content="text/html; charset=utf-8" />
<title>VeraCrypt - Бесплатное надёжное шифрование дисков с открытым исходным кодом</title>
<meta name="description" content="VeraCrypt это бесплатное программное обеспечение для шифрования дисков с открытым исходным кодом для Windows, Mac OS X (macOS) и Linux. В случае, если злоумышленник вынуждает вас раскрыть пароль, VeraCrypt обеспечивает правдоподобное отрицание наличия шифрования. В отличие от пофайлового шифрования, VeraCrypt шифрует данные в реальном времени (на лету), автоматически, прозрачно, требует очень мало памяти и не использует временные незашифрованные файлы."/>
<meta name="keywords" content="encryption, security, шифрование, безопасность"/>
<link href="styles.css" rel="stylesheet" type="text/css" />
</head>
<body>

<div>
<a href="Documentation.html"><img src="VeraCrypt128x128.png" alt="VeraCrypt"/></a>
</div>

<div id="menu">
	<ul>
	  <li><a href="Home.html">Начало</a></li>
	  <li><a href="Code.html">Исходный код</a></li>
	  <li><a href="Downloads.html">Загрузить</a></li>
	  <li><a class="active" href="Documentation.html">Документация</a></li>
	  <li><a href="Donation.html">Поддержать разработку</a></li>
	  <li><a href="https://sourceforge.net/p/veracrypt/discussion/" target="_blank">Форум</a></li>
	</ul>
</div>

<div>
<p>
<a href="Documentation.html">Документация</a>
<img src="arrow_right.gif" alt=">>" style="margin-top: 5px">
<a href="Security%20Requirements%20and%20Precautions.html">Требования безопасности и меры предосторожности</a>
<img src="arrow_right.gif" alt=">>" style="margin-top: 5px">
<a href="Changing%20Passwords%20and%20Keyfiles.html">Изменение паролей и ключевых файлов</a>
</p></div>

<div class="wikidoc">
<h1>Изменение паролей и ключевых файлов</h1>
<p>Обратите внимание, что в заголовке тома (который зашифрован с помощью ключа заголовка, сформированного из пароля/ключевого файла) содержится мастер-ключ (не путайте с паролем), посредством которого зашифрован том.
 Если у злоумышленника есть возможность сделать копию вашего тома до того, как изменили пароль и/или ключевые файлы, он может использовать свою копию или фрагмент (старый заголовок) тома VeraCrypt, чтобы смонтировать ваш том, используя скомпрометированный пароль и/или скомпрометированные ключевые файлы, которые требовались для монтирования тома
 до изменения пароля и/или ключевых файлов.<br>
<br>
Если вы не уверены, знает ли злоумышленник ваш пароль (или обладает вашими ключевыми файлами), и что у него нет копии вашего тома, когда вам потребовалось изменить его пароль и/или ключевые файлы, то настоятельно рекомендуется создать новый том VeraCrypt и переместить в него файлы
 из старого тома (у нового тома будет другой мастер-ключ).<br>
<br>
Также учтите, что если злоумышленник знает ваш пароль (или обладает вашими ключевыми файлами) и у него есть доступ к вашему тому, он может получить и сохранить его мастер-ключ. Сделав это, он сможет расшифровать ваш том, даже если вы затем изменили пароль и/или ключевые файлы
 (потому что мастер-ключ не меняется при смене пароля тома и/или ключевых файлов). В этом случае создайте новый том VeraCrypt и переместите в него все файлы из старого тома.<br>
<br>
Следующие разделы этой главы содержат дополнительную информацию о возможных проблемах безопасности, связанных со сменой паролей и/или ключевых файлов:</p>
<ul>
<li><a href="Security%20Requirements%20and%20Precautions.html"><em>Требования безопасности и меры предосторожности</em></a>
</li><li><a href="Journaling%20File%20Systems.html"><em>Журналируемые файловые системы</em></a>
</li><li><a href="Defragmenting.html"><em>Дефрагментация</em></a>
</li><li><a href="Reallocated%20Sectors.html"><em>Перераспределённые сектора</em></a>
</li></ul>
</div><div class="ClearBoth"></div></body></html>
