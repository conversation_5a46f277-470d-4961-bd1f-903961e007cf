﻿<!DOCTYPE html>
<html lang="ru">
<head>
<meta http-equiv="content-type" content="text/html; charset=utf-8" />
<title>VeraCrypt - Бесплатное надёжное шифрование дисков с открытым исходным кодом</title>
<meta name="description" content="VeraCrypt это бесплатное программное обеспечение для шифрования дисков с открытым исходным кодом для Windows, Mac OS X (macOS) и Linux. В случае, если злоумышленник вынуждает вас раскрыть пароль, VeraCrypt обеспечивает правдоподобное отрицание наличия шифрования. В отличие от пофайлового шифрования, VeraCrypt шифрует данные в реальном времени (на лету), автоматически, прозрачно, требует очень мало памяти и не использует временные незашифрованные файлы."/>
<meta name="keywords" content="encryption, security, шифрование, безопасность"/>
<link href="styles.css" rel="stylesheet" type="text/css" />
</head>
<body>

<div>
<a href="Documentation.html"><img src="VeraCrypt128x128.png" alt="VeraCrypt"/></a>
</div>

<div id="menu">
	<ul>
	  <li><a href="Home.html">Начало</a></li>
	  <li><a href="Code.html">Исходный код</a></li>
	  <li><a href="Downloads.html">Загрузить</a></li>
	  <li><a class="active" href="Documentation.html">Документация</a></li>
	  <li><a href="Donation.html">Поддержать разработку</a></li>
	  <li><a href="https://sourceforge.net/p/veracrypt/discussion/" target="_blank">Форум</a></li>
	</ul>
</div>

<div>
<p>
<a href="Documentation.html">Документация</a>
<img src="arrow_right.gif" alt=">>" style="margin-top: 5px">
<a href="VeraCrypt%20Volume.html">Том VeraCrypt</a>
<img src="arrow_right.gif" alt=">>" style="margin-top: 5px">
<a href="Creating%20New%20Volumes.html">Создание новых томов</a>
</p></div>

<div class="wikidoc">
<h1>Создание нового тома VeraCrypt</h1>
<div style="text-align:left; margin-top:19px; margin-bottom:19px; padding-top:0px; padding-bottom:0px">
<p>Чтобы создать том VeraCrypt на основе файла или чтобы зашифровать раздел/устройство (для этого требуются права администратора),
нажмите кнопку <i>Создать том</i> в главном окне программы. Появится окно мастера создания томов VeraCrypt. Сразу за этим
мастер начнёт сбор данных для генерирования мастер-ключа, вторичного ключа (режим XTS) и соли для нового тома. В сборе данных,
которые должны носить как можно более случайный характер, участвуют перемещения мыши, нажатия клавиш и другая получаемая из системы
информация (см. подробности в главе <a href="Random%20Number%20Generator.html"> <em>Генератор случайных чисел</em></a>).
При создании тома VeraCrypt мастер предоставляет необходимые подсказки и сведения, однако некоторые настройки требуют пояснения.
</p>
<h3>Алгоритм хеширования</h3>
<p>Здесь можно выбрать хеш-алгоритм, который будет применять VeraCrypt. Выбранный алгоритм используется генератором случайных
чисел (как функция псевдослучайного смешивания) для генерирования мастер-ключа, вторичного ключа (режим XTS) и соли
(см. раздел <a href="Random%20Number%20Generator.html"><em>Генератор случайных чисел</em></a>). Также он используется в формировании
(деривации) ключа заголовка тома и вторичного ключа заголовка (см. раздел <a href="Header%20Key%20Derivation.html">
<em>Формирование ключа заголовка, соль и количество итераций</em></a>).<br>
<br>
Сведения о доступных хеш-алгоритмах приведены в главе <a href="Hash%20Algorithms.html">
<em>Алгоритмы хеширования.</em></a><br>
<br>
Обратите внимание: вывод хеш-функции <em>никогда</em> не используется непосредственно как ключ шифрования. Дополнительные сведения
см. в главе <a href="Technical%20Details.html"><em>Технические подробности</em></a>.
</p>
<h3>Алгоритм шифрования</h3>
<p>Здесь выбирается алгоритм шифрования, который будет применяться в новом томе. Обратите внимание, что выбранный алгоритм шифрования
после создания тома изменить уже нельзя. Дополнительные сведения см. в главе
<a href="Encryption%20Algorithms.html"><em>Алгоритмы шифрования</em></a>.
</p>
<h3 id="QuickFormat">Быстрое форматирование</h3>
<p>Если этот параметр выключен, форматированию подвергается каждый сектор нового тома. Это означает, что новый том будет
<em>целиком</em> заполнен случайными данными. Быстрое форматирование занимает гораздо меньше времени, но оно менее надёжно,
так как пока весь том не будет заполнен файлами, существует вероятность определить, как много данных он содержит
(если свободное пространство не было предварительно заполнено случайными данными). Если вы не уверены, нужно ли включать
или выключать быстрое форматирование, рекомендуем оставить этот параметр выключенным. Обратите внимание, что параметр
<i>Быстрое форматирование</i> доступен только при шифровании разделов/устройств, за исключением Windows, где он также доступен при создании файловых контейнеров.</p>
<p><i>ВАЖНО: При шифровании раздела/устройства, внутри которого вы планируете затем создать скрытый том, оставьте этот параметр <u>выключенным</u>.</i>
</p>
<h3 id="dynamic">Динамический («растягивающийся») том</h3>
<p>Динамический контейнер VeraCrypt представляет собой предраспределённый разрежённый (sparse) файл NTFS, чей физический
размер (реально занимаемое место на диске) увеличивается по мере добавления в контейнер новых данных. Обратите внимание,
что физический размер контейнера (реально занимаемое контейнером место на диске) <i>не уменьшается</i> при удалении файлов
из тома VeraCrypt. Физический размер контейнера может только увеличиваться до максимального значения, указанного пользователем
при создании этого тома. По достижении указанного максимального значения физический размер тома будет оставаться постоянным.<br>
<br>
Учтите, что разрежённые файлы можно создавать только в файловой системе NTFS. При создании контейнера в файловой системе FAT,
параметр <em>Динамический</em> будет недоступен (&ldquo;затенён&rdquo;).<br>
<br>
Имейте в виду, что размер динамического (на основе разрежённого файла) тома VeraCrypt, сообщаемый Windows и VeraCrypt, будет
всегда равен его максимальному размеру (который был указан при создании этого тома). Чтобы узнать текущий физический размер
контейнера (действительно занимаемое им место на диске), щёлкните правой кнопкой по файлу-контейнеру (в Проводнике Windows,
не в VeraCrypt), и выберите пункт <em>Свойства</em> – в поле <i>На диске</i> будет указано реальное значение.</p>
<p>ВНИМАНИЕ: Скорость выполнения операций у динамических (на основе разрежённых файлов) томов VeraCrypt значительно ниже, чем
у обычных томов. Кроме того, динамические тома VeraCrypt менее безопасны, так как они позволяют определить количество незанятых
секторов в томе. Более того, если при записи данных на динамический том окажется, что в файловой системе, где находится
файловый контейнер с данным томом, недостаточно свободного места, это может привести к повреждению зашифрованной файловой системы.</p>
<h3>Размер кластера</h3>
<p>Кластер это единица хранения данных. Например, один распределённый кластер в файловой системе FAT – это однобайтовый файл.
Когда файл увеличивается и превосходит границу кластера, распределяется ещё один кластер. В теории это означает, что чем
больше размер кластера, тем больше тратится места на диске и тем выше производительность. Если вы не знаете, какой размер
выбрать, используйте значение, предложенное по умолчанию.</p>
<h3>Тома VeraCrypt на дисках CD и DVD</h3>
<p>Если вы хотите сохранить том VeraCrypt на CD или DVD, то сначала создайте на жёстком диске контейнер TrueCrypt на основе
файла, а затем запишите («прожгите») его на CD/DVD с помощью любой программы для записи CD/DVD (в среде Windows XP и более
новых версий Windows для этого можно воспользоваться средством записи CD, входящим в комплект поставки этой ОС).
Имейте в виду, что если вы собираетесь монтировать том VeraCrypt, хранящийся на носителе, допускающем только чтение (например,
на CD/DVD) в Windows 2000, том VeraCrypt должен быть отформатирован в FAT. Причина этого в том, что Windows 2000 не может
монтировать файловую систему NTFS на носителях только для чтения (в отличие от Windows XP и более новых версий Windows).</p>
<h3>Аппаратный/программный RAID, динамические тома Windows</h3>
<p>VeraCrypt поддерживает аппаратные/программные массивы RAID, а также динамические тома Windows.</p>
<p><i>Windows Vista и новее:</i> динамические тома отображаются в диалоговом окне <i>Выбрать устройство</i> как
<code>\Device\HarddiskVolumeN</code>.</p>
<p><i>Windows XP/2000/2003:</i> если вы намереваетесь отформатировать динамический том Windows как том VeraCrypt, помните,
что после создания динамического тома Windows (с помощью Windows-средства <i>Управление дисками</i>) нужно перезагрузить
операционную систему, чтобы том стал доступен/виден в диалоговом окне выбора устройства в мастере создания томов VeraCrypt.
Также учтите, что в окне <i>Выбрать устройство</i> динамический диск Windows отображается не как одно
устройство (элемент), а как все тома, из которых состоит динамический том Windows, и вы можете выбрать любой из них, чтобы
отформатировать весь динамический том Windows.</p>
<h3>Примечания к созданию томов</h3>
<p>После нажатия кнопки <i>Разметить</i> в окне мастера создания томов (последний этап), последует небольшая задержка,
в течение которой система собирает дополнительные случайные данные. Затем для нового тома генерируются мастер-ключ, ключ
заголовка, вторичный ключ (режим XTS) и соль с показом содержимого мастер-ключа и ключа заголовка.<br>
<br>
Для большей безопасности можно отключить вывод на экран частей содержимого пула случайных чисел, мастер-ключа и ключа
заголовка, сняв отметку в правом верхнем углу соответствующего поля:<br>
<br>
<img src="Beginner's Tutorial_Image_023.png" alt="" width="338" height="51"><br>
<br>
Примечание: отображаются только первые 128 бит пула/ключей (а не всё содержимое).<br>
<br>
Можно создавать тома FAT (будет это FAT12, FAT16 и FAT32 – определяется автоматически по количеству кластеров) или NTFS
(однако нужно иметь в виду, что тома NTFS могут создавать только пользователи с правами администратора). Смонтированные
тома VeraCrypt можно в любое время переформатировать в FAT12, FAT16, FAT32 или NTFS. Они ведут себя точно так же, как
стандартные дисковые устройства, поэтому можно щёлкнуть правой кнопкой мыши по значку смонтированного тома VeraCrypt
(например, в окне <em>Компьютер</em> или <em>Мой компьютер</em>) и выбрать команду <i>Форматировать</i>.<br>
<br>
Более подробные сведения о создании томов VeraCrypt см. в разделе <a href="Hidden%20Volume.html">
<em>Скрытый том</em></a>.</p>
<p>&nbsp;</p>
<p><a href="Favorite%20Volumes.html" style="text-align:left; color:#0080c0; text-decoration:none; font-weight:bold">Следующий раздел &gt;&gt;</a></p>
</div>
</div>

</body></html>
