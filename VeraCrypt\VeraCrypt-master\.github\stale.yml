# Number of days of inactivity before an issue becomes stale
daysUntilStale: 60
# Number of days of inactivity before a stale issue is closed
daysUntilClose: 7
# Issues with these labels will never be considered stale
exemptLabels:
  - pinned
  - security
# Label to use when marking an issue as stale
staleLabel: wontfix
# Comment to post when marking an issue as stale. Set to `false` to disable
markComment: >
  This issue has been automatically marked as stale because it has not had
  recent activity. It will be closed if no further activity occurs. Thank you
  for your contributions.
# Comment to post when closing a stale issue. Set to `false` to disable
closeComment: >
  This issue has been automatically closed because it has not had
  recent activity. This probably means that it is not reproducible
  or it has been fixed in a newer version. If it’s an enhancement
  and hasn't been taken on for so long, then it seems no one has
  the time to implement this.
  Please reopen if you still encounter this issue with the [latest stable version](https://veracrypt.jp/en/Downloads.html).
  You can also contribute directly by providing a pull request.
  Thank you!
