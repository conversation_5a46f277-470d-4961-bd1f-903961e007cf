<head>
<meta http-equiv="content-type" content="text/html; charset=utf-8" />
<title>VeraCrypt - Free Open source disk encryption with strong security for the Paranoid</title>
<meta name="description" content="VeraCrypt is free open-source disk encryption software for Windows, Mac OS X and Linux. In case an attacker forces you to reveal the password, VeraCrypt provides plausible deniability. In contrast to file encryption, data encryption performed by VeraCrypt is real-time (on-the-fly), automatic, transparent, needs very little memory, and does not involve temporary unencrypted files."/>
<meta name="keywords" content="encryption, security"/>
<link href="styles.css" rel="stylesheet" type="text/css" />
</head>
<body>

<div>
<a href="Documentation.html"><img src="VeraCrypt128x128.png" alt="VeraCrypt"/></a>
</div>

<div id="menu">
	<ul>
	  <li><a href="Home.html">Home</a></li>
	  <li><a href="Code.html">Source Code</a></li>
	  <li><a href="Downloads.html">Downloads</a></li>
	  <li><a class="active" href="Documentation.html">Documentation</a></li>
	  <li><a href="Donation.html">Donate</a></li>
	  <li><a href="https://sourceforge.net/p/veracrypt/discussion/" target="_blank">Forums</a></li>
	</ul>
</div>

<div>
<p>
<a href="Documentation.html">Documentation</a>
<img src="arrow_right.gif" alt=">>" style="margin-top: 5px">
<a href="Supported%20Operating%20Systems.html">Supported Operating Systems</a>
</p></div>

<div class="wikidoc">
<h1>Supported Operating Systems</h1>
<div style="text-align:left; margin-top:19px; margin-bottom:19px; padding-top:0px; padding-bottom:0px">
VeraCrypt currently supports the following operating systems:</div>
<ul style="text-align:left; margin-top:18px; margin-bottom:19px; padding-top:0px; padding-bottom:0px">
<li style="text-align:left; margin-top:0px; margin-bottom:0px; padding-top:0px; padding-bottom:0px">
Windows 11 </li><li style="text-align:left; margin-top:0px; margin-bottom:0px; padding-top:0px; padding-bottom:0px">
Windows 10 </li><li style="text-align:left; margin-top:0px; margin-bottom:0px; padding-top:0px; padding-bottom:0px">
Windows Server 2016 </li><li style="text-align:left; margin-top:0px; margin-bottom:0px; padding-top:0px; padding-bottom:0px">
Mac OS X 14 Sonoma </li><li style="text-align:left; margin-top:0px; margin-bottom:0px; padding-top:0px; padding-bottom:0px">
Mac OS X 13 Ventura </li><li style="text-align:left; margin-top:0px; margin-bottom:0px; padding-top:0px; padding-bottom:0px">
Mac OS X 12 Monterey </li><li style="text-align:left; margin-top:0px; margin-bottom:0px; padding-top:0px; padding-bottom:0px">
Linux x86, x86-64, ARM64 (Starting from Debian 10, Ubuntu 20.04, CentOS 7, OpenSUSE 15.1)  </li><li style="text-align:left; margin-top:0px; margin-bottom:0px; padding-top:0px; padding-bottom:0px">
FreeBSD x86-64 (starting from version 12) </li><li style="text-align:left; margin-top:0px; margin-bottom:0px; padding-top:0px; padding-bottom:0px">
Raspberry Pi OS (32-bit and 64-bit) </li></ul>
<div style="text-align:left; margin-top:19px; margin-bottom:19px; padding-top:0px; padding-bottom:0px">
<span style="text-align:left;">Note: <br/>
VeraCrypt 1.25.9 is the last version that supports Windows XP, Windows Vista, Windows 7, Windows 8, and Windows 8.1.<br/>
VeraCrypt 1.25.9 is the last version the supports Mac OS X versions from 10.9 Mavericks to 11 Big Sur<br/>
VeraCrypt 1.24-Update8 is the last version that supports Mac OS X 10.7 Lion and Mac OS X 10.8 Mountain Lion.</span></div>
<div style="text-align:left; margin-top:19px; margin-bottom:19px; padding-top:0px; padding-bottom:0px">
<br style="text-align:left">
<span style="text-align:left; font-size:10px; line-height:12px">Note: The following operating systems (among others) are not supported: Windows RT, Windows 2003 IA-64, Windows 2008 IA-64, Windows XP IA-64, and the Embedded/Tablet versions of Windows.</span></div>
<p><br style="text-align:left">
<br style="text-align:left">
Also see the section <strong style="text-align:left"><a href="Supported%20Systems%20for%20System%20Encryption.html" style="text-align:left; color:#0080c0; text-decoration:none">Operating Systems Supported for System
 Encryption</a></strong></p>
</div>
</body></html>
