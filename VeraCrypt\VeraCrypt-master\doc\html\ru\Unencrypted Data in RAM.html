﻿<!DOCTYPE html>
<html lang="ru">
<head>
<meta http-equiv="content-type" content="text/html; charset=utf-8" />
<title>VeraCrypt - Бесплатное надёжное шифрование дисков с открытым исходным кодом</title>
<meta name="description" content="VeraCrypt это бесплатное программное обеспечение для шифрования дисков с открытым исходным кодом для Windows, Mac OS X (macOS) и Linux. В случае, если злоумышленник вынуждает вас раскрыть пароль, VeraCrypt обеспечивает правдоподобное отрицание наличия шифрования. В отличие от пофайлового шифрования, VeraCrypt шифрует данные в реальном времени (на лету), автоматически, прозрачно, требует очень мало памяти и не использует временные незашифрованные файлы."/>
<meta name="keywords" content="encryption, security, шифрование, безопасность"/>
<link href="styles.css" rel="stylesheet" type="text/css" />
</head>
<body>

<div>
<a href="Documentation.html"><img src="VeraCrypt128x128.png" alt="VeraCrypt"/></a>
</div>

<div id="menu">
	<ul>
	  <li><a href="Home.html">Начало</a></li>
	  <li><a href="Code.html">Исходный код</a></li>
	  <li><a href="Downloads.html">Загрузить</a></li>
	  <li><a class="active" href="Documentation.html">Документация</a></li>
	  <li><a href="Donation.html">Поддержать разработку</a></li>
	  <li><a href="https://sourceforge.net/p/veracrypt/discussion/" target="_blank">Форум</a></li>
	</ul>
</div>

<div>
<p>
<a href="Documentation.html">Документация</a>
<img src="arrow_right.gif" alt=">>" style="margin-top: 5px">
<a href="Security%20Requirements%20and%20Precautions.html">Требования безопасности и меры предосторожности</a>
<img src="arrow_right.gif" alt=">>" style="margin-top: 5px">
<a href="Unencrypted%20Data%20in%20RAM.html">Незашифрованные данные в ОЗУ</a>
</p></div>

<div class="wikidoc">
<h1>Незашифрованные данные в ОЗУ</h1>
<div style="text-align:left; margin-top:19px; margin-bottom:19px; padding-top:0px; padding-bottom:0px">
Важно понимать, что VeraCrypt – это программа для шифрования данных только на <em style="text-align:left">дисках</em>,
но не в ОЗУ (оперативной памяти).</div>
<div style="text-align:left; margin-top:19px; margin-bottom:19px; padding-top:0px; padding-bottom:0px">
Не забывайте о том, что большинство программ не очищают область памяти (буферы), где они хранят незашифрованные
файлы (или их части) при их загрузке из тома VeraCrypt. Это означает, что после того, как вы выйдете из такой
программы, в памяти (в ОЗУ) могут оставаться незашифрованные данные, с которыми она работала, до момента,
пока не будет выключен компьютер (а согласно некоторым исследованиям, даже некоторое время после отключения
питания*). Также имейте в виду, что когда вы открываете хранящийся в томе VeraCrypt файл, например, в текстовом
редакторе, а затем принудительно размонтируете этот том VeraCrypt, данный файл останется незашифрованным в области
памяти (ОЗУ), используемой (занятой) текстовым редактором. Это также относится и к принудительному авторазмонтированию.</div>
<div style="text-align:left; margin-top:19px; margin-bottom:19px; padding-top:0px; padding-bottom:0px">
По сути, незашифрованные мастер-ключи также должны храниться в оперативной памяти. При размонтировании
несистемного тома VeraCrypt стирает его мастер-ключи (хранящиеся в ОЗУ). При штатно выполненной перезагрузке
(или нормальном завершении работы) компьютера все несистемные тома VeraCrypt автоматически размонтируются, и,
соответственно, все хранящиеся в ОЗУ мастер-ключи удаляются драйвером VeraCrypt (за исключением мастер-ключей
для системных разделов/дисков — см. ниже).  Однако при внезапном отключении питания, при перезагрузке компьютера
кнопкой <i>Reset</i> (а не штатно), или при зависании системы <strong style="text-align:left">
VeraCrypt, естественно, перестаёт работать и поэтому не может удалить ни какие-либо ключи, ни любые другие
конфиденциальные данные</strong>. Более того, поскольку Microsoft не предоставляет соответствующего API для
обработки гибернации и завершения работы, используемые для шифрования системы мастер-ключи не могут быть надёжно
удалены (и не удаляются) из ОЗУ при переходе компьютера в состояние гибернации, завершении работы или перезагрузке.**</div>
<div style="text-align:left; margin-top:19px; margin-bottom:19px; padding-top:0px; padding-bottom:0px">
Начиная с версии 1.24, в VeraCrypt появился механизм шифрования мастер-ключей и кэшированных паролей в ОЗУ.
Этот механизм шифрования ОЗУ необходимо активировать вручную в окне <i>Настройки > Производительность и драйвер</i>,
включив опцию <i>Шифровать ключи и пароли в ОЗУ</i>. Шифрование оперативной памяти сопряжено со снижением производительности
(5-15% в зависимости от скорости процессора), и оно отключает гибернацию Windows. <br>
Кроме того, в VeraCrypt 1.24 и выше реализован дополнительный механизм безопасности при шифровании системы,
который заставляет VeraCrypt стирать мастер-ключи из ОЗУ при подключении нового устройства к ПК. Этот дополнительный
механизм активируется опцией в окне системных настроек.<br/>
Несмотря на то, что оба вышеперечисленных механизма обеспечивают надёжную защиту мастер-ключей и кэшированного
пароля, пользователи всё равно должны принимать обычные меры предосторожности, связанные с сохранением
конфиденциальных данных в оперативной памяти.</div>
<table style="border-collapse:separate; border-spacing:0px; text-align:left; font-size:11px; line-height:13px; font-family:Verdana,Arial,Helvetica,sans-serif">
<tbody style="text-align:left">
<tr style="text-align:left">
<td style="text-align:left; font-size:11px; line-height:13px; font-family:Verdana,Arial,Helvetica,sans-serif; color:#ff0000; padding:15px; border:1px solid #000000">
Подводя итог, заметим, что VeraCrypt <strong style="text-align:left">не</strong> может и <strong style="text-align:left">
не</strong> гарантирует отсутствие в ОЗУ секретной информации (паролей, мастер-ключей или расшифрованных данных).
Поэтому после каждого сеанса работы с томом VeraCrypt или в котором запущена зашифрованная операционная
система, вы должны завершить работу компьютера (или, если
<a href="Hibernation%20File.html" style="text-align:left; color:#0080c0; text-decoration:none">
файл гибернации</a> у вас <a href="System%20Encryption.html" style="text-align:left; color:#0080c0; text-decoration:none">
зашифрован</a>, переведите компьютер в режим гибернации), а затем оставить его выключенным как минимум на
несколько минут (чем дольше, тем лучше), прежде чем снова включить его. Это необходимо, чтобы очистить ОЗУ (см. также раздел
<a href="Hibernation%20File.html" style="text-align:left; color:#0080c0; text-decoration:none">
Файл гибернации</a>).</td>
</tr>
</tbody>
</table>
<p>&nbsp;</p>
<hr align="left" size="1" width="189" style="text-align:left; height:0px; border-width:0px 1px 1px; border-style:solid; border-color:#000000">
<p><span style="text-align:left; font-size:10px; line-height:12px">* Предположительно, в течение 1,5-35 секунд при
нормальных рабочих температурах (26-44 &deg;C) и до нескольких часов, если модули памяти охлаждаются (при работающем
компьютере) до очень низких температур (например, до -50&nbsp;&deg;C). У новых типов модулей памяти, как утверждается,
гораздо более короткое время затухания (например, 1,5-2,5 секунды), чем у более старых типов (по состоянию на 2008 год).</span><br style="text-align:left">
<span style="text-align:left; font-size:10px; line-height:12px">** Прежде чем ключ может быть стёрт из оперативной
памяти, должен быть размонтирован соответствующий том VeraCrypt. Для несистемных томов это не вызывает никаких проблем.
Однако поскольку Microsoft в настоящее время не предоставляет какого-либо подходящего API для обработки заключительной
фазы процесса завершения работы системы, файлы подкачки, расположенные на зашифрованных системных томах, которые
размонтируются при завершении работы системы, всё еще могут содержать страницы памяти (включая части системных файлов
Windows). Это может привести к ошибкам "синего экрана". Поэтому чтобы предотвратить ошибки "синего экрана", VeraCrypt
не размонтирует зашифрованные системные тома и, следовательно, не может очистить мастер-ключи системных томов при
выключении или перезагрузке системы.</span></p>
</div><div class="ClearBoth"></div></body></html>
