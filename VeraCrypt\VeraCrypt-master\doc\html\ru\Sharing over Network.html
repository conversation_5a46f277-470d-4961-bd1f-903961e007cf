﻿<!DOCTYPE html>
<html lang="ru">
<head>
<meta http-equiv="content-type" content="text/html; charset=utf-8" />
<title>VeraCrypt - Бесплатное надёжное шифрование дисков с открытым исходным кодом</title>
<meta name="description" content="VeraCrypt это бесплатное программное обеспечение для шифрования дисков с открытым исходным кодом для Windows, Mac OS X (macOS) и Linux. В случае, если злоумышленник вынуждает вас раскрыть пароль, VeraCrypt обеспечивает правдоподобное отрицание наличия шифрования. В отличие от пофайлового шифрования, VeraCrypt шифрует данные в реальном времени (на лету), автоматически, прозрачно, требует очень мало памяти и не использует временные незашифрованные файлы."/>
<meta name="keywords" content="encryption, security, шифрование, безопасность"/>
<link href="styles.css" rel="stylesheet" type="text/css" />
</head>
<body>

<div>
<a href="Documentation.html"><img src="VeraCrypt128x128.png" alt="VeraCrypt"/></a>
</div>

<div id="menu">
	<ul>
	  <li><a href="Home.html">Начало</a></li>
	  <li><a href="Code.html">Исходный код</a></li>
	  <li><a href="Downloads.html">Загрузить</a></li>
	  <li><a class="active" href="Documentation.html">Документация</a></li>
	  <li><a href="Donation.html">Поддержать разработку</a></li>
	  <li><a href="https://sourceforge.net/p/veracrypt/discussion/" target="_blank">Форум</a></li>
	</ul>
</div>

<div>
<p>
<a href="Documentation.html">Документация</a>
<img src="arrow_right.gif" alt=">>" style="margin-top: 5px">
<a href="Miscellaneous.html">Разное</a>
<img src="arrow_right.gif" alt=">>" style="margin-top: 5px">
<a href="Sharing%20over%20Network.html">Общий доступ по сети</a>
</p></div>

<div class="wikidoc">
<h1>Общий доступ по сети</h1>
<p>Если требуется доступ к одному и тому же тому VeraCrypt одновременно из нескольких операционных
систем, возможны два варианта:</p>
<ol>
<li>Том VeraCrypt монтируется только на одном компьютере (скажем, на сервере), а по сети совместно используется
только содержимое смонтированного тома VeraCrypt (то есть файловая система внутри тома VeraCrypt). Пользователи
других компьютеров или систем не монтируют том (он уже смонтирован на сервере).
<p><strong>Преимущества</strong><span>: все пользователи могут записывать данные в том VeraCrypt. Совместно
используемый том может быть как на основе файла, так и на основе раздела/устройства.</span></p>
<p><strong>Недостаток</strong><span>: пересылаемые по сети данные не будут зашифрованными. Тем не менее их
всё-таки можно шифровать с помощью SSL, TLS, VPN и других технологий.</span></p>
<p><strong>Замечания</strong>: примите к сведению, что при перезагрузке системы общий сетевой ресурс будет
автоматически восстановлен только в случае, если это системный избранный том или зашифрованный системный
раздел/диск (о том, как сделать том системным избранным томом, см. в главе <a href="System%20Favorite%20Volumes.html">
<em>Системные избранные тома</em></a>).</p>
</li><li>Размонтированный файловый контейнер VeraCrypt хранится на одном компьютере (скажем, на сервере).
Этот зашифрованный файл доступен по сети. Пользователи других компьютеров или систем будут локально монтировать
этот общий файл. Таким образом, том будет монтироваться одновременно в нескольких операционных системах.
<p><strong>Преимущество</strong><span>: пересылаемые по сети данные будут зашифрованными (тем не менее, их всё же
рекомендуется шифровать с помощью SSL, TLS, VPN или других подходящих технологий, чтобы ещё сильнее осложнить
анализ трафика и сохранить целостность данных).</span></p>
<p><strong>Недостатки</strong>: общий том может быть только на основе файла-контейнера (но не на основе
раздела/устройства). В каждой из систем том должен монтироваться в режиме только для чтения (о том, как
смонтировать том в режиме только для чтения, см. раздел <em>Параметры монтирования</em>). Обратите внимание,
что это требование относится и к незашифрованным томам. Одна из причин, например, в том, что данные, считанные
из обычной файловой системы в среде одной ОС, в это же время могут быть изменены другой ОС, и потому
окажутся несогласованными (что может привести к повреждению данных).</p>
</li></ol>
</div><div class="ClearBoth"></div></body></html>
