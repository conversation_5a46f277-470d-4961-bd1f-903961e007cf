﻿<!DOCTYPE html>
<html lang="ru">
<head>
<meta http-equiv="content-type" content="text/html; charset=utf-8" />
<title>VeraCrypt - Бесплатное надёжное шифрование дисков с открытым исходным кодом</title>
<meta name="description" content="VeraCrypt это бесплатное программное обеспечение для шифрования дисков с открытым исходным кодом для Windows, Mac OS X (macOS) и Linux. В случае, если злоумышленник вынуждает вас раскрыть пароль, VeraCrypt обеспечивает правдоподобное отрицание наличия шифрования. В отличие от пофайлового шифрования, VeraCrypt шифрует данные в реальном времени (на лету), автоматически, прозрачно, требует очень мало памяти и не использует временные незашифрованные файлы."/>
<meta name="keywords" content="encryption, security, шифрование, безопасность"/>
<link href="styles.css" rel="stylesheet" type="text/css" />
</head>
<body>

<div>
<a href="Documentation.html"><img src="VeraCrypt128x128.png" alt="VeraCrypt"/></a>
</div>

<div id="menu">
	<ul>
	  <li><a href="Home.html">Начало</a></li>
	  <li><a href="Code.html">Исходный код</a></li>
	  <li><a href="Downloads.html">Загрузить</a></li>
	  <li><a class="active" href="Documentation.html">Документация</a></li>
	  <li><a href="Donation.html">Поддержать разработку</a></li>
	  <li><a href="https://sourceforge.net/p/veracrypt/discussion/" target="_blank">Форум</a></li>
	</ul>
</div>

<div>
<p>
<a href="Documentation.html">Документация</a>
<img src="arrow_right.gif" alt=">>" style="margin-top: 5px">
<a href="Technical%20Details.html">Технические подробности</a>
<img src="arrow_right.gif" alt=">>" style="margin-top: 5px">
<a href="Random%20Number%20Generator.html">Генератор случайных чисел</a>
</p></div>

<div class="wikidoc">
<h1>Генератор случайных чисел</h1>
<p>Для генерирования мастер-ключа шифрования, вторичного ключа (режим XTS), соли и ключевых файлов в VeraCrypt
используется генератор случайных чисел (RNG). Он создаёт в ОЗУ (оперативной памяти компьютера) пул из случайных
значений. Этот пул размером 320 байт заполняется данными, получаемыми из следующих источников:</p>
<ul>
<li>перемещения мыши</li>
<li>нажатия клавиш</li>
<li><em>Mac OS X и Linux</em>: значения, генерируемые встроенным RNG (оба – <em>/dev/random</em> и <em>/dev/urandom</em>)</li>
<li><em>только Microsoft Windows</em>: Windows CryptoAPI (регулярно собираются с интервалом 500 мс)</li>
<li><em>только Microsoft Windows</em>: статистика сетевого интерфейса (NETAPI32) </li>
<li><em>только Microsoft Windows</em>: различные дескрипторы Win32, переменные времени и счётчики (регулярно собираются с интервалом 500 мс)</li>
</ul>
<p>Прежде чем значение, полученное из любого вышеуказанного источника, будет записано в пул, оно разделяется на
отдельные байты (например, 32-битовое число делится на четыре байта). Затем эти байты индивидуально записываются
в пул операцией сложения по модулю 2<sup>8</sup> (не заменяя старые значения в пуле) в позиции указателя пула.
После записи байта позиция указателя пула перемещается на один байт вперёд. Когда указатель достигает конца пула,
его позиция устанавливается в начало пула. После записи в пул каждого шестнадцатого байта, ко всему пулу автоматически
применяется функция перемешивания (см. ниже).</p>
<h2>Функция перемешивания пула</h2>
<p>Назначение этой функции – выполнение диффузии [2]. Диффузия максимально распространяет (рассеивает) влияние
индивидуальных "необработанных" ("raw") входных бит по пулу, что также скрывает статистические зависимости.
После записи в пул каждого шестнадцатого байта, эта функция применяется ко всему пулу.</p>
<p>Описание функции перемешивания пула:</p>
<ol>
<li>Пусть <em>R</em> это пул случайных значений</li>
<li>Пусть <em>H</em> это выбранная пользователем функция хеширования (SHA-512, BLAKE2S-256 или Whirlpool)</li>
<li><em>l</em> = байтовый размер вывода функции хеширования <em>H</em> (то есть если
<em>H</em> – BLAKE2S-256, то <em>l</em> = 20; если <em>H</em> – SHA-512, то <em>l</em> = 64)</li>
<li><em>z</em> = байтовый размер пула случайных значений <em>R </em>(320 байт)</li>
<li><em>q</em> = <em>z</em> / <em>l</em> &ndash; 1 (например, если <em>H</em> – Whirlpool, то
<em>q</em> = 4)</li>
<li><i>R</i> это поделённые на <i>l</i> байт блоки B<sub>0</sub>...B<sub>q</sub>
<p>Для 0 &le; <i>i</i> &le; <i>q</i> (то есть для каждого блока <i>B</i>) выполняются следующие шаги:</p>
<ol type="a">
<li><em>M = H</em> (<em>B</em><sub>0</sub> || <em>B</em><sub>1</sub> || ... || <em>B</em><sub>q</sub>)
[то есть пул случайных значений хешируется с помощью хеш-функции <i>H</i>, что даёт хеш <i>M</i>]</li>
<li><i>B</i><sub>i</sub> = <i>B</i><sub>i</sub> ^ <i>M</i></li></ol></li>
<li><em>R = B</em><sub>0</sub> || <em>B</em><sub>1</sub> || ... || <em>B</em><sub>q</sub></li></ol>
<p>Например, если <em>q</em> = 1, то пул случайных значений будет перемешан следующим образом:</p>
<ol>
<li>(<em>B</em><sub>0</sub> || <em>B</em><sub>1</sub>) = <em>R</em></li>
<li><em>B</em><sub>0</sub> = <em>B</em><sub>0</sub> ^ <em>H</em>(<em>B</em><sub>0</sub> || <em>B</em><sub>1</sub>) </li>
<li><em>B</em><sub>1</sub> = <em>B</em><sub>1</sub> ^ <em>H</em>(<em>B</em><sub>0</sub> || <em>B</em><sub>1</sub>) </li>
<li><em>R</em> = <em>B</em><sub>0</sub> || <em>B</em><sub>1</sub> </li></ol>
<h2>Создаваемые значения</h2>
<p>Содержимое пула RNG никогда прямо не экспортируется (даже когда VeraCrypt даёт RNG инструкцию сгенерировать
и экспортировать значение). Таким образом, даже если неприятель завладеет созданным RNG значением, это ему
никак не поможет в определении или предсказании (с помощью полученного значения) любых других значений,
созданных RNG в течение сеанса (определить содержимое пула, основываясь на сгенерированном RNG значении, невозможно).</p>
<p>Генератор случайных чисел обеспечивает это, выполняя следующие шаги всякий раз, когда VeraCrypt даёт
указание сгенерировать и экспортировать значение:</p>
<ol>
<li>Данные, полученные из перечисленных выше источников, добавляются в пул, как описано выше.</li>
<li>Запрошенное число байт копируется из пула в выходной буфер (копирование начинается с позиции указателя пула;
по достижении конца пула копирование продолжается с начала пула; если запрошенное число байт больше размера пула,
значение не генерируется и возвращается ошибка).</li>
<li>Состояние каждого бита в пуле инвертируется (то есть 0 становится 1, а 1 становится 0).</li>
<li>Данные, полученные из какого-либо перечисленного выше источника, добавляются в пул, как описано выше.</li>
<li>Содержимое пула трансформируется с помощью функции перемешивания пула.<br>
Примечание. Эта функция использует криптографически стойкую одностороннюю хеш-функцию, выбираемую пользователем
(подробности см. выше в разделе <em>Функция перемешивания пула</em>).</li>
<li>Преобразованное содержимое пула подвергается операции XOR в выходной буфер следующим образом:
<ol type="a">
<li>Указатель записи в выходном буфере устанавливается в 0 (первый байт буфера).</li>
<li>Байт в позиции курсора пула считывается из пула и подвергается операции XOR с байтом в выходном буфере
в позиции курсора записи выходного буфера.</li>
<li>Позиция указателя пула смещается вперёд на один байт. По достижении конца пула позиция указателя
устанавливается в 0 (первый байт пула).</li>
<li>Позиция указателя записи выходного буфера перемещается вперёд на один байт. </li>
<li>Шаги b&ndash;d повторяются для каждого остающегося байта в выходном буфере (чья длина равна запрошенному
числу байт).</li>
<li>Содержимое выходного буфера, являющееся окончательным значением, сгенерированным RNG, экспортируется.</li>
</ol>
</li></ol>
<h2>Первоисточники</h2>
<p>Дизайн и реализация генератора случайных чисел основаны на следующих работах:</p>
<ul>
<li><i>Software Generation of Practically Strong Random Numbers</i>, автор Peter Gutmann [10]</li>
<li><i>Cryptographic Random Numbers</i>, автор Carl Ellison [11]</li></ul>
<p>&nbsp;</p>
<p><a href="Keyfiles.html" style="text-align:left; color:#0080c0; text-decoration:none; font-weight:bold">Следующий раздел &gt;&gt;</a></p>
</div><div class="ClearBoth"></div></body></html>
