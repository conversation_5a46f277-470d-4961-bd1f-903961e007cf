<!DOCTYPE html>
<html lang="en">
<head>
<meta http-equiv="content-type" content="text/html; charset=utf-8" />
<title>VeraCrypt - Free Open source disk encryption with strong security for the Paranoid</title>
<meta name="description" content="VeraCrypt is free open-source disk encryption software for Windows, Mac OS X and Linux. In case an attacker forces you to reveal the password, VeraCrypt provides plausible deniability. In contrast to file encryption, data encryption performed by VeraCrypt is real-time (on-the-fly), automatic, transparent, needs very little memory, and does not involve temporary unencrypted files."/>
<meta name="keywords" content="encryption, security"/>
<link href="styles.css" rel="stylesheet" type="text/css" />
</head>
<body>

<div>
<a href="Documentation.html"><img src="VeraCrypt128x128.png" alt="VeraCrypt"/></a>
</div>

<div id="menu">
	<ul>
	  <li><a href="Home.html">Home</a></li>
	  <li><a href="Code.html">Source Code</a></li>
	  <li><a href="Downloads.html">Downloads</a></li>
	  <li><a class="active" href="Documentation.html">Documentation</a></li>
	  <li><a href="Donation.html">Donate</a></li>
	  <li><a href="https://sourceforge.net/p/veracrypt/discussion/" target="_blank">Forums</a></li>
	</ul>
</div>

<div>
<p>
<a href="Documentation.html">Documentation</a>
<img src="arrow_right.gif" alt=">>" style="margin-top: 5px">
<a href="Miscellaneous.html">Miscellaneous</a>
<img src="arrow_right.gif" alt=">>" style="margin-top: 5px">
<a href="Removable%20Medium%20Volume.html">Removable Medium Volume</a>
</p></div>

<div class="wikidoc">
<h2>Volume Mounted as Removable Medium</h2>
<p>This section applies to VeraCrypt volumes mounted when one of the following options is enabled (as applicable):</p>
<ul>
<li><em>Settings</em> &gt; <em>Preferences</em> &gt; <em>Mount volumes as removable media</em>
</li><li><em>Mount Options</em> &gt; <em>Mount volume as removable medium</em> </li><li><em>Favorites</em> &gt; <em>Organize Favorite Volumes</em> &gt; <em>Mount selected volume as removable medium</em>
</li><li><em>Favorites</em> &gt; <em>Organize System Favorite Volumes</em> &gt; <em>Mount selected volume as removable medium</em>
</li></ul>
<p>VeraCrypt Volumes that are mounted as removable media have the following advantages and disadvantages:</p>
<ul>
<li>Windows is prevented from automatically creating the &lsquo;<em>Recycled</em>&rsquo; and/or the &lsquo;<em>System Volume Information</em>&rsquo; folders on VeraCrypt volumes (in Windows, these folders are used by the Recycle Bin and System Restore features).
</li><li>Windows 8 and later is prevented from writing an Event 98 to the Events Log that contains the device name (\\device\VeraCryptVolumeXX) of VeraCrypt volumes formatted using NTFS. This event log &quot;feature&quot; was introduced in Windows 8 as part of newly introduced
 NTFS health checks as <a href="https://blogs.msdn.microsoft.com/b8/2012/05/09/redesigning-chkdsk-and-the-new-ntfs-health-model/" target="_blank">
explained here</a>. Big thanks to Liran Elharar for discovering this. </li><li>Windows may use caching methods and write delays that are normally used for removable media (for example, USB flash drives). This might slightly decrease the performance but at the same increase the likelihood that it will be possible to unmount the volume
 quickly without having to force the unmount. </li><li>The operating system may tend to keep the number of handles it opens to such a volume to a minimum. Hence, volumes mounted as removable media might require fewer forced unmounts than other volumes.
</li><li>Under Windows Vista and earlier, the &lsquo;<em>Computer</em>&rsquo; (or &lsquo;<em>My Computer</em>&rsquo;) list does not show the amount of free space on volumes mounted as removable (note that this is a Windows limitation, not a bug in VeraCrypt).
</li><li>Under desktop editions of Windows Vista or later, sectors of a volume mounted as removable medium may be accessible to all users (including users without administrator privileges; see section
<a href="Multi-User%20Environment.html">
<em>Multi-User Environment</em></a>). </li></ul>
</div><div class="ClearBoth"></div></body></html>
