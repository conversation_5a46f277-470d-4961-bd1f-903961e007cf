<!DOCTYPE html>
<html lang="en">
<head>
<meta http-equiv="content-type" content="text/html; charset=utf-8" />
<title>VeraCrypt - Free Open source disk encryption with strong security for the Paranoid</title>
<meta name="description" content="VeraCrypt is free open-source disk encryption software for Windows, Mac OS X and Linux. In case an attacker forces you to reveal the password, VeraCrypt provides plausible deniability. In contrast to file encryption, data encryption performed by VeraCrypt is real-time (on-the-fly), automatic, transparent, needs very little memory, and does not involve temporary unencrypted files."/>
<meta name="keywords" content="encryption, security"/>
<link href="styles.css" rel="stylesheet" type="text/css" />
</head>
<body>

<div>
<a href="Documentation.html"><img src="VeraCrypt128x128.png" alt="VeraCrypt"/></a>
</div>

<div id="menu">
	<ul>
	  <li><a href="Home.html">Home</a></li>
	  <li><a href="Code.html">Source Code</a></li>
	  <li><a href="Downloads.html">Downloads</a></li>
	  <li><a class="active" href="Documentation.html">Documentation</a></li>
	  <li><a href="Donation.html">Donate</a></li>
	  <li><a href="https://sourceforge.net/p/veracrypt/discussion/" target="_blank">Forums</a></li>
	</ul>
</div>

<div>
<p>
<a href="Documentation.html">Documentation</a>
<img src="arrow_right.gif" alt=">>" style="margin-top: 5px">
<a href="FAQ.html">Frequently Asked Questions</a>
</p></div>

<div class="wikidoc">
<h1>Frequently Asked Questions</h1>
<div style="text-align:left; margin-bottom:19px; padding-top:0px; padding-bottom:0px; margin-top:0px">
Last Updated July 2nd, 2017</div>
<div style="text-align:left; margin-top:19px; margin-bottom:19px; padding-top:0px; padding-bottom:0px">
<em style="text-align:left">This document is not guaranteed to be error-free and is provided &quot;as is&quot; without warranty of any kind. For more information, see
<a href="Disclaimers.html" target="_blank" style="text-align:left; color:#0080c0; text-decoration:none">
Disclaimers</a>.</em></div>
<div style="text-align:left; margin-top:19px; margin-bottom:19px; padding-top:0px; padding-bottom:0px">
<div style="text-align:left; margin-top:19px; margin-bottom:19px; padding-top:0px; padding-bottom:0px">
<strong style="text-align:left">Can TrueCrypt and VeraCrypt be running on the same machine?</strong></div>
<div style="text-align:left; margin-top:19px; margin-bottom:19px; padding-top:0px; padding-bottom:0px">
Yes. There are generally no conflicts between TrueCrypt and VeraCrypt, thus they can be installed and used on the same machine. On Windows however, if they are both used to mount the same volume, two drives may appear when mounting it. This can be solved by
 running the following command in an elevated command prompt (using Run as an administrator) before mounting any volume:
<strong>mountvol.exe /r</strong>.</div>
<div style="text-align:left; margin-top:19px; margin-bottom:19px; padding-top:0px; padding-bottom:0px">
<div style="text-align:left; margin-top:19px; margin-bottom:19px; padding-top:0px; padding-bottom:0px">
<strong style="text-align:left">Can I use my TrueCrypt volumes in VeraCrypt?</strong></div>
Yes. Starting from version 1.0f, VeraCrypt supports mounting TrueCrypt volumes.</div>
<div style="text-align:left; margin-top:19px; margin-bottom:19px; padding-top:0px; padding-bottom:0px">
<div style="text-align:left; margin-top:19px; margin-bottom:19px; padding-top:0px; padding-bottom:0px">
<strong style="text-align:left">Can I convert my TrueCrypt volumes to VeraCrypt format?</strong></div>
Yes. Starting from version 1.0f, VeraCrypt offers the possibility to convert TrueCrypt containers and non-system partitions to VeraCrypt format. This can achieved using the &quot;Change Volume Password&quot; or &quot;Set Header Key Derivation Algorithm&quot; actions. Just check
 the &quot;TrueCrypt Mode&quot;, enter you TrueCrypt password and perform the operation. After that, you volume will have the VeraCrypt format.<br>
Before doing the conversion, it is advised to backup the volume header using TrueCrypt. You can delete this backup safely once the conversion is done and after checking that the converted volume is mounted properly by VeraCrypt.</div>
<div style="text-align:left; margin-top:19px; margin-bottom:19px; padding-top:0px; padding-bottom:0px">
<div style="text-align:left; margin-top:19px; margin-bottom:19px; padding-top:0px; padding-bottom:0px">
<strong style="text-align:left">What's the difference between TrueCrypt and VeraCrypt?</strong></div>
<div style="text-align:left; margin-top:19px; margin-bottom:19px; padding-top:0px; padding-bottom:0px">
VeraCrypt adds enhanced security to the algorithms used for system and partitions encryption making it immune to new developments in brute-force attacks.<br>
It also solves many vulnerabilities and security issues found in TrueCrypt.<br>
As an example, when the system partition is encrypted, TrueCrypt uses PBKDF2-RIPEMD160 with 1000 iterations whereas in VeraCrypt we use
<span style="text-decoration:underline">327661</span>. And for standard containers and other partitions, TrueCrypt uses at most 2000 iterations but VeraCrypt uses
<span style="text-decoration:underline">500000 </span>iterations.<br>
This enhanced security adds some delay only to the opening of encrypted partitions without any performance impact to the application use phase. This is acceptable to the legitimate owner but it makes it much harder for an attacker to gain access to the encrypted
 data.</div>
</div>
<br id="PasswordLost" style="text-align:left">
<strong style="text-align:left">I forgot my password &ndash; is there any way ('backdoor') to recover the files from my VeraCrypt volume?</strong></div>
<div style="text-align:left; margin-top:19px; margin-bottom:19px; padding-top:0px; padding-bottom:0px">
We have not implemented any 'backdoor' in VeraCrypt (and will never implement any even if asked to do so by a government agency), because it would defeat the purpose of the software. VeraCrypt does not allow decryption of data without knowing the correct password
 or key. We cannot recover your data because we do not know and cannot determine the password you chose or the key you generated using VeraCrypt. The only way to recover your files is to try to &quot;crack&quot; the password or the key, but it could take thousands or
 millions of years (depending on the length and quality of the password or keyfiles, on the software/hardware performance, algorithms, and other factors). Back in 2010, there was news about the
<a href="http://www.webcitation.org/query?url=g1.globo.com/English/noticia/2010/06/not-even-fbi-can-de-crypt-files-daniel-dantas.html" target="_blank">
FBI failing to decrypt a TrueCrypt volume after a year of trying</a>. While we can't verify if this is true or just a &quot;psy-op&quot; stunt, in VeraCrypt we have increased the security of the key derivation to a level where any brute-force of the password is virtually
 impossible, provided that all security requirements are respected.</div>
<div style="text-align:left; margin-top:19px; margin-bottom:19px; padding-top:0px; padding-bottom:0px">
<br style="text-align:left">
<strong style="text-align:left">Is there a &quot;Quick Start Guide&quot; or some tutorial for beginners?</strong></div>
<div style="text-align:left; margin-top:19px; margin-bottom:19px; padding-top:0px; padding-bottom:0px">
Yes. The first chapter, <strong style="text-align:left"><a href="Beginner%27s%20Tutorial.html" target="_blank" style="text-align:left; color:#0080c0; text-decoration:none">Beginner's Tutorial</a></strong>, in the VeraCrypt
 User Guide contains screenshots and step-by-step instructions on how to create, mount, and use a VeraCrypt volume.</div>
<div style="text-align:left; margin-top:19px; margin-bottom:19px; padding-top:0px; padding-bottom:0px">
<br style="text-align:left">
<strong style="text-align:left">Can I encrypt a partition/drive where Windows is installed?</strong></div>
<div style="text-align:left; margin-top:19px; margin-bottom:19px; padding-top:0px; padding-bottom:0px">
Yes, see the chapter <a href="System%20Encryption.html" target="_blank" style="text-align:left; color:#0080c0; text-decoration:none">
System Encryption</a> in the VeraCrypt User Guide.</div>
<div id="BootingHang" style="text-align:left; margin-top:19px; margin-bottom:19px; padding-top:0px; padding-bottom:0px">
<strong>The system encryption Pre Test fails because the bootloader hangs with the messaging &quot;booting&quot; after successfully verifying the password. How to make the Pre Test succeed?</strong></div>
<div style="text-align:left; margin-top:19px; margin-bottom:19px; padding-top:0px; padding-bottom:0px">
There two known workarounds for this issue (Both require having a Windows Installation disk):</div>
<div style="text-align:left; margin-top:19px; margin-bottom:19px; padding-top:0px; padding-bottom:0px">
<ol>
<li>Boot your machine using a Windows Installation disk and select to repair your computer. Choose &quot;Command Prompt&quot; option and when it opens, type the commands below and then restart your system:
<ul>
<li>BootRec /fixmbr </li><li>BootRec /FixBoot </li></ul>
</li><li>Delete the 100 MB System Reserved partition located at the beginning of your drive and set the system partition next to it as the active partition (both can be done using diskpart utility available in Windows Installation disk repair option). After that,
 run Startup Repair after rebooting on Windows Installation disk. The following link contains detailed instructions:
<a href="https://www.sevenforums.com/tutorials/71363-system-reserved-partition-delete.html" target="_blank">
https://www.sevenforums.com/tutorials/71363-system-reserved-partition-delete.html</a>
</li></ol>
</div>
<div style="text-align:left; margin-top:19px; margin-bottom:19px; padding-top:0px; padding-bottom:0px">
<div id="PreTestFail" style="text-align:left; margin-top:19px; margin-bottom:19px; padding-top:0px; padding-bottom:0px">
<strong>The system encryption Pre Test fails even though the password was correctly entered in the bootloader. How to make the Pre Test succeed?</strong></div>
<div style="text-align:left; margin-top:19px; margin-bottom:19px; padding-top:0px; padding-bottom:0px">
This can be caused by the TrueCrypt driver that clears BIOS memory before VeraCrypt is able to read it. In this case, uninstalling TrueCrypt solves the issue.<br>
This can also be caused by some hardware drivers and other software that access BIOS memory. There is no generic solution for this and affected users should identify such software and remove it from the system.</div>
<div style="text-align:left; margin-top:19px; margin-bottom:19px; padding-top:0px; padding-bottom:0px">
<br style="text-align:left">
<strong style="text-align:left">Can I directly play a video (.avi, .mpg, etc.) stored on a VeraCrypt volume?</strong></div>
</div>
<div style="text-align:left; margin-top:19px; margin-bottom:19px; padding-top:0px; padding-bottom:0px">
Yes, VeraCrypt-encrypted volumes are like normal disks. You provide the correct password (and/or keyfile) and mount (open) the VeraCrypt volume. When you double click the icon of the video file, the operating system launches the application associated with
 the file type &ndash; typically a media player. The media player then begins loading a small initial portion of the video file from the VeraCrypt-encrypted volume to RAM (memory) in order to play it. While the portion is being loaded, VeraCrypt is automatically
 decrypting it (in RAM). The decrypted portion of the video (stored in RAM) is then played by the media player. While this portion is being played, the media player begins loading another small portion of the video file from the VeraCrypt-encrypted volume to
 RAM (memory) and the process repeats.<br style="text-align:left">
<br style="text-align:left">
The same goes for video recording: Before a chunk of a video file is written to a VeraCrypt volume, VeraCrypt encrypts it in RAM and then writes it to the disk. This process is called on-the-fly encryption/decryption and it works for all file types (not only
 for video files).</div>
<div style="text-align:left; margin-top:19px; margin-bottom:19px; padding-top:0px; padding-bottom:0px">
<br style="text-align:left">
<strong style="text-align:left">Will VeraCrypt be open-source and free forever?</strong></div>
<div style="text-align:left; margin-top:19px; margin-bottom:19px; padding-top:0px; padding-bottom:0px">
Yes, it will. We will never create a commercial version of VeraCrypt, as we believe in open-source and free security software.</div>
<div style="text-align:left; margin-top:19px; margin-bottom:19px; padding-top:0px; padding-bottom:0px">
<br style="text-align:left">
<strong style="text-align:left">Is it possible to donate to the VeraCrypt project?</strong></div>
<div style="text-align:left; margin-top:19px; margin-bottom:19px; padding-top:0px; padding-bottom:0px">
Yes. You can use the donation buttons at <a href="https://veracrypt.jp/en/Donation.html" target="_blank">
https://veracrypt.jp/en/Donation.html</a>.</div>
<div style="text-align:left; margin-top:19px; margin-bottom:19px; padding-top:0px; padding-bottom:0px">
<br style="text-align:left">
<strong style="text-align:left">Why is VeraCrypt open-source? What are the advantages?</strong></div>
<div style="text-align:left; margin-top:19px; margin-bottom:19px; padding-top:0px; padding-bottom:0px">
As the source code for VeraCrypt is publicly available, independent researchers can verify that the source code does not contain any security flaw or secret 'backdoor'. If the source code were not available, reviewers would need to reverse-engineer the executable
 files. However, analyzing and understanding such reverse-engineered code is so difficult that it is practically
<em style="text-align:left">impossible</em> to do (especially when the code is as large as the VeraCrypt code).<br style="text-align:left">
<br style="text-align:left">
Remark: A similar problem also affects cryptographic hardware (for example, a self-encrypting storage device). It is very difficult to reverse-engineer it to verify that it does not contain any security flaw or secret 'backdoor'.</div>
<div style="text-align:left; margin-top:19px; margin-bottom:19px; padding-top:0px; padding-bottom:0px">
<br style="text-align:left">
<strong style="text-align:left">VeraCrypt is open-source, but has anybody actually reviewed the source code?</strong></div>
<div style="text-align:left; margin-top:19px; margin-bottom:19px; padding-top:0px; padding-bottom:0px">
Yes. An <a href="http://blog.quarkslab.com/security-assessment-of-veracrypt-fixes-and-evolutions-from-truecrypt.html" target="_blank">
audit</a> has been performed by <a href="https://quarkslab.com/" target="_blank">
Quarkslab</a>. The technical report can be downloaded from <a href="http://blog.quarkslab.com/resources/2016-10-17-audit-veracrypt/16-08-215-REP-VeraCrypt-sec-assessment.pdf">here</a>. VeraCrypt 1.19 addressed the issues found by this audit.</div>
<div style="text-align:left; margin-top:19px; margin-bottom:19px; padding-top:0px; padding-bottom:0px">
<br style="text-align:left">
<strong style="text-align:left">As VeraCrypt is open-source software, independent researchers can verify that the source code does not contain any security flaw or secret 'backdoor'. Can they also verify that the official executable files were built from the
 published source code and contain no additional code?</strong></div>
<div style="text-align:left; margin-top:19px; margin-bottom:19px; padding-top:0px; padding-bottom:0px">
Yes, they can. In addition to reviewing the source code, independent researchers can compile the source code and compare the resulting executable files with the official ones. They may find some differences (for example, timestamps or embedded digital signatures)
 but they can analyze the differences and verify that they do not form malicious code.</div>
<div id="UsbFlashDrive" style="text-align:left; margin-top:19px; margin-bottom:19px; padding-top:0px; padding-bottom:0px">
<br style="text-align:left">
<strong style="text-align:left">How can I use VeraCrypt on a USB flash drive? </strong>
</div>
<div style="text-align:left; margin-top:19px; margin-bottom:19px; padding-top:0px; padding-bottom:0px">
You have three options:</div>
<ol style="text-align:left; margin-top:18px; margin-bottom:19px; padding-top:0px; padding-bottom:0px">
<li style="text-align:left; margin-top:0px; margin-bottom:0px; padding-top:0px; padding-bottom:0px">
Encrypt the entire USB flash drive. However, you will not be able run VeraCrypt from the USB flash drive.
</li><li style="text-align:left; margin-top:0px; margin-bottom:0px; padding-top:0px; padding-bottom:0px">
Create two or more partitions on your USB flash drive. Leave the first partition non encrypted and encrypt the other partition(s). You can store VeraCrypt on the first partition in order to run it directly from the USB flash drive.<br style="text-align:left">
Note: Windows can only access the primary partition of a USB flash drive, nevertheless the extra partitions remain accessible through VeraCrypt.
</li><li style="text-align:left; margin-top:0px; margin-bottom:0px; padding-top:0px; padding-bottom:0px">
Create a VeraCrypt file container on the USB flash drive (for information on how to do so, see the chapter
<strong style="text-align:left"><a href="Beginner%27s%20Tutorial.html" style="text-align:left; color:#0080c0; text-decoration:none">Beginner's Tutorial</a></strong>, in the
<a href="https://veracrypt.jp/en/Documentation.html" target="_blank" style="text-align:left; color:#0080c0; text-decoration:none">
VeraCrypt User Guide</a>). If you leave enough space on the USB flash drive (choose an appropriate size for the VeraCrypt container), you will also be able to store VeraCrypt on the USB flash drive (along with the container &ndash; not
<em style="text-align:left">in</em> the container) and you will be able to run VeraCrypt from the USB flash drive (see also the chapter
<a href="Portable%20Mode.html" target="_blank" style="text-align:left; color:#0080c0; text-decoration:none">
Portable Mode</a> in the <a href="https://veracrypt.jp/en/Documentation.html" target="_blank" style="text-align:left; color:#0080c0; text-decoration:none">
VeraCrypt User Guide</a>). </li></ol>
<div style="text-align:left; margin-top:19px; margin-bottom:19px; padding-top:0px; padding-bottom:0px">
<br style="text-align:left">
<strong style="text-align:left">Does VeraCrypt also encrypt file names and folder names?
</strong></div>
<div style="text-align:left; margin-top:19px; margin-bottom:19px; padding-top:0px; padding-bottom:0px">
Yes. The entire file system within a VeraCrypt volume is encrypted (including file names, folder names, and contents of every file). This applies to both types of VeraCrypt volumes &ndash; i.e., to file containers (virtual VeraCrypt disks) and to VeraCrypt-encrypted
 partitions/devices.</div>
<div style="text-align:left; margin-top:19px; margin-bottom:19px; padding-top:0px; padding-bottom:0px">
<br style="text-align:left">
<strong style="text-align:left">Does VeraCrypt use parallelization?</strong></div>
<div style="text-align:left; margin-top:19px; margin-bottom:19px; padding-top:0px; padding-bottom:0px">
Yes. Increase in encryption/decryption speed is directly proportional to the number of cores/processors your computer has. For more information, please see the chapter
<a href="Parallelization.html" target="_blank" style="text-align:left; color:#0080c0; text-decoration:none">
Parallelization</a> in the <a href="https://veracrypt.jp/en/Documentation.html" target="_blank" style="text-align:left; color:#0080c0; text-decoration:none">
documentation</a>.</div>
<div style="text-align:left; margin-top:19px; margin-bottom:19px; padding-top:0px; padding-bottom:0px">
<br style="text-align:left">
<strong style="text-align:left">Can data be read from and written to an encrypted volume/drive as fast as if the drive was not encrypted?</strong></div>
<div style="text-align:left; margin-top:19px; margin-bottom:19px; padding-top:0px; padding-bottom:0px">
Yes, since VeraCrypt uses pipelining and parallelization. For more information, please see the chapters
<a href="Pipelining.html" target="_blank" style="text-align:left; color:#0080c0; text-decoration:none">
Pipelining</a> and <a href="Parallelization.html" target="_blank" style="text-align:left; color:#0080c0; text-decoration:none">
Parallelization</a> in the <a href="https://veracrypt.jp/en/Documentation.html" style="text-align:left; color:#0080c0; text-decoration:none">
documentation</a>.</div>
<div style="text-align:left; margin-top:19px; margin-bottom:19px; padding-top:0px; padding-bottom:0px">
<br style="text-align:left">
<strong style="text-align:left">Does VeraCrypt support hardware-accelerated encryption?</strong></div>
<div style="text-align:left; margin-top:19px; margin-bottom:19px; padding-top:0px; padding-bottom:0px">
Yes. For more information, please see the chapter <a href="Hardware%20Acceleration.html" target="_blank" style="text-align:left; color:#0080c0; text-decoration:none">
Hardware Acceleration</a> in the <a href="https://veracrypt.jp/en/Documentation.html" target="_blank" style="text-align:left; color:#0080c0; text-decoration:none">
documentation</a>.</div>
<div style="text-align:left; margin-top:19px; margin-bottom:19px; padding-top:0px; padding-bottom:0px">
<br style="text-align:left">
<strong style="text-align:left">Is it possible to boot Windows installed in a hidden VeraCrypt volume?</strong></div>
<div style="text-align:left; margin-top:19px; margin-bottom:19px; padding-top:0px; padding-bottom:0px">
Yes, it is. For more information, please see the section <a href="Hidden%20Operating%20System.html" target="_blank" style="text-align:left; color:#0080c0; text-decoration:none">
Hidden Operating System</a> in the <a href="https://veracrypt.jp/en/Documentation.html" target="_blank" style="text-align:left; color:#0080c0; text-decoration:none">
documentation</a>.</div>
<div style="text-align:left; margin-top:19px; margin-bottom:19px; padding-top:0px; padding-bottom:0px">
<br style="text-align:left">
<strong style="text-align:left">Will I be able to mount my VeraCrypt volume (container) on any computer?</strong></div>
<div style="text-align:left; margin-top:19px; margin-bottom:19px; padding-top:0px; padding-bottom:0px">
Yes, <a href="VeraCrypt%20Volume.html" target="_blank" style="text-align:left; color:#0080c0; text-decoration:none">
VeraCrypt volumes</a> are independent of the operating system. You will be able to mount your VeraCrypt volume on any computer on which you can run VeraCrypt (see also the question '<em style="text-align:left">Can I use VeraCrypt on Windows if I do not have
 administrator privileges?</em>').</div>
<div style="text-align:left; margin-top:19px; margin-bottom:19px; padding-top:0px; padding-bottom:0px">
<br style="text-align:left">
<strong style="text-align:left">Can I unplug or turn off a hot-plug device (for example, a USB flash drive or USB hard drive) when there is a mounted VeraCrypt volume on it?</strong></div>
<div style="text-align:left; margin-top:19px; margin-bottom:19px; padding-top:0px; padding-bottom:0px">
Before you unplug or turn off the device, you should always unmount the VeraCrypt volume in VeraCrypt first, and then perform the '<em style="text-align:left">Eject</em>' operation if available (right-click the device in the '<em style="text-align:left">Computer</em>'
 or '<em style="text-align:left">My Computer</em>' list), or use the '<em style="text-align:left">Safely Remove Hardware</em>' function (built in Windows, accessible via the taskbar notification area). Otherwise, data loss may occur.</div>
<div style="text-align:left; margin-top:19px; margin-bottom:19px; padding-top:0px; padding-bottom:0px">
<br style="text-align:left">
<strong style="text-align:left">What is a hidden operating system?</strong></div>
<div style="text-align:left; margin-top:19px; margin-bottom:19px; padding-top:0px; padding-bottom:0px">
See the section <a href="Hidden%20Operating%20System.html" target="_blank" style="text-align:left; color:#0080c0; text-decoration:none">
Hidden Operating System</a> in the <a href="https://veracrypt.jp/en/Documentation.html" target="_blank" style="text-align:left; color:#0080c0; text-decoration:none">
documentation</a>.</div>
<div style="text-align:left; margin-top:19px; margin-bottom:19px; padding-top:0px; padding-bottom:0px">
<br style="text-align:left">
<strong style="text-align:left">What is plausible deniability?</strong></div>
<div style="text-align:left; margin-top:19px; margin-bottom:19px; padding-top:0px; padding-bottom:0px">
See the chapter <a href="Plausible%20Deniability.html" target="_blank" style="text-align:left; color:#0080c0; text-decoration:none">
Plausible Deniability</a> in the <a href="https://veracrypt.jp/en/Documentation.html" target="_blank" style="text-align:left; color:#0080c0; text-decoration:none">
documentation</a>.</div>
<div id="SystemReinstallUpgrade" style="text-align:left; margin-top:19px; margin-bottom:19px; padding-top:0px; padding-bottom:0px">
<br style="text-align:left">
<strong style="text-align:left">Will I be able to mount my VeraCrypt partition/container after I reinstall or upgrade the operating system?</strong></div>
<div style="text-align:left; margin-top:19px; margin-bottom:19px; padding-top:0px; padding-bottom:0px">
Yes, <a href="VeraCrypt%20Volume.html" target="_blank" style="text-align:left; color:#0080c0; text-decoration:none">
VeraCrypt volumes</a> are independent of the operating system. However, you need to make sure your operating system installer does not format the partition where your VeraCrypt volume resides.<br style="text-align:left">
<br style="text-align:left">
Note: If the system partition/drive is encrypted and you want to reinstall or upgrade Windows, you need to decrypt it first (select
<em style="text-align:left">System</em> &gt; <em style="text-align:left">Permanently Decrypt System Partition/Drive</em>). However, a running operating system can be
<em style="text-align:left">updated</em> (security patches, service packs, etc.) without any problems even when the system partition/drive is encrypted.</div>
<div style="text-align:left; margin-top:19px; margin-bottom:19px; padding-top:0px; padding-bottom:0px">
<br style="text-align:left">
<strong style="text-align:left">Can I upgrade from an older version of VeraCrypt to the latest version without any problems?</strong></div>
<div style="text-align:left; margin-top:19px; margin-bottom:19px; padding-top:0px; padding-bottom:0px">
Generally, yes. However, before upgrading, please read the <a href="Release%20Notes.html" target="_blank" style="text-align:left; color:#0080c0; text-decoration:none">
release notes</a> for all versions of VeraCrypt that have been released since your version was released. If there are any known issues or incompatibilities related to upgrading from your version to a newer one, they will be listed in the
<a href="Release%20Notes.html" target="_blank" style="text-align:left; color:#0080c0; text-decoration:none">
release notes</a>.</div>
<div style="text-align:left; margin-top:19px; margin-bottom:19px; padding-top:0px; padding-bottom:0px">
<br style="text-align:left">
<strong style="text-align:left">Can I upgrade VeraCrypt if the system partition/drive is encrypted or do I have to decrypt it first?</strong></div>
<div style="text-align:left; margin-top:19px; margin-bottom:19px; padding-top:0px; padding-bottom:0px">
Generally, you can upgrade to the latest version without decrypting the system partition/drive (just run the VeraCrypt installer and it will automatically upgrade VeraCrypt on the system). However, before upgrading, please read the
<a href="Release%20Notes.html" target="_blank" style="text-align:left; color:#0080c0; text-decoration:none">
release notes</a> for all versions of VeraCrypt that have been released since your version was released. If there are any known issues or incompatibilities related to upgrading from your version to a newer one, they will be listed in the
<a href="Release%20Notes.html" target="_blank" style="text-align:left; color:#0080c0; text-decoration:none">
release notes</a>. Note that this FAQ answer is also valid for users of a <a href="Hidden%20Operating%20System.html" target="_blank" style="text-align:left; color:#0080c0; text-decoration:none">
hidden operating system</a>. Also note that you cannot <em style="text-align:left">
down</em>grade VeraCrypt if the system partition/drive is encrypted.</div>
<div style="text-align:left; margin-top:19px; margin-bottom:19px; padding-top:0px; padding-bottom:0px">
<br style="text-align:left">
<strong style="text-align:left">I use pre-boot authentication. Can I prevent a person (adversary) that is watching me start my computer from knowing that I use VeraCrypt?</strong></div>
<div style="text-align:left; margin-top:19px; margin-bottom:19px; padding-top:0px; padding-bottom:0px">
Yes. To do so, boot the encrypted system, start VeraCrypt, select <em style="text-align:left">
Settings</em> &gt; <em style="text-align:left">System Encryption</em>, enable the option '<em style="text-align:left">Do not show any texts in the pre-boot authentication screen</em>' and click
<em style="text-align:left">OK</em>. Then, when you start the computer, no texts will be displayed by the VeraCrypt boot loader (not even when you enter the wrong password). The computer will appear to be &quot;frozen&quot; while you can type your password. It is, however,
 important to note that if the adversary can analyze the content of the hard drive, he can still find out that it contains the VeraCrypt boot loader.</div>
<div style="text-align:left; margin-top:19px; margin-bottom:19px; padding-top:0px; padding-bottom:0px">
<br style="text-align:left">
<strong style="text-align:left">I use pre-boot authentication. Can I configure the VeraCrypt Boot Loader to display only a fake error message?</strong></div>
<div style="text-align:left; margin-top:19px; margin-bottom:19px; padding-top:0px; padding-bottom:0px">
Yes. To do so, boot the encrypted system, start VeraCrypt, select <em style="text-align:left">
Settings</em> &gt; <em style="text-align:left">System Encryption</em>, enable the option '<em style="text-align:left">Do not show any texts in the pre-boot authentication screen</em>' and enter the fake error message in the corresponding field (for example,
 the &quot;<em style="text-align:left">Missing operating system</em>&quot; message, which is normally displayed by the Windows boot loader if it finds no Windows boot partition). It is, however, important to note that if the adversary can analyze the content of the hard
 drive, he can still find out that it contains the VeraCrypt boot loader.</div>
<div style="text-align:left; margin-top:19px; margin-bottom:19px; padding-top:0px; padding-bottom:0px">
<br style="text-align:left">
<strong style="text-align:left">Can I configure VeraCrypt to mount automatically whenever Windows starts a non-system VeraCrypt volume that uses the same password as my system partition/drive (i.e. my pre-boot authentication password)?</strong></div>
<div style="text-align:left; margin-top:19px; margin-bottom:19px; padding-top:0px; padding-bottom:0px">
Yes. To do so, follow these steps:</div>
<ol style="text-align:left; margin-top:18px; margin-bottom:19px; padding-top:0px; padding-bottom:0px">
<li style="text-align:left; margin-top:0px; margin-bottom:0px; padding-top:0px; padding-bottom:0px">
Mount the volume (to the drive letter to which you want it to be mounted every time).
</li><li style="text-align:left; margin-top:0px; margin-bottom:0px; padding-top:0px; padding-bottom:0px">
Right-click the mounted volume in the drive list in the main VeraCrypt window and select '<em style="text-align:left">Add to System Favorites</em>'.
</li><li style="text-align:left; margin-top:0px; margin-bottom:0px; padding-top:0px; padding-bottom:0px">
The System Favorites Organizer window should appear now. In this window, enable the option '<em style="text-align:left">Mount system favorite volumes when Windows starts</em>' and click
<em style="text-align:left">OK</em>. </li></ol>
<div style="text-align:left; margin-top:19px; margin-bottom:19px; padding-top:0px; padding-bottom:0px">
For more information, see the chapter <a href="System%20Favorite%20Volumes.html" target="_blank" style="text-align:left; color:#0080c0; text-decoration:none">
System Favorite Volumes</a>.</div>
<div style="text-align:left; margin-top:19px; margin-bottom:19px; padding-top:0px; padding-bottom:0px">
<br style="text-align:left">
<strong style="text-align:left">Can a volume be automatically mounted whenever I log on to Windows?</strong></div>
<div style="text-align:left; margin-top:19px; margin-bottom:19px; padding-top:0px; padding-bottom:0px">
Yes. To do so, follow these steps:</div>
<ol style="text-align:left; margin-top:18px; margin-bottom:19px; padding-top:0px; padding-bottom:0px">
<li style="text-align:left; margin-top:0px; margin-bottom:0px; padding-top:0px; padding-bottom:0px">
Mount the volume (to the drive letter to which you want it to be mounted every time).
</li><li style="text-align:left; margin-top:0px; margin-bottom:0px; padding-top:0px; padding-bottom:0px">
Right-click the mounted volume in the drive list in the main VeraCrypt window and select '<em style="text-align:left">Add to Favorites</em>'.
</li><li style="text-align:left; margin-top:0px; margin-bottom:0px; padding-top:0px; padding-bottom:0px">
The <a href="Favorite%20Volumes.html" target="_blank" style="text-align:left; color:#0080c0; text-decoration:none">
Favorites</a> Organizer window should appear now. In this window, enable the option '<em style="text-align:left">Mount selected volume upon logon</em>' and click
<em style="text-align:left">OK</em>. </li></ol>
<div style="text-align:left; margin-top:19px; margin-bottom:19px; padding-top:0px; padding-bottom:0px">
Then, when you log on to Windows, you will be asked for the volume password (and/or keyfiles) and if it is correct, the volume will be mounted.<br style="text-align:left">
<br style="text-align:left">
Alternatively, if the volumes are partition/device-hosted and if you do not need to mount them to particular drive letters every time, you can follow these steps:</div>
<ol style="text-align:left; margin-top:18px; margin-bottom:19px; padding-top:0px; padding-bottom:0px">
<li style="text-align:left; margin-top:0px; margin-bottom:0px; padding-top:0px; padding-bottom:0px">
Select <em style="text-align:left">Settings</em> &gt; <em style="text-align:left">
Preferences. </em>The <em style="text-align:left">Preferences</em> window should appear now.
</li><li style="text-align:left; margin-top:0px; margin-bottom:0px; padding-top:0px; padding-bottom:0px">
In the section '<em style="text-align:left">Actions to perform upon logon to Windows</em>', enable the option '<em style="text-align:left">Mount all devices-hosted VeraCrypt volumes</em>' and click
<em style="text-align:left">OK</em>. </li></ol>
<div style="text-align:left; margin-top:19px; margin-bottom:19px; padding-top:0px; padding-bottom:0px">
Note: VeraCrypt will not prompt you for a password if you have enabled caching of the
<a href="System%20Encryption.html" target="_blank" style="text-align:left; color:#0080c0; text-decoration:none">
pre-boot authentication</a> password (<em style="text-align:left">Settings</em> &gt; '<em style="text-align:left">System Encryption</em>') and the volumes use the same password as the system partition/drive.</div>
<div style="text-align:left; margin-top:19px; margin-bottom:19px; padding-top:0px; padding-bottom:0px">
<br style="text-align:left">
<strong style="text-align:left">Can a volume be automatically mounted whenever its host device gets connected to the computer?</strong></div>
<div style="text-align:left; margin-top:19px; margin-bottom:19px; padding-top:0px; padding-bottom:0px">
Yes. For example, if you have a VeraCrypt container on a USB flash drive and you want VeraCrypt to mount it automatically when you insert the USB flash drive into the USB port, follow these steps:</div>
<ol style="text-align:left; margin-top:18px; margin-bottom:19px; padding-top:0px; padding-bottom:0px">
<li style="text-align:left; margin-top:0px; margin-bottom:0px; padding-top:0px; padding-bottom:0px">
Mount the volume (to the drive letter to which you want it to be mounted every time).
</li><li style="text-align:left; margin-top:0px; margin-bottom:0px; padding-top:0px; padding-bottom:0px">
Right-click the mounted volume in the drive list in the main VeraCrypt window and select '<em style="text-align:left">Add to Favorites</em>'.
</li><li style="text-align:left; margin-top:0px; margin-bottom:0px; padding-top:0px; padding-bottom:0px">
The <a href="Favorite%20Volumes.html" target="_blank" style="text-align:left; color:#0080c0; text-decoration:none">
Favorites</a> Organizer window should appear now. In this window, enable the option '<em style="text-align:left">Mount selected volume when its host device gets connected</em>' and click
<em style="text-align:left">OK</em>. </li></ol>
<div style="text-align:left; margin-top:19px; margin-bottom:19px; padding-top:0px; padding-bottom:0px">
Then, when you insert the USB flash drive into the USB port, you will be asked for the volume password (and/or keyfiles) (unless it is cached) and if it is correct, the volume will be mounted.<br style="text-align:left">
<br style="text-align:left">
Note: VeraCrypt will not prompt you for a password if you have enabled caching of the
<a href="System%20Encryption.html" target="_blank" style="text-align:left; color:#0080c0; text-decoration:none">
pre-boot authentication</a> password (<em style="text-align:left">Settings</em> &gt; '<em style="text-align:left">System Encryption</em>') and the volume uses the same password as the system partition/drive.</div>
<div style="text-align:left; margin-top:19px; margin-bottom:19px; padding-top:0px; padding-bottom:0px">
<br style="text-align:left">
<strong style="text-align:left">Can my pre-boot authentication password be cached so that I can use it mount non-system volumes during the session?</strong></div>
<div style="text-align:left; margin-top:19px; margin-bottom:19px; padding-top:0px; padding-bottom:0px">
Yes. Select <em style="text-align:left">Settings</em> &gt; '<em style="text-align:left">System Encryption</em>' and enable the following option: '<em style="text-align:left">Cache pre-boot authentication password in driver memory</em>'.</div>
<div style="text-align:left; margin-top:19px; margin-bottom:19px; padding-top:0px; padding-bottom:0px">
<a name="notraces" style="text-align:left; color:#0080c0; text-decoration:none"></a><br style="text-align:left">
<strong style="text-align:left">I live in a country that violates basic human rights of its people. Is it possible to use VeraCrypt without leaving any 'traces' on unencrypted Windows?</strong></div>
<div style="text-align:left; margin-top:19px; margin-bottom:19px; padding-top:0px; padding-bottom:0px">
Yes. This can be achieved by running VeraCrypt in <a href="Portable%20Mode.html" target="_blank" style="text-align:left; color:#0080c0; text-decoration:none">
portable mode</a> under <a href="http://www.nu2.nu/pebuilder/" target="_blank" style="text-align:left; color:#0080c0; text-decoration:none">
BartPE</a> or in a similar environment. BartPE stands for &quot;Bart's Preinstalled Environment&quot;, which is essentially the Windows operating system prepared in a way that it can be entirely stored on and booted from a CD/DVD (registry, temporary files, etc., are
 stored in RAM &ndash; hard drive is not used at all and does not even have to be present). The freeware
<a href="http://www.nu2.nu/pebuilder/" target="_blank" style="text-align:left; color:#0080c0; text-decoration:none">
Bart's PE Builder</a> can transform a Windows XP installation CD into a BartPE CD. Note that you do not even need any special VeraCrypt plug-in for BartPE. Follow these steps:</div>
<ol style="text-align:left; margin-top:18px; margin-bottom:19px; padding-top:0px; padding-bottom:0px">
<li style="text-align:left; margin-top:0px; margin-bottom:0px; padding-top:0px; padding-bottom:0px">
Create a BartPE CD and boot it. (Note: You must perform each of the following steps from within BartPE.)
</li><li style="text-align:left; margin-top:0px; margin-bottom:0px; padding-top:0px; padding-bottom:0px">
Download the VeraCrypt self-extracting package to the RAM disk (which BartPE automatically creates).
<br style="text-align:left">
<br style="text-align:left">
<strong style="text-align:left">Note</strong>: If the adversary can intercept data you send or receive over the Internet and you need to prevent the adversary from knowing you downloaded VeraCrypt, consider downloading it via
<a href="https://geti2p.net/en/" target="_blank" style="text-align:left; color:#0080c0; text-decoration:none">
<strong style="text-align:left">I2P</strong></a>, <a href="http://www.torproject.org/" target="_blank" style="text-align:left; color:#0080c0; text-decoration:none">
<strong style="text-align:left">Tor</strong></a>, or a similar anonymizing network.
</li><li style="text-align:left; margin-top:0px; margin-bottom:0px; padding-top:0px; padding-bottom:0px">
Verify the digital signatures of the downloaded file (see <a href="Digital%20Signatures.html" target="_blank" style="text-align:left; color:#0080c0; text-decoration:none">
this</a> section of the documentation for more information). </li><li style="text-align:left; margin-top:0px; margin-bottom:0px; padding-top:0px; padding-bottom:0px">
Run the downloaded file, and select <em style="text-align:left">Extract</em> (instead of
<em style="text-align:left">Install</em>) on the second page of the VeraCrypt Setup wizard. Extract the contents to the RAM disk.
</li><li style="text-align:left; margin-top:0px; margin-bottom:0px; padding-top:0px; padding-bottom:0px">
Run the file <em style="text-align:left">VeraCrypt.exe</em> from the RAM disk. </li></ol>
<div style="text-align:left; margin-top:19px; margin-bottom:19px; padding-top:0px; padding-bottom:0px">
Note: You may also want to consider creating a hidden operating system (see the section
<a href="Hidden%20Operating%20System.html" target="_blank" style="text-align:left; color:#0080c0; text-decoration:none">
Hidden Operating System</a> in the <a href="https://veracrypt.jp/en/Documentation.html" target="_blank" style="text-align:left; color:#0080c0; text-decoration:none">
documentation</a>). See also the chapter <a href="Plausible%20Deniability.html" target="_blank" style="text-align:left; color:#0080c0; text-decoration:none">
Plausible Deniability</a>.</div>
<div style="text-align:left; margin-top:19px; margin-bottom:19px; padding-top:0px; padding-bottom:0px">
<br style="text-align:left">
<strong style="text-align:left">Can I encrypt my system partition/drive if I don't have a US keyboard?</strong></div>
<div style="text-align:left; margin-top:19px; margin-bottom:19px; padding-top:0px; padding-bottom:0px">
Yes, VeraCrypt supports all keyboard layouts. Because of BIOS requirement, the pre-boot password is typed using
<strong>US keyboard layout. </strong>During the system encryption process, VeraCrypt automatically and transparently switches the keyboard to US layout in order to ensure that the password value typed will match the one typed in pre-boot mode. Thus, in order
 to avoid wrong password errors, one must type the password using the same keys as when creating the system encryption.</div>
<div style="text-align:left; margin-top:19px; margin-bottom:19px; padding-top:0px; padding-bottom:0px">
<br style="text-align:left">
<strong style="text-align:left">Can I save data to the decoy system partition without risking damage to the hidden system partition?</strong></div>
<div style="text-align:left; margin-top:19px; margin-bottom:19px; padding-top:0px; padding-bottom:0px">
Yes. You can write data to the decoy system partition anytime without any risk that the hidden volume will get damaged (because the decoy system is
<em style="text-align:left">not</em> installed within the same partition as the hidden system). For more information, see the section
<a href="Hidden%20Operating%20System.html" target="_blank" style="text-align:left; color:#0080c0; text-decoration:none">
Hidden Operating System</a> in the <a href="https://veracrypt.jp/en/Documentation.html" target="_blank" style="text-align:left; color:#0080c0; text-decoration:none">
documentation</a>.</div>
<div style="text-align:left; margin-top:19px; margin-bottom:19px; padding-top:0px; padding-bottom:0px">
<br style="text-align:left">
<strong style="text-align:left">Can I use VeraCrypt on Windows if I do not have administrator privileges?</strong></div>
<div style="text-align:left; margin-top:19px; margin-bottom:19px; padding-top:0px; padding-bottom:0px">
See the chapter '<a href="Using%20VeraCrypt%20Without%20Administrator%20Privileges.html" target="_blank" style="text-align:left; color:#0080c0; text-decoration:none">Using VeraCrypt Without Administrator Privileges</a>'
 in the <a href="https://veracrypt.jp/en/Documentation.html" target="_blank" style="text-align:left; color:#0080c0; text-decoration:none">
documentation</a>.</div>
<div style="text-align:left; margin-top:19px; margin-bottom:19px; padding-top:0px; padding-bottom:0px">
<br style="text-align:left">
<strong style="text-align:left">Does VeraCrypt save my password to a disk?</strong></div>
<div style="text-align:left; margin-top:19px; margin-bottom:19px; padding-top:0px; padding-bottom:0px">
No.</div>
<div style="text-align:left; margin-top:19px; margin-bottom:19px; padding-top:0px; padding-bottom:0px">
<br style="text-align:left">
<strong style="text-align:left">How does VeraCrypt verify that the correct password was entered?</strong></div>
<div style="text-align:left; margin-top:19px; margin-bottom:19px; padding-top:0px; padding-bottom:0px">
See the section <a href="Encryption%20Scheme.html" target="_blank" style="text-align:left; color:#0080c0; text-decoration:none">
Encryption Scheme</a> (chapter <a href="Technical%20Details.html" target="_blank" style="text-align:left; color:#0080c0; text-decoration:none">
Technical Details</a>) in the <a href="https://veracrypt.jp/en/Documentation.html" target="_blank" style="text-align:left; color:#0080c0; text-decoration:none">
documentation</a>.</div>
<div id="encrypt-in-place" style="text-align:left; margin-top:19px; margin-bottom:19px; padding-top:0px; padding-bottom:0px">
<br style="text-align:left">
<strong style="text-align:left">Can I encrypt a partition/drive without losing the data currently stored on it?</strong></div>
<div style="text-align:left; margin-top:19px; margin-bottom:19px; padding-top:0px; padding-bottom:0px">
Yes, but the following conditions must be met:</div>
<ul style="text-align:left; margin-top:18px; margin-bottom:19px; padding-top:0px; padding-bottom:0px">
<li style="text-align:left; margin-top:0px; margin-bottom:0px; padding-top:0px; padding-bottom:0px">
If you want to encrypt an entire system drive (which may contain multiple partitions) or a system partition (in other words, if you want to encrypt a drive or partition where Windows is installed), you can do so provided that you use Windows XP or a later version
 of Windows (such as Windows 7) <span style="text-align:left; font-size:10px; line-height:12px">
(select '<em style="text-align:left">System</em>' &gt; '<em style="text-align:left">Encrypt System Partition/Drive</em>' and then follow the instructions in the wizard)</span>.
</li><li style="text-align:left; margin-top:0px; margin-bottom:0px; padding-top:0px; padding-bottom:0px">
If you want to encrypt a non-system partition in place, you can do so provided that it contains an NTFS filesystem and that you use Windows Vista or a later version of Windows (for example, Windows 7)
<span style="text-align:left; font-size:10px; line-height:12px">(click '<em style="text-align:left">Create Volume</em>' &gt; '<em style="text-align:left">Encrypt a non-system partition</em>' &gt; '<em style="text-align:left">Standard volume</em>' &gt; '<em style="text-align:left">Select
 Device</em>' &gt; '<em style="text-align:left">Encrypt partition in place</em>' and then follow the instructions in the wizard)</span>.
</li></ul>
<div style="text-align:left; margin-top:19px; margin-bottom:19px; padding-top:0px; padding-bottom:0px">
<br style="text-align:left">
<strong style="text-align:left">Can I run VeraCrypt if I don't install it?</strong></div>
<div style="text-align:left; margin-top:19px; margin-bottom:19px; padding-top:0px; padding-bottom:0px">
Yes, see the chapter <a href="Portable%20Mode.html" target="_blank" style="text-align:left; color:#0080c0; text-decoration:none">
Portable Mode</a> in the <a href="https://veracrypt.jp/en/Documentation.html" target="_blank" style="text-align:left; color:#0080c0; text-decoration:none">
VeraCrypt User Guide</a>.</div>
<div style="text-align:left; margin-top:19px; margin-bottom:19px; padding-top:0px; padding-bottom:0px">
<a name="tpm" style="text-align:left; color:#0080c0; text-decoration:none"></a><br style="text-align:left">
<strong style="text-align:left">Some encryption programs use TPM to prevent attacks. Will VeraCrypt use it too?</strong></div>
<div style="text-align:left; margin-top:19px; margin-bottom:19px; padding-top:0px; padding-bottom:0px">
No. Those programs use TPM to protect against attacks that <em style="text-align:left">
require</em> the attacker to have administrator privileges, or physical access to the computer, and the attacker needs you to use the computer after such an access.
<em style="text-align:left">However, if any of these conditions is met, it is actually impossible to secure the computer</em> (see below) and, therefore, you must stop using it (instead of relying on TPM).
<br style="text-align:left">
<br style="text-align:left">
If the attacker has administrator privileges, he can, for example, reset the TPM, capture the content of RAM (containing master keys) or content of files stored on mounted VeraCrypt volumes (decrypted on the fly), which can then be sent to the attacker over
 the Internet or saved to an unencrypted local drive (from which the attacker might be able to read it later, when he gains physical access to the computer).
<br style="text-align:left">
<br style="text-align:left">
If the attacker can physically access the computer hardware (and you use it after such an access), he can, for example, attach a malicious component to it (such as a hardware keystroke logger) that will capture the password, the content of RAM (containing master
 keys) or content of files stored on mounted VeraCrypt volumes (decrypted on the fly), which can then be sent to the attacker over the Internet or saved to an unencrypted local drive (from which the attacker might be able to read it later, when he gains physical
 access to the computer again). <br style="text-align:left">
<br style="text-align:left">
The only thing that TPM is almost guaranteed to provide is a false sense of security (even the name itself, &quot;Trusted Platform Module&quot;, is misleading and creates a false sense of security). As for real security, TPM is actually redundant (and implementing redundant
 features is usually a way to create so-called bloatware). <br style="text-align:left">
<br style="text-align:left">
For more information, please see the sections <a title="Physical%20Security&quot;" style="text-align:left; color:#0080c0; text-decoration:none">
Physical Security</a> and <a href="Malware.html" style="text-align:left; color:#0080c0; text-decoration:none">
Malware</a> in the <a href="https://veracrypt.jp/en/Documentation.html" target="_blank" style="text-align:left; color:#0080c0; text-decoration:none">
documentation</a>.</div>
<div style="text-align:left; margin-top:19px; margin-bottom:19px; padding-top:0px; padding-bottom:0px">
<br style="text-align:left">
<strong style="text-align:left">Do I have to unmount VeraCrypt volumes before shutting down or restarting Windows?</strong></div>
<div style="text-align:left; margin-top:19px; margin-bottom:19px; padding-top:0px; padding-bottom:0px">
No. VeraCrypt automatically unmounts all mounted VeraCrypt volumes on system shutdown/restart.</div>
<div style="text-align:left; margin-top:19px; margin-bottom:19px; padding-top:0px; padding-bottom:0px">
<br style="text-align:left">
<strong style="text-align:left">Which type of VeraCrypt volume is better &ndash; partition or file container?</strong></div>
<div style="text-align:left; margin-top:19px; margin-bottom:19px; padding-top:0px; padding-bottom:0px">
<a href="VeraCrypt%20Volume.html" target="_blank" style="text-align:left; color:#0080c0; text-decoration:none">File containers</a> are normal files so you can work with them as with any normal files (file containers
 can be, for example, moved, renamed, and deleted the same way as normal files). <a href="VeraCrypt%20Volume.html" target="_blank" style="text-align:left; color:#0080c0; text-decoration:none">
Partitions/drives</a> may be better as regards performance. Note that reading and writing to/from a file container may take significantly longer when the container is heavily fragmented. To solve this problem, defragment the file system in which the container
 is stored (when the VeraCrypt volume is unmounted).</div>
<div style="text-align:left; margin-top:19px; margin-bottom:19px; padding-top:0px; padding-bottom:0px">
<br style="text-align:left">
<strong style="text-align:left">What's the recommended way to back up a VeraCrypt volume?</strong></div>
<div style="text-align:left; margin-top:19px; margin-bottom:19px; padding-top:0px; padding-bottom:0px">
See the chapter <a href="How%20to%20Back%20Up%20Securely.html" style="text-align:left; color:#0080c0; text-decoration:none">
How to Back Up Securely</a> in the <a href="https://veracrypt.jp/en/Documentation.html" target="_blank" style="text-align:left; color:#0080c0; text-decoration:none">
documentation</a>.</div>
<div style="text-align:left; margin-top:19px; margin-bottom:19px; padding-top:0px; padding-bottom:0px">
<br style="text-align:left">
<strong style="text-align:left">What will happen if I format a VeraCrypt partition?</strong></div>
<div style="text-align:left; margin-top:19px; margin-bottom:19px; padding-top:0px; padding-bottom:0px">
See the question '<em style="text-align:left"><a href="#changing-filesystem" style="text-align:left; color:#0080c0; text-decoration:none">Is it possible to change the file system of an encrypted volume?</a></em>'</div>
<div style="text-align:left; margin-top:19px; margin-bottom:19px; padding-top:0px; padding-bottom:0px">
<br style="text-align:left">
<strong style="text-align:left"><a name="changing-filesystem" style="text-align:left; color:#0080c0; text-decoration:none"></a>Is it possible to change the file system of an encrypted volume?</strong></div>
<div style="text-align:left; margin-top:19px; margin-bottom:19px; padding-top:0px; padding-bottom:0px">
Yes, when mounted, VeraCrypt volumes can be formatted as FAT12, FAT16, FAT32, NTFS, or any other file system. VeraCrypt volumes behave as standard disk devices so you can right-click the device icon (for example in the '<em style="text-align:left">Computer</em>'
 or '<em style="text-align:left">My Computer</em>' list) and select '<em style="text-align:left">Format</em>'. The actual volume contents will be lost. However, the whole volume will remain encrypted. If you format a VeraCrypt-encrypted partition when the VeraCrypt
 volume that the partition hosts is not mounted, then the volume will be destroyed, and the partition will not be encrypted anymore (it will be empty).</div>
<div style="text-align:left; margin-top:19px; margin-bottom:19px; padding-top:0px; padding-bottom:0px">
<br style="text-align:left">
<strong style="text-align:left">Is it possible to mount a VeraCrypt container that is stored on a CD or DVD?</strong></div>
<div style="text-align:left; margin-top:19px; margin-bottom:19px; padding-top:0px; padding-bottom:0px">
Yes. However, if you need to mount a VeraCrypt volume that is stored on a read-only medium (such as a CD or DVD) under Windows 2000, the file system within the VeraCrypt volume must be FAT (Windows 2000 cannot mount an NTFS file system on read-only media).</div>
<div style="text-align:left; margin-top:19px; margin-bottom:19px; padding-top:0px; padding-bottom:0px">
<br style="text-align:left">
<strong style="text-align:left">Is it possible to change the password for a hidden volume?</strong></div>
<div style="text-align:left; margin-top:19px; margin-bottom:19px; padding-top:0px; padding-bottom:0px">
Yes, the password change dialog works both for standard and <a href="Hidden%20Volume.html" target="_blank" style="text-align:left; color:#0080c0; text-decoration:none">
hidden volumes</a>. Just type the password for the hidden volume in the 'Current Password' field of the 'Volume Password Change' dialog.</div>
<div style="text-align:left; margin-top:19px; margin-bottom:19px; padding-top:0px; padding-bottom:0px; font-size:10px; line-height:12px">
Remark: VeraCrypt first attempts to decrypt the standard <a href="VeraCrypt%20Volume%20Format%20Specification.html" target="_blank" style="text-align:left; color:#0080c0; text-decoration:none">
volume header</a> and if it fails, it attempts to decrypt the area within the volume where the hidden volume header may be stored (if there is a hidden volume within). In case it is successful, the password change applies to the hidden volume. (Both attempts
 use the password typed in the 'Current Password' field.)</div>
<div style="text-align:left; margin-top:19px; margin-bottom:19px; padding-top:0px; padding-bottom:0px">
<br style="text-align:left">
<strong style="text-align:left">How do I burn a VeraCrypt container larger than 2 GB onto a DVD?</strong><br style="text-align:left">
<br style="text-align:left">
The DVD burning software you use should allow you to select the format of the DVD. If it does, select the UDF format (ISO format does not support files larger than 2 GB).</div>
<div id="disk_defragmenter" style="text-align:left; margin-top:19px; margin-bottom:19px; padding-top:0px; padding-bottom:0px">
<br style="text-align:left">
<strong style="text-align:left">Can I use tools like <em style="text-align:left">
chkdsk</em>, Disk Defragmenter, etc. on the contents of a mounted VeraCrypt volume?</strong></div>
<div style="text-align:left; margin-top:19px; margin-bottom:19px; padding-top:0px; padding-bottom:0px">
Yes, VeraCrypt volumes behave like real physical disk devices, so it is possible to use any filesystem checking/repairing/defragmenting tools on the contents of a mounted VeraCrypt volume.</div>
<div style="text-align:left; margin-top:19px; margin-bottom:19px; padding-top:0px; padding-bottom:0px">
<br style="text-align:left">
<strong style="text-align:left">Does VeraCrypt support 64-bit versions of Windows?</strong></div>
<div style="text-align:left; margin-top:19px; margin-bottom:19px; padding-top:0px; padding-bottom:0px">
Yes, it does. <span style="text-align:left; font-size:10px; line-height:12px">Note: 64-bit versions of Windows load only drivers that are digitally signed with a digital certificate issued by a certification authority approved for issuing kernel-mode code signing
 certificates. VeraCrypt complies with this requirement (the VeraCrypt driver is <a href="Digital%20Signatures.html" style="text-align:left; color:#0080c0; text-decoration:none">
digitally signed</a> with the digital certificate of IDRIX, which was issued by the certification authority Thawte).</span></div>
<div style="text-align:left; margin-top:19px; margin-bottom:19px; padding-top:0px; padding-bottom:0px">
<br style="text-align:left">
<strong style="text-align:left">Can I mount my VeraCrypt volume under Windows, Mac OS X, and Linux?</strong></div>
<div style="text-align:left; margin-top:19px; margin-bottom:19px; padding-top:0px; padding-bottom:0px">
Yes, VeraCrypt volumes are fully cross-platform.</div>
<div style="text-align:left; margin-top:19px; margin-bottom:19px; padding-top:0px; padding-bottom:0px">
<strong style="text-align:left">How can I uninstall VeraCrypt on Linux?</strong>
<div style="text-align:left; margin-top:19px; margin-bottom:19px; padding-top:0px; padding-bottom:0px">
To uninstall VeraCrypt on Linux, run the following command in Terminal as root: <strong>
veracrypt-uninstall.sh</strong>. On Ubuntu, you can use &quot;<strong>sudo veracrypt-uninstall.sh</strong>&quot;.</div>
</div>
<div style="text-align:left; margin-top:19px; margin-bottom:19px; padding-top:0px; padding-bottom:0px">
<br style="text-align:left">
<strong style="text-align:left">Is there a list of all operating systems that VeraCrypt supports?</strong></div>
<div style="text-align:left; margin-top:19px; margin-bottom:19px; padding-top:0px; padding-bottom:0px">
Yes, see the chapter <a href="Supported%20Operating%20Systems.html" target="_blank" style="text-align:left; color:#0080c0; text-decoration:none">
Supported Operating Systems</a> in the <a href="https://veracrypt.jp/en/Documentation.html" target="_blank" style="text-align:left; color:#0080c0; text-decoration:none">
VeraCrypt User Guide</a>.</div>
<div style="text-align:left; margin-top:19px; margin-bottom:19px; padding-top:0px; padding-bottom:0px">
<br style="text-align:left">
<strong style="text-align:left">Is it possible to install an application to a VeraCrypt volume and run it from there?</strong></div>
<div style="text-align:left; margin-top:19px; margin-bottom:19px; padding-top:0px; padding-bottom:0px">
Yes.</div>
<div style="text-align:left; margin-top:19px; margin-bottom:19px; padding-top:0px; padding-bottom:0px">
<br style="text-align:left">
<strong style="text-align:left">What will happen when a part of a VeraCrypt volume becomes corrupted?</strong></div>
<div style="text-align:left; margin-top:19px; margin-bottom:19px; padding-top:0px; padding-bottom:0px">
In encrypted data, one corrupted bit usually corrupts the whole ciphertext block in which it occurred. The ciphertext block size used by VeraCrypt is 16 bytes (i.e., 128 bits). The
<a href="Modes%20of%20Operation.html" target="_blank" style="text-align:left; color:#0080c0; text-decoration:none">
mode of operation</a> used by VeraCrypt ensures that if data corruption occurs within a block, the remaining blocks are not affected. See also the question '<em style="text-align:left">What do I do when the encrypted filesystem on my VeraCrypt volume is corrupted?</em></div>
<div style="text-align:left; margin-top:19px; margin-bottom:19px; padding-top:0px; padding-bottom:0px">
<br style="text-align:left">
<strong style="text-align:left">What do I do when the encrypted filesystem on my VeraCrypt volume is corrupted?</strong></div>
<div style="text-align:left; margin-top:19px; margin-bottom:19px; padding-top:0px; padding-bottom:0px">
File system within a VeraCrypt volume may become corrupted in the same way as any normal unencrypted file system. When that happens, you can use filesystem repair tools supplied with your operating system to fix it. In Windows, it is the '<em style="text-align:left">chkdsk</em>'
 tool. VeraCrypt provides an easy way to use this tool on a VeraCrypt volume: Right-click the mounted volume in the main VeraCrypt window (in the drive list) and from the context menu select '<em style="text-align:left">Repair Filesystem</em>'.</div>
<div id="reset_volume_password" style="text-align:left; margin-top:19px; margin-bottom:19px; padding-top:0px; padding-bottom:0px">
<br style="text-align:left">
<strong style="text-align:left">We use VeraCrypt in a corporate/enterprise environment. Is there a way for an administrator to reset a volume password or pre-boot authentication password when a user forgets it (or loses a keyfile)?</strong></div>
<div style="text-align:left; margin-top:19px; margin-bottom:19px; padding-top:0px; padding-bottom:0px">
Yes. Note that there is no &quot;backdoor&quot; implemented in VeraCrypt. However, there is a way to &quot;reset&quot; volume passwords/<a href="Keyfiles.html" target="_blank" style="text-align:left; color:#0080c0; text-decoration:none">keyfiles</a>
 and <a href="System%20Encryption.html" target="_blank" style="text-align:left; color:#0080c0; text-decoration:none">
pre-boot authentication</a> passwords. After you create a volume, back up its header to a file (select
<em style="text-align:left">Tools</em> -&gt; <em style="text-align:left">Backup Volume Header</em>) before you allow a
<a href="Using%20VeraCrypt%20Without%20Administrator%20Privileges.html" style="text-align:left; color:#0080c0; text-decoration:none">
non-admin user</a> to use the volume. Note that the <a href="VeraCrypt%20Volume%20Format%20Specification.html" target="_blank" style="text-align:left; color:#0080c0; text-decoration:none">
volume header</a> (which is encrypted with a <a href="Header%20Key%20Derivation.html" target="_blank" style="text-align:left; color:#0080c0; text-decoration:none">
header key</a> derived from a password/keyfile) contains the <a href="Encryption%20Scheme.html" style="text-align:left; color:#0080c0; text-decoration:none">
master key</a> with which the volume is encrypted. Then ask the user to choose a password, and set it for him/her (<em style="text-align:left">Volumes</em> -&gt;
<em style="text-align:left">Change Volume Password</em>); or generate a user keyfile for him/her. Then you can allow the user to use the volume and to change the password/keyfiles without your assistance/permission. In case he/she forgets his/her password or
 loses his/her keyfile, you can &quot;reset&quot; the volume password/keyfiles to your original admin password/keyfiles by restoring the volume header from the backup file (<em style="text-align:left">Tools</em> -&gt;
<em style="text-align:left">Restore Volume Header</em>). <br style="text-align:left">
<br style="text-align:left">
Similarly, you can reset a <a href="System%20Encryption.html" target="_blank" style="text-align:left; color:#0080c0; text-decoration:none">
pre-boot authentication</a> password<a href="System%20Encryption.html" style="text-align:left; color:#0080c0; text-decoration:none">.
</a>To create a backup of the master key data (that will be stored on a <a href="VeraCrypt%20Rescue%20Disk.html" target="_blank" style="text-align:left; color:#0080c0; text-decoration:none">
VeraCrypt Rescue Disk</a> and encrypted with your administrator password), select '<em style="text-align:left">System</em>' &gt; '<a href="VeraCrypt%20Rescue%20Disk.html" target="_blank" style="text-align:left; color:#0080c0; text-decoration:none"><em style="text-align:left">Create
 Rescue Disk</em></a>'. To set a user <a href="System%20Encryption.html" target="_blank" style="text-align:left; color:#0080c0; text-decoration:none">
pre-boot authentication</a> password, select '<em style="text-align:left">System</em>' &gt; '<em style="text-align:left">Change Password</em>'. To restore your administrator password, boot the VeraCrypt Rescue Disk, select '<em style="text-align:left">Repair
 Options</em>' &gt; '<em style="text-align:left">Restore key data</em>' and enter your administrator password.
<br style="text-align:left">
<span style="text-align:left; font-size:10px; line-height:12px">Note: It is not required to burn each
<a href="VeraCrypt%20Rescue%20Disk.html" target="_blank" style="text-align:left; color:#0080c0; text-decoration:none">
VeraCrypt Rescue Disk</a> ISO image to a CD/DVD. You can maintain a central repository of ISO images for all workstations (rather than a repository of CDs/DVDs). For more information see the section
<a href="Command%20Line%20Usage.html" target="_blank" style="text-align:left; color:#0080c0; text-decoration:none">
Command Line Usage</a> (option <em style="text-align:left">/noisocheck</em>).</span></div>
<div style="text-align:left; margin-top:19px; margin-bottom:19px; padding-top:0px; padding-bottom:0px">
<br style="text-align:left">
<strong style="text-align:left">Can our commercial company use VeraCrypt free of charge?</strong></div>
<div style="text-align:left; margin-top:19px; margin-bottom:19px; padding-top:0px; padding-bottom:0px">
Provided that you comply with the terms and conditions of the <a href="VeraCrypt%20License.html" target="_blank" style="text-align:left; color:#0080c0; text-decoration:none">
VeraCrypt License</a>, you can install and run VeraCrypt free of charge on an arbitrary number of your computers.</div>
<div style="text-align:left; margin-top:19px; margin-bottom:19px; padding-top:0px; padding-bottom:0px">
<br style="text-align:left">
<strong style="text-align:left">We share a volume over a network. Is there a way to have the network share automatically restored when the system is restarted?</strong></div>
<div style="text-align:left; margin-top:19px; margin-bottom:19px; padding-top:0px; padding-bottom:0px">
Please see the chapter '<a href="Sharing%20over%20Network.html" style="text-align:left; color:#0080c0; text-decoration:none">Sharing over Network</a>' in the
<a href="https://veracrypt.jp/en/Documentation.html" target="_blank" style="text-align:left; color:#0080c0; text-decoration:none">
VeraCrypt User Guide</a>.</div>
<div style="text-align:left; margin-top:19px; margin-bottom:19px; padding-top:0px; padding-bottom:0px">
<br style="text-align:left">
<strong style="text-align:left">It is possible to access a single VeraCrypt volume simultaneously from multiple operating systems (for example, a volume shared over a network)?</strong></div>
<div style="text-align:left; margin-top:19px; margin-bottom:19px; padding-top:0px; padding-bottom:0px">
Please see the chapter '<a href="Sharing%20over%20Network.html" style="text-align:left; color:#0080c0; text-decoration:none">Sharing over Network</a>' in the
<a href="https://veracrypt.jp/en/Documentation.html" target="_blank" style="text-align:left; color:#0080c0; text-decoration:none">
VeraCrypt User Guide</a>.</div>
<div style="text-align:left; margin-top:19px; margin-bottom:19px; padding-top:0px; padding-bottom:0px">
<br style="text-align:left">
<strong style="text-align:left">Can a user access his or her VeraCrypt volume via a network?</strong></div>
<div style="text-align:left; margin-top:19px; margin-bottom:19px; padding-top:0px; padding-bottom:0px">
Please see the chapter '<a href="Sharing%20over%20Network.html" style="text-align:left; color:#0080c0; text-decoration:none">Sharing over Network</a>' in the
<a href="https://veracrypt.jp/en/Documentation.html" target="_blank" style="text-align:left; color:#0080c0; text-decoration:none">
VeraCrypt User Guide</a>.</div>
<div id="non_system_drive_letter" style="text-align:left; margin-top:19px; margin-bottom:19px; padding-top:0px; padding-bottom:0px">
<br style="text-align:left">
<strong style="text-align:left">I encrypted a non-system partition, but its original drive letter is still visible in the '<span style="text-align:left; font-style:italic">My Computer</span>' list. When I double click this drive letter, Windows asks if I want
 to format the drive. Is there a way to hide or free this drive letter? </strong>
</div>
<div style="text-align:left; margin-top:19px; margin-bottom:19px; padding-top:0px; padding-bottom:0px">
Yes, to free the drive letter follow these steps:</div>
<ol style="text-align:left; margin-top:18px; margin-bottom:19px; padding-top:0px; padding-bottom:0px">
<li style="text-align:left; margin-top:0px; margin-bottom:0px; padding-top:0px; padding-bottom:0px">
Right-click the '<em style="text-align:left">Computer</em>' (or '<span style="text-align:left; font-style:italic">My Computer</span>') icon on your desktop or in the Start Menu and select
<span style="text-align:left; font-style:italic">Manage</span>. The '<span style="text-align:left; font-style:italic">Computer Management</span>' window should appear.
</li><li style="text-align:left; margin-top:0px; margin-bottom:0px; padding-top:0px; padding-bottom:0px">
From the list on the left, select '<span style="text-align:left; font-style:italic">Disk Management</span>' (within the
<span style="text-align:left; font-style:italic">Storage</span> sub-tree). </li><li style="text-align:left; margin-top:0px; margin-bottom:0px; padding-top:0px; padding-bottom:0px">
Right-click the encrypted partition/device and select <span style="text-align:left; font-style:italic">
Change Drive Letter and Paths</span>. </li><li style="text-align:left; margin-top:0px; margin-bottom:0px; padding-top:0px; padding-bottom:0px">
Click <span style="text-align:left; font-style:italic">Remove</span>. </li><li style="text-align:left; margin-top:0px; margin-bottom:0px; padding-top:0px; padding-bottom:0px">
If Windows prompts you to confirm the action, click <span style="text-align:left; font-style:italic">
Yes</span>. </li></ol>
<div style="text-align:left; margin-top:19px; margin-bottom:19px; padding-top:0px; padding-bottom:0px">
<strong style="text-align:left"><br style="text-align:left">
When I plug in my encrypted USB flash drive, Windows asks me if I want to format it. Is there a way to prevent that?</strong></div>
<div style="text-align:left; margin-top:19px; margin-bottom:19px; padding-top:0px; padding-bottom:0px">
Yes, but you will need to remove the drive letter assigned to the device. For information on how to do so, see the question '<em style="text-align:left">I encrypted a non-system partition, but its original drive letter is still visible in the 'My Computer'
 list.</em>'</div>
<div style="text-align:left; margin-top:19px; margin-bottom:19px; padding-top:0px; padding-bottom:0px">
<strong style="text-align:left"><br style="text-align:left">
How do I remove or undo encryption if I do not need it anymore? How do I permanently decrypt a volume?
</strong></div>
<div style="text-align:left; margin-top:19px; margin-bottom:19px; padding-top:0px; padding-bottom:0px">
Please see the section '<a href="Removing%20Encryption.html" style="text-align:left; color:#0080c0; text-decoration:none">How to Remove Encryption</a>' in the
<a href="https://veracrypt.jp/en/Documentation.html" target="_blank" style="text-align:left; color:#0080c0; text-decoration:none">
VeraCrypt User Guide</a>.</div>
<div style="text-align:left; margin-top:19px; margin-bottom:19px; padding-top:0px; padding-bottom:0px">
<br style="text-align:left">
<strong style="text-align:left">What will change when I enable the option '<em style="text-align:left">Mount volumes as removable media</em>'?</strong></div>
<div style="text-align:left; margin-top:19px; margin-bottom:19px; padding-top:0px; padding-bottom:0px">
Please see the section '<a href="Removable%20Medium%20Volume.html" style="text-align:left; color:#0080c0; text-decoration:none">Volume Mounted as Removable Medium</a>' in the
<a href="https://veracrypt.jp/en/Documentation.html" target="_blank" style="text-align:left; color:#0080c0; text-decoration:none">
VeraCrypt User Guide</a>.</div>
<div style="text-align:left; margin-top:19px; margin-bottom:19px; padding-top:0px; padding-bottom:0px">
<br style="text-align:left">
<strong style="text-align:left">Is the online documentation available for download as a single file?</strong></div>
<div style="text-align:left; margin-top:19px; margin-bottom:19px; padding-top:0px; padding-bottom:0px">
Yes, the documentation is contained in the file <em style="text-align:left">VeraCrypt User Guide.chm</em> that is included in official VeraCrypt installer for Windows. You can also download the CHM using the link available at the home page
<a href="https://veracrypt.jp/en/Downloads.html" target="_blank">https://veracrypt.jp/en/downloads/</a>. Note that you do
<em style="text-align:left">not</em> have to install VeraCrypt to obtain the CHM documentation. Just run the self-extracting installation package and then select
<em style="text-align:left">Extract</em> (instead of <em style="text-align:left">
Install</em>) on the second page of the VeraCrypt Setup wizard. Also note that when you
<em style="text-align:left">do</em> install VeraCrypt, the CHM documentation is automatically copied to the folder to which VeraCrypt is installed, and is accessible via the VeraCrypt user interface (by pressing F1 or choosing
<em style="text-align:left">Help</em> &gt; <em style="text-align:left">User's Guide</em>).</div>
<div style="text-align:left; margin-top:19px; margin-bottom:19px; padding-top:0px; padding-bottom:0px">
<br style="text-align:left">
<strong style="text-align:left">Do I have to &quot;wipe&quot; free space and/or files on a VeraCrypt volume?</strong></div>
<div style="text-align:left; margin-top:19px; margin-bottom:19px; padding-top:0px; padding-bottom:0px">
<span style="text-align:left; font-size:10px; line-height:12px">Remark: to &quot;wipe&quot; = to securely erase; to overwrite sensitive data in order to render them unrecoverable.
</span><br style="text-align:left">
<br style="text-align:left">
If you believe that an adversary will be able to decrypt the volume (for example that he will make you reveal the password), then the answer is yes. Otherwise, it is not necessary, because the volume is entirely encrypted.</div>
<div style="text-align:left; margin-top:19px; margin-bottom:19px; padding-top:0px; padding-bottom:0px">
<br style="text-align:left">
<strong style="text-align:left">How does VeraCrypt know which encryption algorithm my VeraCrypt volume has been encrypted with?</strong></div>
<div style="text-align:left; margin-top:19px; margin-bottom:19px; padding-top:0px; padding-bottom:0px">
Please see the section <a href="Encryption%20Scheme.html" target="_blank" style="text-align:left; color:#0080c0; text-decoration:none">
Encryption Scheme</a> (chapter <a href="Technical%20Details.html" target="_blank" style="text-align:left; color:#0080c0; text-decoration:none">
Technical Details</a>) in the <a href="https://veracrypt.jp/en/Documentation.html" target="_blank" style="text-align:left; color:#0080c0; text-decoration:none">
documentation</a>.</div>
<div style="text-align:left; margin-top:19px; margin-bottom:19px; padding-top:0px; padding-bottom:0px">
<strong style="text-align:left">How can I perform a Windows built-in backup on a VeraCrypt volume? The VeraCrypt volume doesn't show up in the list of available backup paths.<br>
</strong>
<div style="text-align:left; margin-top:19px; margin-bottom:19px; padding-top:0px; padding-bottom:0px">
Windows built-in backup utility looks only for physical driver, that's why it doesn't display the VeraCrypt volume. Nevertheless, you can still backup on a VeraCrypt volume by using a trick: activate sharing on the VeraCrypt volume through Explorer interface
 (of course, you have to put the correct permission to avoid unauthorized access) and then choose the option &quot;Remote shared folder&quot; (it is not remote of course but Windows needs a network path). There you can type the path of the shared drive (for example \\ServerName\sharename)
 and the backup will be configured correctly.</div>
</div>
<div style="text-align:left; margin-top:19px; margin-bottom:19px; padding-top:0px; padding-bottom:0px">
<strong style="text-align:left">Is the encryption used by VeraCrypt vulnerable to Quantum attacks?</strong>
<div style="text-align:left; margin-top:19px; margin-bottom:19px; padding-top:0px; padding-bottom:0px">
VeraCrypt uses block ciphers (AES, Serpent, Twofish) for its encryption. Quantum attacks against these block ciphers are just a faster brute-force since the best know attack against these algorithms is exhaustive search (related keys attacks are irrelevant
 to our case because all keys are random and independent from each other).<br>
Since VeraCrypt always uses 256-bit random and independent keys, we are assured of a 128-bit security<br>
level against quantum algorithms which makes VeraCrypt encryption immune to such attacks.</div>
<div style="text-align:left; margin-top:19px; margin-bottom:19px; padding-top:0px; padding-bottom:0px">
<strong>How to make a VeraCrypt volume available for Windows Search indexing?</strong></div>
<div style="text-align:left; margin-top:19px; margin-bottom:19px; padding-top:0px; padding-bottom:0px">
In order to be able to index a VeraCrypt volume through Windows Search, the volume must be mounted at boot time (System Favorite) or the Windows Search services must be restart after the volume is mounted. This is needed because Windows Search can only index
 drives that are available when it starts.</div>
 <div style="text-align:left; margin-top:19px; margin-bottom:19px; padding-top:0px; padding-bottom:0px">
<strong>I'm encountering an "Operation not permitted" error with VeraCrypt on macOS when trying to mount a file container. How can I resolve this?</strong></div>
<div style="text-align:left; margin-top:19px; margin-bottom:19px; padding-top:0px; padding-bottom:0px">

<p>This specific error, which appears in the form "Operation not permitted: /var/folders/w6/d2xssyzx.../T/.veracrypt_aux_mnt1/control VeraCrypt::File::Open:232", has been reported by some users. It is the result of macOS not granting the necessary permissions to VeraCrypt. Here are a couple of solutions you can try:</p>

<ul>
<li>A. Granting Full Disk Access to VeraCrypt:
<p>
<ol>
    <li>Go to <code>Apple Menu</code> > <code>System Settings</code>.</li>
    <li>Click on the <code>Privacy & Security</code> tab.</li>
    <li>Scroll down and select <code>Full Disk Access</code>.</li>
    <li>Click the <code>+</code> button, navigate to your Applications folder, select <code>VeraCrypt</code>, and click <code>Open</code>.</li>
    <li>Ensure that the checkbox next to VeraCrypt is ticked.</li>
    <li>Close the System Settings window and try using VeraCrypt again.</li>
</p>
</ol>
</li>
<li>B. Using the sudo approach to launch VeraCrypt:
<p>You can launch VeraCrypt from the Terminal using elevated permissions:

<pre>
sudo /Applications/VeraCrypt.app/Contents/MacOS/VeraCrypt
</pre>

Running VeraCrypt with sudo often bypasses certain permission-related issues, but it's always a good practice to grant the necessary permissions via the system settings whenever possible.</p>
</li>
</ul>
</div>
<div style="text-align:left; margin-top:19px; margin-bottom:19px; padding-top:0px; padding-bottom:0px">
<strong style="text-align:left">Why does VeraCrypt show an unknown device in its list that doesn't appear as a physical disk in Windows Disk Management or in DiskPart output?</strong></div>
<div style="text-align:left; margin-top:19px; margin-bottom:19px; padding-top:0px; padding-bottom:0px">
<p>
Starting from Windows 10 version 1903 and later, Microsoft introduced a feature called <b>Windows Sandbox</b>. This is an isolated environment designed to run untrusted applications safely. As part of this feature, Windows generates a dynamic virtual hard disk (VHDX) which represents a clean Windows installation. This VHDX contains a base system image, user data, and the runtime state, and its size can vary depending on system configurations and usage.	
</p>
<p>
When VeraCrypt enumerates devices on a system, it identifies all available disk devices using device path formats like <b>\Device\HardDiskX\PartitionY</b>. VeraCrypt lists these devices, including virtual ones such as those associated with Windows Sandbox, without making distinctions based on their physical or virtual nature. Therefore, you might observe an unexpected device in VeraCrypt, even if it doesn't appear as a physical disk in tools like diskpart.
</p>
<p>
For more details on the Windows Sandbox feature and its associated virtual hard disk, you can refer to this <a href="https://techcommunity.microsoft.com/t5/windows-os-platform-blog/windows-sandbox/ba-p/301849">official Microsoft article</a>.
</p>
</div>
<div style="text-align:left; margin-top:19px; margin-bottom:19px; padding-top:0px; padding-bottom:0px">
<strong style="text-align:left">I haven't found any answer to my question in the FAQ &ndash; what should I do?</strong></div>
<div style="text-align:left; margin-top:19px; margin-bottom:19px; padding-top:0px; padding-bottom:0px">
Please search the VeraCrypt documentation and website.</div>
</div><div class="ClearBoth"></div></div></body></html>
