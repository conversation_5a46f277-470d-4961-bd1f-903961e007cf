<!DOCTYPE html>
<html lang="en">
<head>
<meta http-equiv="content-type" content="text/html; charset=utf-8" />
<title>VeraCrypt - Free Open source disk encryption with strong security for the Paranoid</title>
<meta name="description" content="VeraCrypt is free open-source disk encryption software for Windows, Mac OS X and Linux. In case an attacker forces you to reveal the password, VeraCrypt provides plausible deniability. In contrast to file encryption, data encryption performed by VeraCrypt is real-time (on-the-fly), automatic, transparent, needs very little memory, and does not involve temporary unencrypted files."/>
<meta name="keywords" content="encryption, security"/>
<link href="styles.css" rel="stylesheet" type="text/css" />
</head>
<body>

<div>
<a href="Documentation.html"><img src="VeraCrypt128x128.png" alt="VeraCrypt"/></a>
</div>

<div id="menu">
	<ul>
	  <li><a href="Home.html">Home</a></li>
	  <li><a href="Code.html">Source Code</a></li>
	  <li><a href="Downloads.html">Downloads</a></li>
	  <li><a class="active" href="Documentation.html">Documentation</a></li>
	  <li><a href="Donation.html">Donate</a></li>
	  <li><a href="https://sourceforge.net/p/veracrypt/discussion/" target="_blank">Forums</a></li>
	</ul>
</div>

<div>
<p>
<a href="Documentation.html">Documentation</a>
<img src="arrow_right.gif" alt=">>" style="margin-top: 5px">
<a href="Default%20Mount%20Parameters.html">Default Mount Parameters</a>
</p></div>

<div class="wikidoc">
<h2>Default Mount Parameters</h2>
<p>Starting from version 1.0f-2, it is possible to specify the PRF algorithm and the TrueCrypt mode that will be selected by default in the password dialog.</p>
<p>As show below, select the entry &quot;Default Mount Parameters&quot; under the menu &quot;Settings&quot;:</p>
<p><img src="Home_VeraCrypt_menu_Default_Mount_Parameters.png" alt="Menu Default Mount Parameters"></p>
<p>&nbsp;</p>
<p>The following dialog will be displayed:</p>
<p><img src="Home_VeraCrypt_Default_Mount_Parameters.png" alt="Default Mount Parameters Dialog"></p>
<p>Make your modifications and then click OK.</p>
<p>The chosen values are then written to VeraCrypt main configuration file (Configuration.xml) making them persistent.</p>
<p>All subsequent password request dialogs will use the default values chosen previously. For example, if in the Default Mount Parameters dialog you check TrueCrypt Mode and you select SHA-512 as a PRF, then subsequent password dialogs will look like:<br>
<img src="Default Mount Parameters_VeraCrypt_password_using_default_parameters.png" alt="Mount Password Dialog using default values"></p>
<p>&nbsp;</p>
<p><strong>Note:</strong> The default mount parameters can be overridden by the&nbsp;<a href="Command%20Line%20Usage.html">Command Line</a> switches
<strong>/tc</strong> and <strong>/hash</strong> which always take precedence.</p>
<p>&nbsp;</p>
</div><div class="ClearBoth"></div></body></html>
