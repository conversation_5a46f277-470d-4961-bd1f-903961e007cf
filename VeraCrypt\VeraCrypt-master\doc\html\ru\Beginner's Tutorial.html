﻿<!DOCTYPE html>
<html lang="ru">
<head>
<meta http-equiv="content-type" content="text/html; charset=utf-8" />
<title>VeraCrypt - Бесплатное надёжное шифрование дисков с открытым исходным кодом</title>
<meta name="description" content="VeraCrypt это бесплатное программное обеспечение для шифрования дисков с открытым исходным кодом для Windows, Mac OS X (macOS) и Linux. В случае, если злоумышленник вынуждает вас раскрыть пароль, VeraCrypt обеспечивает правдоподобное отрицание наличия шифрования. В отличие от пофайлового шифрования, VeraCrypt шифрует данные в реальном времени (на лету), автоматически, прозрачно, требует очень мало памяти и не использует временные незашифрованные файлы."/>
<meta name="keywords" content="encryption, security, шифрование, безопасность"/>
<link href="styles.css" rel="stylesheet" type="text/css" />
</head>
<body>

<div>
<a href="Documentation.html"><img src="VeraCrypt128x128.png" alt="VeraCrypt"/></a>
</div>

<div id="menu">
	<ul>
	  <li><a href="Home.html">Начало</a></li>
	  <li><a href="Code.html">Исходный код</a></li>
	  <li><a href="Downloads.html">Загрузить</a></li>
	  <li><a class="active" href="Documentation.html">Документация</a></li>
	  <li><a href="Donation.html">Поддержать разработку</a></li>
	  <li><a href="https://sourceforge.net/p/veracrypt/discussion/" target="_blank">Форум</a></li>
	</ul>
</div>

<div>
<p>
<a href="Documentation.html">Документация</a>
<img src="arrow_right.gif" alt=">>" style="margin-top: 5px">
<a href="Beginner's%20Tutorial.html">Руководство для начинающих пользователей</a>
</p></div>

<div class="wikidoc">
<h1>Руководство для начинающих пользователей</h1>
<h2>Как создать и использовать контейнер VeraCrypt</h2>
<p>В этой главе содержатся пошаговые инструкции о том, как создавать, монтировать и использовать том VeraCrypt.
Настоятельно рекомендуем вам ознакомиться и с другими разделами данного руководства, так как они содержат важную информацию.</p>
<h4>ШАГ 1:</h4>
<p>Если вы этого ещё не сделали, загрузите и установите программу VeraCrypt. Затем запустите её, дважды щёлкнув
по файлу VeraCrypt.exe или по ярлыку VeraCrypt в меню <i>Пуск</i> в Windows.</p>
<h4>ШАГ 2:</h4>
<p><img src="Beginner's Tutorial_Image_001.png" alt=""><br>
<br>
Должно появиться главное окно VeraCrypt. Нажмите кнопку <strong>Создать том</strong> (на иллюстрации она выделена красным).</p>
<h4>ШАГ 3:</h4>
<p><img src="Beginner's Tutorial_Image_002.png" alt=""><br>
<br>
Должно появиться окно мастера создания томов VeraCrypt.<br>
<br>
На этом этапе нам нужно выбрать место, где будет создан том VeraCrypt. Том может находиться в файле
(также именуемом контейнером), в разделе или на диске. В этом примере мы выберем первый вариант и
создадим том VeraCrypt внутри файла.
<br>
<br>
Поскольку эта опция выбрана по умолчанию, просто нажимаем кнопку <strong>Далее</strong>.</p>
<h4>ШАГ 4:</h4>
<p><img src="Beginner's Tutorial_Image_003.png" alt=""><br>
<br>
Сейчас нам нужно выбрать, какой том VeraCrypt мы хотим создать – обычный или скрытый. В этом примере мы
выберем первый вариант и создадим обычный том.<br>
<br>
Поскольку эта опция выбрана по умолчанию, просто нажимаем кнопку <strong>Далее</strong>.</p>
<h4>ШАГ 5:</h4>
<p><img src="Beginner's Tutorial_Image_004.png" alt=""><br>
<br>
На этом этапе требуется указать место создания тома (файла-контейнера) VeraCrypt. Обратите внимание: контейнер
VeraCrypt ничем не отличается от любого другого обычного файла. Например, его можно перемещать или удалять
как любой другой файл. Также ему потребуется имя файла, которое мы выберем на следующем этапе.<br>
<br>
Нажмите кнопку <strong>Выбрать файл</strong>.<br>
<br>
Появится стандартное диалоговое окно выбора файлов Windows (при этом окно мастера создания томов останется открытым в фоне).</p>
<h4>ШАГ 6:</h4>
<p><img src="Beginner's Tutorial_Image_005.png" alt=""><br>
<br>
В этом руководстве мы создадим наш том VeraCrypt в папке F<em>:\Data\ </em>
и присвоим тому (файлу-контейнеру) имя <em>MyVolume.hc</em> (как показано на иллюстрации выше). Разумеется,
вы можете выбрать любое другое имя и расположение файла (например, поместив его на USB-флешку). Обратите внимание,
что файла <em>MyVolume.hc</em> пока не существует &ndash; VeraCrypt его создаст.</p>
<p>ВАЖНО: Имейте в виду, что VeraCrypt <em>не будет</em> шифровать никакие имеющиеся файлы (при создании
файла-контейнера VeraCrypt). Если на данном этапе выбрать какой-либо уже существующий файл, он будет
перезаписан и заменён новым созданным томом (то есть перезаписанный файл будет <em>уничтожен</em>, а <em>не</em>
зашифрован). Зашифровать имеющиеся файлы вы сможете позднее, переместив их в том VeraCrypt, который мы сейчас создаём.*</p>
<p>Выберите в файловом окне желаемый путь (место, где вы хотите создать контейнер). В поле
<strong>Имя файла</strong> введите имя, которое вы хотите дать файлу-контейнеру.<br>
<br>
Нажмите кнопку <strong>Сохранить</strong>.<br>
<br>
Окно выбора файлов должно исчезнуть.<br>
<br>
На следующих этапах мы вернёмся в окно мастера создания томов  VeraCrypt.</p>
<p>* Обратите внимание, что после того, как вы скопируете существующие незашифрованные файлы на том VeraCrypt,
вы должны надёжно удалить (затереть) исходные незашифрованные файлы. Для надёжного стирания существуют
специальные программы (многие из которых бесплатны).</p>
<h4>ШАГ 7:</h4>
<p><img src="Beginner's Tutorial_Image_007.png" alt=""><br>
<br>
В окне мастера создания томов нажмите <strong>Далее</strong>.</p>
<h4>ШАГ 8:</h4>
<p><img src="Beginner's Tutorial_Image_008.png" alt=""><br>
<br>
Здесь можно выбрать для тома алгоритмы шифрования и хеширования. Если вы не знаете, что лучше выбрать,
просто оставьте предложенные значения и нажмите
<strong>Далее</strong> (см. подробности в главах <a href="Encryption Algorithms.html">
<em>Алгоритмы шифрования</em></a> и <a href="Hash%20Algorithms.html">
<em>Алгоритмы хеширования</em></a>).</p>
<h4>ШАГ 9:</h4>
<p><img src="Beginner's Tutorial_Image_009.png" alt=""><br>
<br>
Здесь мы укажем, что хотим создать контейнер VeraCrypt размером 250 мегабайт. Разумеется, вы можете
указать любой другой размер. После того, как вы введёте размер в поле ввода (оно выделено красным),
нажмите кнопку <strong>Далее</strong>.</p>
<h4>ШАГ 10:</h4>
<p><img src="Beginner's Tutorial_Image_010.png" alt=""><br>
<br>
Мы подошли к одному из самых важных этапов: нам нужно выбрать для тома хороший пароль. Какой пароль
следует считать хорошим, написано в этом окне мастера. Внимательно прочитайте данную информацию.<br>
<br>
После того, как вы определитесь с хорошим паролем, введите его в первое поле ввода. Затем введите тот же
самый пароль в расположенное ниже второе поле ввода и нажмите кнопку <strong>Далее</strong>.</p>
<p>Примечание: кнопка <strong>Далее</strong> будет недоступна до тех пор, пока в полях ввода не будут
введены одинаковые пароли.</p>
<h4>ШАГ 11:</h4>
<p><img src="Beginner's Tutorial_Image_011.png" alt=""><br>
<br>
Произвольно перемещайте мышь в окне мастера создания томов в течение хотя бы 30 секунд. Чем дольше вы
будете перемещать мышь, тем лучше – этим вы значительно повысите криптостойкость ключей шифрования
(что увеличит их надёжность).<br>
<br>
Нажмите кнопку <strong>Разметить</strong>.<br>
<br>
Сейчас должно начаться создание тома. VeraCrypt создаст файл с именем <em>MyVolume.hc</em>
 в папке <em>F:\Data\</em> (как мы указали в шаге 6). Этот файл станет контейнером VeraCrypt (то есть он
 будет содержать зашифрованный том VeraCrypt). В зависимости от размера тома, создание тома может
 длиться довольно долго. По окончании появится следующее окно:<br>
<br>
<img src="Beginner's Tutorial_Image_012.png" alt=""><br>
<br>
Нажмите <strong>OK</strong>, чтобы закрыть это окно.</p>
<h4>ШАГ 12:</h4>
<p><img src="Beginner's Tutorial_Image_013.png" alt=""><br>
<br>
Итак, только что мы успешно создали том VeraCrypt (файловый контейнер). Нажмите кнопку
<strong>Выход</strong> в окне мастера создания томов VeraCrypt.<br>
<br>
Окно мастера должно исчезнуть.<br>
<br>
На следующих этапах мы смонтируем том, который только что создали. Сейчас мы должны были вернуться
в главное окно VeraCrypt (которое должно быть всё ещё открыто, в противном случае выполните заново шаг 1,
чтобы запустить VeraCrypt, а затем перейдите к шагу 13).</p>
<h4>ШАГ 13:</h4>
<p><img src="Beginner's Tutorial_Image_014.png" alt=""><br>
<br>
Выберите в списке букву диска (на иллюстрации она помечена красным). Она станет буквой диска
со смонтированным контейнером VeraCrypt.<br>
<br>
Примечание: в нашем примере мы выбрали букву диска M, но вы, разумеется, можете выбрать любую другую доступную букву.</p>
<h4>ШАГ 14:</h4>
<p><img src="Beginner's Tutorial_Image_015.png" alt=""><br>
<br>
Нажмите кнопку <strong>Выбрать файл</strong>.<br>
<br>
Появится обычное окно выбора файлов.</p>
<h4>ШАГ 15:</h4>
<p><img src="Beginner's Tutorial_Image_016.png" alt=""><br>
<br>
В окне выбора файлов найдите и укажите файловый контейнер (который мы создали на шагах 6-12). Нажмите кнопку
<strong>Открыть</strong> (в окне выбора файлов).<br>
<br>
Окно выбора файлов должно исчезнуть.<br>
<br>
На следующих этапах мы вернёмся в главное окно VeraCrypt.</p>
<h4>ШАГ 16:</h4>
<p><img src="Beginner's Tutorial_Image_017.png" alt=""><br>
<br>
В главном окне VeraCrypt нажмите кнопку <strong>Смонтировать</strong>. Появится окно ввода пароля.</p>
<h4>ШАГ 17:</h4>
<p><img src="Beginner's Tutorial_Image_018.png" alt=""><br>
<br>
Укажите пароль (который мы задали на шаге 10) в поле ввода (на иллюстрации оно отмечено красным).</p>
<h4>ШАГ 18:</h4>
<p><img src="Beginner's Tutorial_Image_019.png" alt=""><br>
<br>
Выберите алгоритм PRF, который был использован при создании тома (по умолчанию VeraCrypt использует
PRF-алгоритм SHA-512). Если вы не помните, какой PRF использовался, просто оставьте здесь автоопределение,
но монтирование в этом случае займёт большее время. После ввода пароля нажмите <strong>OK</strong>.<br>
<br>
Сейчас VeraCrypt попытается смонтировать наш том. Если пароль указан неправильно (например, вы ошиблись
при вводе), VeraCrypt известит вас об этом, и потребуется повторить предыдущий этап (снова ввести пароль и
нажать <strong>OK</strong>). Если пароль правильный, том будет смонтирован.</p>
<h4>ШАГ 19 (ЗАКЛЮЧИТЕЛЬНЫЙ):</h4>
<p><img src="Beginner's Tutorial_Image_020.png" alt=""><br>
<br>
Итак, мы только что успешно смонтировали контейнер как виртуальный диск M:.<br>
<br>
Этот виртуальный диск полностью зашифрован (в том числе зашифрованы имена файлов, таблицы распределения,
свободное место и т. д.) и ведёт себя как настоящий диск. Вы можете сохранять (или копировать, перемещать
и т. д.) файлы на этом виртуальном диске – они будут шифроваться на лету в момент записи.<br>
<br>
Если вы откроете файл, хранящийся в томе VeraCrypt, например, в медиапроигрывателе, этот файл будет
автоматически расшифровываться в памяти (в ОЗУ) непосредственно в момент считывания, то есть на лету.</p>
<p><i>ВАЖНО: Обратите внимание, что когда вы открываете файл, хранящийся в томе VeraCrypt (или когда
сохраняете/копируете файл в томе VeraCrypt), повторно пароль не запрашивается. Правильный пароль нужно
указать только один раз – при монтировании тома.</i></p>
<p>Открыть смонтированный том можно, например, двойным щелчком по элементу, выделенному на иллюстрации синим цветом.</p>
<p>Просматривать содержимое смонтированного тома можно точно так же, как содержимое любого другого диска.
Например, открыть <em>Компьютер</em> (или <em>Мой компьютер</em>) и дважды щёлкнуть по соответствующей
букве диска (в нашем случае это буква M).<br>
<br>
<img src="Beginner's Tutorial_Image_021.png" alt=""><br>
<br>
Вы можете копировать файлы (или папки) в/из том(а) VeraCrypt, как если бы вы копировали их в любой другой
обычный диск (например, с помощью перетаскивания). Файлы, считываемые или копируемые из зашифрованного
тома VeraCrypt, автоматически на лету расшифровываются в ОЗУ (в памяти). Аналогично, файлы, записываемые
или копируемые в том VeraCrypt, автоматически зашифровываются на лету в ОЗУ (непосредственно перед их записью на диск).<br>
<br>
Обратите внимание, что VeraCrypt никогда не сохраняет на диске данные в незашифрованном виде – незашифрованные
данные хранятся лишь временно в ОЗУ (памяти). Даже при смонтированном томе данные в этом томе остаются
зашифрованными. При перезагрузке Windows или выключении компьютера том будет размонтирован, а все находящиеся
в нём файлы станут недоступными (и зашифрованными). Даже в случае случайного перебоя электропитания (то есть
при некорректном завершении работы системы) все хранящиеся в томе файлы будут недоступными (и зашифрованными).
Чтобы снова получить к ним доступ, потребуется смонтировать том. Как это сделать, описано в шагах 13-18.</p>
<p>Если вам нужно закрыть том, сделав его содержимое недоступным, можно либо перезагрузить операционную систему,
либо размонтировать том. Для этого сделайте следующее:<br>
<br>
<img src="Beginner's Tutorial_Image_022.png" alt=""><br>
<br>
Выберите нужный том из списка смонтированных томов в главном окне VeraCrypt (на иллюстрации он отмечен красным)
и нажмите кнопку <strong>Размонтировать</strong> (на иллюстрации она также отмечена красным). Чтобы снова
получить доступ к хранящимся в томе файлам, потребуется смонтировать том. Как это сделать, описано в шагах 13-18.</p>
<h2>Как создать и использовать раздел/устройство, зашифрованное VeraCrypt</h2>
<p>Вместо создания файлов-контейнеров вы можете воспользоваться шифрованием физических разделов или дисков
(то есть создавать тома VeraCrypt на основе устройств). Чтобы это сделать, повторите шаги 1-3, но на шаге 3
выберите второй или третий параметр и следуйте инструкциям мастера.
При создании тома VeraCrypt на основе устройства внутри <em>несистемного</em> раздела/диска, монтирование
этого тома выполняется кнопкой <em>Автомонтирование</em> в главном окне VeraCrypt. Информацию о зашифрованных
<em>системных</em> разделах/дисках см. в главе <a href="System%20Encryption.html">
<em>Шифрование системы</em></a>.</p>
<p>ВАЖНО: <em>Настоятельно рекомендуем ознакомиться с другими главами этого руководства – они содержат важную
информацию, которая здесь опущена, чтобы проще было объяснить азы.</em></p>
</div>
</body></html>
