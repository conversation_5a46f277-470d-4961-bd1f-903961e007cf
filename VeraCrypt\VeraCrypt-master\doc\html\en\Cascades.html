<!DOCTYPE html>
<html lang="en">
<head>
<meta http-equiv="content-type" content="text/html; charset=utf-8" />
<title>VeraCrypt - Free Open source disk encryption with strong security for the Paranoid</title>
<meta name="description" content="VeraCrypt is free open-source disk encryption software for Windows, Mac OS X and Linux. In case an attacker forces you to reveal the password, VeraCrypt provides plausible deniability. In contrast to file encryption, data encryption performed by VeraCrypt is real-time (on-the-fly), automatic, transparent, needs very little memory, and does not involve temporary unencrypted files."/>
<meta name="keywords" content="encryption, security"/>
<link href="styles.css" rel="stylesheet" type="text/css" />
</head>
<body>

<div>
<a href="Documentation.html"><img src="VeraCrypt128x128.png" alt="VeraCrypt"/></a>
</div>

<div id="menu">
	<ul>
	  <li><a href="Home.html">Home</a></li>
	  <li><a href="Code.html">Source Code</a></li>
	  <li><a href="Downloads.html">Downloads</a></li>
	  <li><a class="active" href="Documentation.html">Documentation</a></li>
	  <li><a href="Donation.html">Donate</a></li>
	  <li><a href="https://sourceforge.net/p/veracrypt/discussion/" target="_blank">Forums</a></li>
	</ul>
</div>

<div>
<p>
<a href="Documentation.html">Documentation</a>
<img src="arrow_right.gif" alt=">>" style="margin-top: 5px">
<a href="Encryption%20Algorithms.html">Encryption Algorithms</a>
<img src="arrow_right.gif" alt=">>" style="margin-top: 5px">
<a href="Cascades.html">Cascades of ciphers</a>
</p></div>

<div class="wikidoc">
<h1>Cascades of ciphers</h1>
<p>&nbsp;</p>
<h2>AES-Twofish</h2>
<p>Two ciphers in a cascade [15, 16] operating in XTS mode (see the section <a href="Modes%20of%20Operation.html">
<em>Modes of Operation</em></a>). Each 128-bit block is first encrypted with Twofish (256-bit key) in XTS mode and then with AES (256-bit key) in XTS mode. Each of the cascaded ciphers uses its own key. All encryption keys are mutually independent (note that
 header keys are independent too, even though they are derived from a single password &ndash; see
<a href="Header Key Derivation.html"><em>Header Key Derivation, Salt, and Iteration Count</em></a>). See above for information on the individual cascaded ciphers.</p>
<h2>AES-Twofish-Serpent</h2>
<p>Three ciphers in a cascade [15, 16] operating in XTS mode (see the section <a href="Modes%20of%20Operation.html">
<em>Modes of Operation</em></a>). Each 128-bit block is first encrypted with Serpent (256-bit key) in XTS mode, then with Twofish (256-bit key) in XTS mode, and finally with AES (256-bit key) in XTS mode. Each of the cascaded ciphers uses its own key. All encryption
 keys are mutually independent (note that header keys are independent too, even though they are derived from a single password &ndash; see the section
<a href="Header Key Derivation.html"><em>Header Key Derivation, Salt, and Iteration Count</em></a>). See above for information on the individual cascaded ciphers.</p>
<h2>Camellia-Kuznyechik</h2>
<p>Two ciphers in a cascade [15, 16] operating in XTS mode (see the section <a href="Modes%20of%20Operation.html">
<em>Modes of Operation</em></a>). Each 128-bit block is first encrypted with Kuznyechik (256-bit key) in XTS mode and then with Camellia (256-bit key) in XTS mode. Each of the cascaded ciphers uses its own key. All encryption keys are mutually independent (note that
 header keys are independent too, even though they are derived from a single password &ndash; see the section
<a href="Header Key Derivation.html"><em>Header Key Derivation, Salt, and Iteration Count</em></a>). See above for information on the individual cascaded ciphers.</p>
<h2>Camellia-Serpent</h2>
<p>Two ciphers in a cascade [15, 16] operating in XTS mode (see the section <a href="Modes%20of%20Operation.html">
<em>Modes of Operation</em></a>). Each 128-bit block is first encrypted with Serpent (256-bit key) in XTS mode and then with Camellia (256-bit key) in XTS mode. Each of the cascaded ciphers uses its own key. All encryption keys are mutually independent (note that
 header keys are independent too, even though they are derived from a single password &ndash; see the section
<a href="Header Key Derivation.html"><em>Header Key Derivation, Salt, and Iteration Count</em></a>). See above for information on the individual cascaded ciphers.</p>
<h2>Kuznyechik-AES</h2>
<p>Two ciphers in a cascade [15, 16] operating in XTS mode (see the section <a href="Modes%20of%20Operation.html">
<em>Modes of Operation</em></a>). Each 128-bit block is first encrypted with AES (256-bit key) in XTS mode and then with Kuznyechik (256-bit key) in XTS mode. Each of the cascaded ciphers uses its own key. All encryption keys are mutually independent (note that
 header keys are independent too, even though they are derived from a single password &ndash; see the section
<a href="Header Key Derivation.html"><em>Header Key Derivation, Salt, and Iteration Count</em></a>). See above for information on the individual cascaded ciphers.</p>
<h2>Kuznyechik-Serpent-Camellia</h2>
<p>Three ciphers in a cascade [15, 16] operating in XTS mode (see the section <a href="Modes%20of%20Operation.html">
<em>Modes of Operation</em></a>). Each 128-bit block is first encrypted with Camellia (256-bit key) in XTS mode, then with Serpent (256- bit key) in XTS mode, and finally with Kuznyechik (256-bit key) in XTS mode. Each of the cascaded ciphers uses its own key. All
 encryption keys are mutually independent (note that header keys are independent too, even though they are derived from a single password &ndash; see the section
<a href="Header Key Derivation.html"><em>Header Key Derivation, Salt, and Iteration Count</em></a>). See above for information on the individual cascaded ciphers.</p>
<h2>Kuznyechik-Twofish</h2>
<p>Two ciphers in a cascade [15, 16] operating in XTS mode (see the section <a href="Modes%20of%20Operation.html">
<em>Modes of Operation</em></a>). Each 128-bit block is first encrypted with Twofish (256-bit key) in XTS mode and then with Kuznyechik (256-bit key) in XTS mode. Each of the cascaded ciphers uses its own key. All encryption keys are mutually independent (note that
 header keys are independent too, even though they are derived from a single password &ndash; see the section
<a href="Header Key Derivation.html"><em>Header Key Derivation, Salt, and Iteration Count</em></a>). See above for information on the individual cascaded ciphers.</p>
<h2>Serpent-AES</h2>
<p>Two ciphers in a cascade [15, 16] operating in XTS mode (see the section <a href="Modes%20of%20Operation.html">
<em>Modes of Operation</em></a>). Each 128-bit block is first encrypted with AES (256-bit key) in XTS mode and then with Serpent (256-bit key) in XTS mode. Each of the cascaded ciphers uses its own key. All encryption keys are mutually independent (note that
 header keys are independent too, even though they are derived from a single password &ndash; see the section
<a href="Header Key Derivation.html"><em>Header Key Derivation, Salt, and Iteration Count</em></a>). See above for information on the individual cascaded ciphers.</p>
<h2>Serpent-Twofish-AES</h2>
<p>Three ciphers in a cascade [15, 16] operating in XTS mode (see the section <a href="Modes%20of%20Operation.html">
<em>Modes of Operation</em></a>). Each 128-bit block is first encrypted with AES (256-bit key) in XTS mode, then with Twofish (256- bit key) in XTS mode, and finally with Serpent (256-bit key) in XTS mode. Each of the cascaded ciphers uses its own key. All
 encryption keys are mutually independent (note that header keys are independent too, even though they are derived from a single password &ndash; see the section
<a href="Header Key Derivation.html"><em>Header Key Derivation, Salt, and Iteration Count</em></a>). See above for information on the individual cascaded ciphers.</p>
<h2>Twofish-Serpent</h2>
<p>Two ciphers in a cascade [15, 16] operating in XTS mode (see the section <a href="Modes%20of%20Operation.html">
<em>Modes of Operation</em></a>). Each 128-bit block is first encrypted with Serpent (256-bit key) in XTS mode and then with Twofish (256-bit key) in XTS mode. Each of the cascaded ciphers uses its own key. All encryption keys are mutually independent (note
 that header keys are independent too, even though they are derived from a single password &ndash; see the section
<a href="Header Key Derivation.html"><em>Header Key Derivation, Salt, and Iteration Count</em></a>). See above for information on the individual cascaded ciphers.</p>

</div>
</body></html>
