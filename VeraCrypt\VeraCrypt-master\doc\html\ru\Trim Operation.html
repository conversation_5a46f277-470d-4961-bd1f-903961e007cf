﻿<!DOCTYPE html>
<html lang="ru">
<head>
<meta http-equiv="content-type" content="text/html; charset=utf-8" />
<title>VeraCrypt - Бесплатное надёжное шифрование дисков с открытым исходным кодом</title>
<meta name="description" content="VeraCrypt это бесплатное программное обеспечение для шифрования дисков с открытым исходным кодом для Windows, Mac OS X (macOS) и Linux. В случае, если злоумышленник вынуждает вас раскрыть пароль, VeraCrypt обеспечивает правдоподобное отрицание наличия шифрования. В отличие от пофайлового шифрования, VeraCrypt шифрует данные в реальном времени (на лету), автоматически, прозрачно, требует очень мало памяти и не использует временные незашифрованные файлы."/>
<meta name="keywords" content="encryption, security, шифрование, безопасность"/>
<link href="styles.css" rel="stylesheet" type="text/css" />
</head>
<body>

<div>
<a href="Documentation.html"><img src="VeraCrypt128x128.png" alt="VeraCrypt"/></a>
</div>

<div id="menu">
	<ul>
	  <li><a href="Home.html">Начало</a></li>
	  <li><a href="Code.html">Исходный код</a></li>
	  <li><a href="Downloads.html">Загрузить</a></li>
	  <li><a class="active" href="Documentation.html">Документация</a></li>
	  <li><a href="Donation.html">Поддержать разработку</a></li>
	  <li><a href="https://sourceforge.net/p/veracrypt/discussion/" target="_blank">Форум</a></li>
	</ul>
</div>

<div>
<p>
<a href="Documentation.html">Документация</a>
<img src="arrow_right.gif" alt=">>" style="margin-top: 5px">
<a href="Security%20Requirements%20and%20Precautions.html">Требования безопасности и меры предосторожности</a>
<img src="arrow_right.gif" alt=">>" style="margin-top: 5px">
<a href="Trim%20Operation.html">Операция TRIM</a>
</p></div>

<div class="wikidoc">
<h1>Операция TRIM</h1>
<div style="text-align:left; margin-top:19px; margin-bottom:19px; padding-top:0px; padding-bottom:0px">
В ряде запоминающих устройств (например, в некоторых твердотельных накопителях, включая USB-флешки) для
маркировки секторов как свободных, например, при удалении файла, применяется так называемая операция "TRIM".
Вследствие этого такие сектора могут содержать незашифрованные нули или другие неопределённые данные
(незашифрованные), даже если они расположены внутри области диска, зашифрованной VeraCrypt.<br>
<br>

В <b>Windows</b> программа VeraCrypt позволяет пользователям управлять операцией TRIM как для несистемных, так и для системных томов:
<ul>
<li>Для несистемных томов TRIM по умолчанию заблокирована. Чтобы её включить, нужно в главном окне VeraCrypt
в меню <i>Настройки</i> выбрать <i>Производительность и драйвер</i> и включить опцию <i>Разрешить команду TRIM
для несистемных SSD-разделов/дисков</i>.</li>
<li>Для <a href="System%20Encryption.html">шифрования системы</a> операция TRIM по умолчанию разрешена (если
только не запущена <a href="Hidden%20Operating%20System.html">скрытая операционная система</a>). Чтобы отключить TRIM,
перейдите в <i>Система > Установки</i> и включите опцию <i>Блокировать команду TRIM на системном разделе/диске</i>.</li>
</ul>

В <b>Linux</b> программа VeraCrypt не блокирует операцию TRIM на томах, использующих собственные криптографические службы
ядра Linux, что является настройкой по умолчанию. Чтобы заблокировать TRIM в Linux, нужно либо включить опцию
"Не использовать криптографические службы ядра" в настройках VeraCrypt (применимо только к томам, смонтированным позднее),
либо использовать при монтировании ключ <code>--mount-options=nokernelcrypto</code> в командной строке.
<br>
<br>
В <b>macOS</b> программа VeraCrypt не поддерживает TRIM, поэтому данная операция всегда заблокирована на всех томах.
<br>
<br>
В случаях, когда выполняется операция TRIM, злоумышленник сможет определить, какие сектора содержат свободное
пространство (и может использовать эту информацию для дальнейшего анализа и атак), что может отрицательно сказаться
на <a href="Plausible%20Deniability.html" style="text-align:left; color:#0080c0; text-decoration:none">
правдоподобности отрицания шифрования</a>. Чтобы избежать этих проблем, следует либо отключить TRIM в настройках VeraCrypt,
как описано выше, либо убедиться, что тома VeraCrypt не расположены на дисках, которые используют операцию TRIM.</div>


<p>Выяснить, используется ли в устройстве операция TRIM, можно в документации на это устройство или
у его поставщика/производителя.</p>
</div><div class="ClearBoth"></div></body></html>
