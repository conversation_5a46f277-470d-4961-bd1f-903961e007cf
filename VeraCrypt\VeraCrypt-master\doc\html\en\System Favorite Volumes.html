<!DOCTYPE html>
<html lang="en">
<head>
<meta http-equiv="content-type" content="text/html; charset=utf-8" />
<title>VeraCrypt - Free Open source disk encryption with strong security for the Paranoid</title>
<meta name="description" content="VeraCrypt is free open-source disk encryption software for Windows, Mac OS X and Linux. In case an attacker forces you to reveal the password, VeraCrypt provides plausible deniability. In contrast to file encryption, data encryption performed by VeraCrypt is real-time (on-the-fly), automatic, transparent, needs very little memory, and does not involve temporary unencrypted files."/>
<meta name="keywords" content="encryption, security"/>
<link href="styles.css" rel="stylesheet" type="text/css" />
</head>
<body>

<div>
<a href="Documentation.html"><img src="VeraCrypt128x128.png" alt="VeraCrypt"/></a>
</div>

<div id="menu">
	<ul>
	  <li><a href="Home.html">Home</a></li>
	  <li><a href="Code.html">Source Code</a></li>
	  <li><a href="Downloads.html">Downloads</a></li>
	  <li><a class="active" href="Documentation.html">Documentation</a></li>
	  <li><a href="Donation.html">Donate</a></li>
	  <li><a href="https://sourceforge.net/p/veracrypt/discussion/" target="_blank">Forums</a></li>
	</ul>
</div>

<div>
<p>
<a href="Documentation.html">Documentation</a>
<img src="arrow_right.gif" alt=">>" style="margin-top: 5px">
<a href="VeraCrypt%20Volume.html">VeraCrypt Volume</a>
<img src="arrow_right.gif" alt=">>" style="margin-top: 5px">
<a href="System%20Favorite%20Volumes.html">System Favorite Volumes</a>
</p></div>

<div class="wikidoc">
<h1>System Favorite Volumes</h1>
<div style="text-align:left; margin-top:19px; margin-bottom:19px; padding-top:0px; padding-bottom:0px">
<p>System favorites are useful, for example, in the following cases:</p>
<ul>
<li>You have volumes that need to be <strong>mounted before system and application services start and before users start logging on</strong>.
</li><li>There are network-shared folders located on VeraCrypt volumes. If you configure these volumes as system favorites, you will ensure that the
<strong>network shares will be automatically restored </strong>by the operating system each time it is restarted.
</li><li>You need each such volume to be mounted as <strong>the same drive letter </strong>
each time the operating system starts. </li></ul>
<p>Note that, unlike the regular (non-system) favorites, <strong>system favorite volumes use the pre-boot authentication password
</strong>and, therefore, require your system partition/drive to be encrypted (also note it is not required to enable caching of the pre-boot authentication password). Moreover, since the pre-boot password is typed using
<strong>US keyboard layout</strong> (BIOS requirement), the password of the system favorite volume must be entered during its creation process using the
<strong>US keyboard layout</strong> by typing the same keyboard keys you type when you enter the pre-boot authentication password. If the password of the system favorite volume is not identical to the pre-boot authentication password under the US keyboard layout,
 then<strong> it will fail to mount</strong>.</p>
<p>When creating a volume that you want to make a system favorite later, you must explicitly set the keyboard layout associated with VeraCrypt to US layout and you have to type the same keyboard keys you type when you enter the pre-boot authentication password.<br>
<br>
System favorite volumes <strong>can be configured to be available within VeraCrypt only to users with administrator privileges
</strong>(select <em>Settings </em>&gt; &lsquo;<em>System Favorite Volumes</em>&rsquo; &gt; &lsquo;<em>Allow only administrators to view and unmount system favorite volumes in VeraCrypt</em>&rsquo;). This option should be enabled on servers to ensure that
 system favorite volumes cannot be unmounted by users without administrator privileges. On non-server systems, this option can be used to prevent normal VeraCrypt volume actions (such as &lsquo;<em>Unmount All</em>&rsquo;, auto-unmount, etc.) from affecting
 system favorite volumes. In addition, when VeraCrypt is run without administrator privileges (the default on Windows Vista and later), system favorite volumes will not be displayed in the drive letter list in the main VeraCrypt application window.</p>
<h3>To configure a VeraCrypt volume as a system favorite volume, follow these steps:</h3>
<ol>
<li>Mount the volume (to the drive letter to which you want it to be mounted every time).
</li><li>Right-click the mounted volume in the drive list in the main VeraCrypt window and select &lsquo;<em>Add to System Favorites</em>&rsquo;.
</li><li>The System Favorites Organizer window should appear now. In this window, enable the option &lsquo;<em>Mount system favorite volumes when Windows starts</em>&rsquo; and click
<em>OK</em>. </li></ol>
<p>The order in which system favorite volumes are displayed in the System Favorites Organizer window (<em>Favorites
</em>&gt; &lsquo;<em>Organize System Favorite Volumes</em>&rsquo;) is <strong>the order in which the volumes are mounted</strong>. You can use the
<em>Move Up </em>and <em>Move Down </em>buttons to change the order of the volumes.</p>
<p id="Label">A special label can be assigned to each system favorite volume. This label is not the same as the filesystem label and it is shown within the VeraCrypt user interface instead of the volume path. To assign such a label, follow these steps:</p>
<ol>
<li>Select <em>Favorites </em>&gt; &lsquo;<em>Organize System Favorite Volumes</em>&rsquo;.
</li><li>The System Favorites Organizer window should appear now. In this window, select the volume whose label you want to edit.
</li><li>Enter the label in the &lsquo;<em>Label of selected favorite volume</em>&rsquo; input field and click OK.
</li></ol>
<p>Note that the System Favorites Organizer window (<em>Favorites </em>&gt; &lsquo;<em>Organize System Favorite Volumes</em>&rsquo;) allows you to
<strong>set various options for each system favorite volume</strong>. For example, any of them can be mounted as read-only or as removable medium.<br>
<br>
Warning: When the drive letter assigned to a system favorite volume (saved in the configuration file) is not free, the volume is not mounted and no error message is displayed.<br>
<br>
Note that Windows needs to use some files (e.g. paging files, Active Directory files, etc.) before system favorite volumes are mounted. Therefore, such files cannot be stored on system favorite volumes. Note, however, that they
<em>can </em>be stored on any partition that is within the key scope of system encryption (e.g. on the system partition or on any partition of a system drive that is entirely encrypted by VeraCrypt).<br>
<br>
<strong>To remove a volume from the list of system favorite volumes</strong>, select
<em>Favorites </em>&gt; <em>Organize System Favorite Volumes</em>, select the volume, click
<em>Remove</em>, and click OK.</p>
</div>
</div>

</body></html>
