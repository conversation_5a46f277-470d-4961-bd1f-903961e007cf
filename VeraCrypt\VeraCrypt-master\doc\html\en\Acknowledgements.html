<!DOCTYPE html>
<html lang="en">
<head>
<meta http-equiv="content-type" content="text/html; charset=utf-8" />
<title>VeraCrypt - Free Open source disk encryption with strong security for the Paranoid</title>
<meta name="description" content="VeraCrypt is free open-source disk encryption software for Windows, Mac OS X and Linux. In case an attacker forces you to reveal the password, VeraCrypt provides plausible deniability. In contrast to file encryption, data encryption performed by VeraCrypt is real-time (on-the-fly), automatic, transparent, needs very little memory, and does not involve temporary unencrypted files."/>
<meta name="keywords" content="encryption, security"/>
<link href="styles.css" rel="stylesheet" type="text/css" />
</head>
<body>

<div>
<a href="Documentation.html"><img src="VeraCrypt128x128.png" alt="VeraCrypt"/></a>
</div>

<div id="menu">
	<ul>
	  <li><a href="Home.html">Home</a></li>
	  <li><a href="Code.html">Source Code</a></li>
	  <li><a href="Downloads.html">Downloads</a></li>
	  <li><a class="active" href="Documentation.html">Documentation</a></li>
	  <li><a href="Donation.html">Donate</a></li>
	  <li><a href="https://sourceforge.net/p/veracrypt/discussion/" target="_blank">Forums</a></li>
	</ul>
</div>

<div>
<p>
<a href="Documentation.html">Documentation</a>
<img src="arrow_right.gif" alt=">>" style="margin-top: 5px">
<a href="Acknowledgements.html">Acknowledgements</a>
</p></div>
<div class="wikidoc">
<div>
<h1>Acknowledgements</h1>
<p>We would like to thank the following people:</p>
<p>The TrueCrypt Developers team who have done an amazing job over the course of 10 years. Without their hard work, VeraCrypt would not exist today.</p>
<p>Paul Le Roux for making his E4M source code available. TrueCrypt 1.0 was derived from E4M and some parts of the E4M source code are still incorporated in the latest version of the TrueCrypt source code.</p>
<p>Brian Gladman, who wrote the excellent AES, Twofish, and SHA-512 routines.</p>
<p>Peter Gutmann for his paper on random numbers, and for creating his cryptlib, which was the source of parts of the random number generator source code.</p>
<p>Wei Dai, who wrote the Serpent and RIPEMD-160 and Whirlpool routines.</p>
<p>Tom St Denis, the author of LibTomCrypt which includes compact SHA-256 routines.</p>
<p>Mark Adler and Jean-loup Gailly, who wrote the zlib library.</p>
<p>The designers of the encryption algorithms, hash algorithms, and the mode of operation:</p>
<p>Horst Feistel, Don Coppersmith, Walt Tuchmann, Lars Knudsen, Ross Anderson, Eli Biham, Bruce Schneier, David Wagner, John Kelsey, Niels Ferguson, Doug Whiting, Chris Hall, Joan Daemen, Vincent Rijmen, Carlisle Adams, Stafford Tavares, Phillip Rogaway, Hans
 Dobbertin, Antoon Bosselaers, Bart Preneel, Paulo S. L. M. Barreto.</p>
<p>Andreas Becker for designing VeraCrypt logo and icons.</p>
<p>Xavier de Carn&eacute; de Carnavalet who proposed a speed optimization for PBKDF2 that reduced mount/boot time by half.</p>
<p>kerukuro for cppcrypto library (http://cppcrypto.sourceforge.net/) from which Kuznyechik cipher implementation was taken.</p>
<p><br>
Dieter Baron and Thomas Klausner who wrote the libzip library.</p>
<p><br>
Jack Lloyd who wrote the SIMD optimized Serpent implementation.</p>
<p>All the others who have made this project possible, all who have morally supported us, and all who sent us bug reports or suggestions for improvements.</p>
<p>Thank you very much.</p>
</div>
</div><div class="ClearBoth"></div></body></html>
