<!DOCTYPE html>
<html lang="en">
<head>
<meta http-equiv="content-type" content="text/html; charset=utf-8" />
<title>VeraCrypt - Free Open source disk encryption with strong security for the Paranoid</title>
<meta name="description" content="VeraCrypt is free open-source disk encryption software for Windows, Mac OS X and Linux. In case an attacker forces you to reveal the password, VeraCrypt provides plausible deniability. In contrast to file encryption, data encryption performed by VeraCrypt is real-time (on-the-fly), automatic, transparent, needs very little memory, and does not involve temporary unencrypted files."/>
<meta name="keywords" content="encryption, security"/>
<link href="styles.css" rel="stylesheet" type="text/css" />
</head>
<body>

<div>
<a href="Documentation.html"><img src="VeraCrypt128x128.png" alt="VeraCrypt"/></a>
</div>

<div id="menu">
	<ul>
	  <li><a href="Home.html">Home</a></li>
	  <li><a href="Code.html">Source Code</a></li>
	  <li><a href="Downloads.html">Downloads</a></li>
	  <li><a class="active" href="Documentation.html">Documentation</a></li>
	  <li><a href="Donation.html">Donate</a></li>
	  <li><a href="https://sourceforge.net/p/veracrypt/discussion/" target="_blank">Forums</a></li>
	</ul>
</div>

<div>
<p>
<a href="Documentation.html">Documentation</a>
<img src="arrow_right.gif" alt=">>" style="margin-top: 5px">
<a href="Technical%20Details.html">Technical Details</a>
<img src="arrow_right.gif" alt=">>" style="margin-top: 5px">
<a href="Personal%20Iterations%20Multiplier%20(PIM).html">PIM</a>
</p></div>

<div class="wikidoc">
<h1>PIM</h1>
<div>
<p>PIM stands for &quot;Personal Iterations Multiplier&quot;. It is a parameter that was introduced in VeraCrypt 1.12 and whose value controls the number of iterations used by the header key derivation function. This value can be specified through the password dialog
 or in the command line.</p>
<p>When a PIM value is specified, the number of iterations is calculated as follows:</p>
<ul>
<li>For system encryption that doesn't use SHA-512 or Whirlpool: Iterations = <strong>PIM x 2048</strong>
</li><li>For system encryption that uses SHA-512 or Whirlpool: Iterations = <strong>15000 &#43; (PIM x 1000)</strong>
</li><li>For non-system encryption and file containers: Iterations = <strong>15000 &#43; (PIM x 1000)</strong>
</li></ul>
<p>If no PIM value is specified, VeraCrypt will use the default number of iterations used in versions prior to 1.12 (see
	<a href="Header%20Key%20Derivation.html">
	Header Key Derivation</a>). This can be summarized as follows:<br/>
		<ul>
		<li>For system partition encryption (boot encryption) that uses SHA-256, BLAKE2s-256 or Streebog, <strong>200000</strong> iterations are used which is equivalent to a PIM value of <strong>98</strong>.</li>
		<li>For system encryption that uses SHA-512 or Whirlpool, <strong>500000</strong> iterations are used which is equivalent to a PIM value of <strong>485</strong>.</li>
		<li>For non-system encryption and file containers, all derivation algorithms will use <strong>500000</strong> iterations which is equivalent to a PIM value of <strong>485</strong>.</li>
		</ul>
</p>
<p>Prior to version 1.12, the security of a VeraCrypt volume was only based on the password strength because VeraCrypt was using a fixed number of iterations.<br>
With the introduction of PIM, VeraCrypt has a 2-dimensional security space for volumes based on the couple (Password, PIM). This provides more flexibility for adjusting the desired security level while also controlling the performance of the mount/boot operation.</p>
<h3>PIM Usage</h3>
It is not mandatory to specify a PIM.</div>
<div><br>
When creating a volume or when changing the password, the user has the possibility to specify a PIM value by checking the &quot;Use PIM&quot; checkbox which in turn will make a PIM field available in the GUI so a PIM value can be entered.</div>
<div>&nbsp;</div>
<div>The PIM is treated like a secret value that must be entered by the user each time alongside the password. If the incorrect PIM value is specified, the mount/boot operation will fail.</div>
<div>&nbsp;</div>
<div>Using high PIM values leads to better security thanks to the increased number of iterations but it comes with slower mounting/booting times.</div>
<div>With small PIM values, mounting/booting is quicker but this could decrease security if a weak password is used.</div>
<div>&nbsp;</div>
<div>During the creation of a volume or the encryption of the system, VeraCrypt forces the PIM value to be greater than or equal to a certain minimal value when the password is less than 20 characters. This check is done in order to ensure that, for short passwords,
 the security level is at least equal to the default level provided by an empty PIM.</div>
<div>&nbsp;</div>
<div>The PIM minimal value for short passwords is <strong>98</strong> for system encryption that doesn't use SHA-512 or Whirlpool and
<strong>485</strong> for the other cases. For password with 20 characters and more, the PIM minimal value is
<strong>1</strong>. In all cases, leaving the PIM empty or setting its value to 0 will make VeraCrypt use the default high number of iterations as explained in section
<a href="Header%20Key%20Derivation.html">
Header Key Derivation</a>.</div>
<div><br>
Motivations behind using a custom PIM value can be:<br>
<ul>
<li>Add an extra secret parameter (PIM) that an attacker will have to guess </li><li>Increase security level by using large PIM values to thwart future development of brute force attacks.
</li><li>Speeding up booting or mounting through the use of a small PIM value (less than 98 for system encryption that doesn't use SHA-512 or Whirlpool and less than 485 for the other cases)
</li></ul>
<p>The screenshots below show the step to mount a volume using a PIM equal to 231:</p>
<table style="margin-left:auto; margin-right:auto">
<tbody>
<tr>
<td><img src="Personal Iterations Multiplier (PIM)_VeraCrypt_UsePIM_Step1.png" alt=""></td>
</tr>
<tr>
<td><img src="Personal Iterations Multiplier (PIM)_VeraCrypt_UsePIM_Step2.png" alt=""></td>
</tr>
</tbody>
</table>
<p>&nbsp;</p>
<h3>Changing/clearing the PIM</h3>
<p>The PIM of a volume or for system encryption can be changed or cleared using the change password functionality. The screenshots below shows an example of changing the PIM from the empty default value to a value equal to 3 (this is possible since the password
 has more than 20 characters). In order to do so, the user must first tick &quot;Use PIM&quot; checkbox in the &quot;New&quot; section to reveal the PIM field.</p>
<table width="519" style="height:896px; width:519px; margin-left:auto; margin-right:auto">
<caption><strong>Normal volume case</strong></caption>
<tbody>
<tr>
<td style="text-align:center"><img src="Personal Iterations Multiplier (PIM)_VeraCrypt_ChangePIM_Step1.png" alt=""></td>
</tr>
<tr>
<td>
<p><img src="Personal Iterations Multiplier (PIM)_VeraCrypt_ChangePIM_Step2.png" alt=""></p>
</td>
</tr>
</tbody>
</table>
<h5>&nbsp;</h5>
<table style="margin-left:auto; margin-right:auto">
<caption><strong>System encryption case</strong></caption>
<tbody>
<tr>
<td><img src="Personal Iterations Multiplier (PIM)_VeraCrypt_ChangePIM_System_Step1.png" alt=""></td>
</tr>
<tr>
<td><img src="Personal Iterations Multiplier (PIM)_VeraCrypt_ChangePIM_System_Step2.png" alt=""></td>
</tr>
</tbody>
</table>
<p>&nbsp;</p>
<p><a href="VeraCrypt%20Volume%20Format%20Specification.html" style="text-align:left; color:#0080c0; text-decoration:none; font-weight:bold">Next Section &gt;&gt;</a></p>
</div>
</div><div class="ClearBoth"></div></body></html>
