﻿<!DOCTYPE html>
<html lang="ru">
<head>
<meta http-equiv="content-type" content="text/html; charset=utf-8" />
<title>VeraCrypt - Бесплатное надёжное шифрование дисков с открытым исходным кодом</title>
<meta name="description" content="VeraCrypt это бесплатное программное обеспечение для шифрования дисков с открытым исходным кодом для Windows, Mac OS X (macOS) и Linux. В случае, если злоумышленник вынуждает вас раскрыть пароль, VeraCrypt обеспечивает правдоподобное отрицание наличия шифрования. В отличие от пофайлового шифрования, VeraCrypt шифрует данные в реальном времени (на лету), автоматически, прозрачно, требует очень мало памяти и не использует временные незашифрованные файлы."/>
<meta name="keywords" content="encryption, security, шифрование, безопасность"/>
<link href="styles.css" rel="stylesheet" type="text/css" />
</head>
<body>

<div>
<a href="Documentation.html"><img src="VeraCrypt128x128.png" alt="VeraCrypt"/></a>
</div>

<div id="menu">
	<ul>
	  <li><a href="Home.html">Начало</a></li>
	  <li><a href="Code.html">Исходный код</a></li>
	  <li><a href="Downloads.html">Загрузить</a></li>
	  <li><a class="active" href="Documentation.html">Документация</a></li>
	  <li><a href="Donation.html">Поддержать разработку</a></li>
	  <li><a href="https://sourceforge.net/p/veracrypt/discussion/" target="_blank">Форум</a></li>
	</ul>
</div>

<div>
<p>
<a href="Documentation.html">Документация</a>
<img src="arrow_right.gif" alt=">>" style="margin-top: 5px">
<a href="Main%20Program%20Window.html">Главное окно программы</a>
<img src="arrow_right.gif" alt=">>" style="margin-top: 5px">
<a href="Mounting%20VeraCrypt%20Volumes.html">Монтирование томов</a>
</p></div>

<div class="wikidoc">
<h1>Монтирование томов VeraCrypt</h1>
<div style="text-align:left; margin-top:19px; margin-bottom:19px; padding-top:0px; padding-bottom:0px">
<p>Если вы этого ещё не сделали, прочитайте разделы <em>Смонтировать</em> и <em>Автомонтирование</em> в главе
<a href="Main%20Program%20Window.html"><em>Главное окно программы</em></a>.</p>
<h3>Кэшировать пароли и ключевые файлы в ОЗУ</h3>
<p>Этот параметр можно задать в окне ввода пароля, чтобы он применялся только к этой конкретной попытке
монтирования. Он также может быть установлен как используемый по умолчанию в настройках программы.
См. подробности в разделе <a href="Program%20Menu.html"><em>Настройки &gt; Параметры</em>, подраздел
<em>Кэшировать пароли в памяти драйвера</em></a>.</p>
<h3>Параметры монтирования</h3>
<p>Параметры монтирования влияют на текущий монтируемый том. Чтобы открыть диалоговое окно <em>Параметры
монтирования</em>, нажмите кнопку <em>Параметры</em> в окне ввода пароля. Если в кэше находится правильный
пароль, то при нажатии кнопки <em>Смонтировать</em> тома будут монтироваться автоматически.
Если вам потребуется изменить параметры монтирования тома, который монтируется с использованием кэшированного
пароля, или избранного тома в меню <em>Избранное</em>, то при щелчке по кнопке <em>Смонтировать</em> удерживайте
нажатой клавишу <em>Control</em> (<em>Ctrl</em>), либо выберите команду <em>Смонтировать том с параметрами</em>
в меню <em>Тома</em>.<br>
<br>
Параметры монтирования, принимаемые по умолчанию, устанавливаются в основных настройках программы
(<em>Настройки &gt; Параметры).</em></p>
<h4>Монтировать как том только для чтения</h4>
<p>Если включено, смонтированный том будет недоступен для записи данных.</p>
<h4>Монтировать том как сменный носитель</h4>
<p>См. раздел <a href="Removable%20Medium%20Volume.html">
<em>Том, смонтированный как сменный носитель</em></a>.</p>
<h4>По возможности применять копию заголовка, встроенную в том</h4>
<p>Все тома, созданные с помощью VeraCrypt, содержат встроенную резервную копию заголовка (расположенную в конце тома).
Если вы включите эту опцию, программа попытается смонтировать том, используя встроенную резервную копию заголовка.
Обратите внимание, что если заголовок тома повреждён, применять данную опцию не нужно. Вместо этого можно восстановить
заголовок, выбрав <em>Сервис</em> &gt; <em>Восстановить заголовок тома</em>.</p>
<h4>Монтировать раздел с шифрованием ОС без предзагрузочной аутентификации</h4>
<p>Включите этот параметр, если нужно смонтировать раздел, входящий в область действия шифрования системы, без
предзагрузочной аутентификации. Пример: вы хотите смонтировать раздел, расположенный на зашифрованном системном
диске с другой ОС, которая сейчас не запущена. Это может понадобиться, скажем, когда требуется создать резервную
копию или восстановить операционную систему, зашифрованную с помощью VeraCrypt (из другой операционной системы).
Обратите внимание, что эту опцию также можно включить при использовании функций <em>Автомонтирование</em> и
<em>Автомонтирование всех томов на основе устройств</em>.</p>
<h4>Защита скрытых томов</h4>
<p>См. раздел <a href="Protection%20of%20Hidden%20Volumes.html">
<em>Защита скрытых томов от повреждений</em></a>.</p>
</div>
</div><div class="ClearBoth"></div></body></html>
