﻿<!DOCTYPE html>
<html lang="ru">
<head>
<meta http-equiv="content-type" content="text/html; charset=utf-8" />
<title>VeraCrypt - Бесплатное надёжное шифрование дисков с открытым исходным кодом</title>
<meta name="description" content="VeraCrypt это бесплатное программное обеспечение для шифрования дисков с открытым исходным кодом для Windows, Mac OS X (macOS) и Linux. В случае, если злоумышленник вынуждает вас раскрыть пароль, VeraCrypt обеспечивает правдоподобное отрицание наличия шифрования. В отличие от пофайлового шифрования, VeraCrypt шифрует данные в реальном времени (на лету), автоматически, прозрачно, требует очень мало памяти и не использует временные незашифрованные файлы."/>
<meta name="keywords" content="encryption, security, шифрование, безопасность"/>
<link href="styles.css" rel="stylesheet" type="text/css" />
</head>
<body>

<div>
<a href="Documentation.html"><img src="VeraCrypt128x128.png" alt="VeraCrypt"/></a>
</div>

<div id="menu">
	<ul>
	  <li><a href="Home.html">Начало</a></li>
	  <li><a href="Code.html">Исходный код</a></li>
	  <li><a href="Downloads.html">Загрузить</a></li>
	  <li><a class="active" href="Documentation.html">Документация</a></li>
	  <li><a href="Donation.html">Поддержать разработку</a></li>
	  <li><a href="https://sourceforge.net/p/veracrypt/discussion/" target="_blank">Форум</a></li>
	</ul>
</div>

<div>
<p>
<a href="Documentation.html">Документация</a>
<img src="arrow_right.gif" alt=">>" style="margin-top: 5px">
<a href="Portable%20Mode.html">Портативный (переносной) режим</a>
</p></div>

<div class="wikidoc">
<h1>Портативный (переносной) режим</h1>
<p>Программа VeraCrypt может работать в так называемом портативном (portable) режиме. Это означает, что её
не нужно устанавливать в операционной системе, в которой она запускается. Тем не менее, при этом нужно помнить о паре вещей:</p>
<ol>
<li>Чтобы запустить VeraCrypt в портативном режиме, необходимо иметь права администратора компьютера (причины этого см. в главе
<a href="Using%20VeraCrypt%20Without%20Administrator%20Privileges.html">
<em>Использование VeraCrypt без прав администратора</em></a>).
<table border="2">
<tbody>
<tr>
<td style="text-align:left; font-size:11px; line-height:13px; font-family:Verdana,Arial,Helvetica,sans-serif; color:#ff0000; padding:15px; border:1px solid #000000">
Примечание. Независимо от того, какое ПО вы используете, если говорить о защите личных данных, в большинстве случаев
<em>небезопасно</em> работать с конфиденциальной информацией в системе, где у вас нет привилегий администратора, так как
администратор может без труда получить и скопировать ваши конфиденциальные данные, в том числе пароли и ключи.</td>
</tr>
</tbody>
</table>
</li><li>Даже если использовался портативный режим, исследовав файл реестра, можно выяснить, что в Windows запускался
VeraCrypt (и что монтировался том VeraCrypt).
</li></ol>
<p><strong>Примечание</strong>. Если для вас это проблема, см. <a href="FAQ.html#notraces" target="_blank.html">
данный вопрос</a> в FAQ.<br>
<br>
Запускать VeraCrypt в портативном режиме можно двумя способами:</p>
<ol>
<li>Извлечь файлы из самораспаковывающегося дистрибутивного пакета VeraCrypt и запустить файл
<em>VeraCrypt.exe</em>.<br>
Чтобы извлечь файлы из самораспаковывающегося дистрибутивного пакета VeraCrypt, запустите его и выберите на второй странице
мастера установки опцию <em>Извлечь</em> (вместо <em>Установить</em>).<br>
</li><li>Воспользоваться средством настройки <em>Переносного диска</em>, чтобы подготовить специальный
носимый с собой диск и запускать VeraCrypt с него.
</li></ol>
<p>Второй вариант имеет ряд преимуществ, описанных ниже в этой главе.</p>
<p>Примечание. При работе в переносном (portable) режиме драйвер VeraCrypt выгружается, когда он больше не нужен (например,
когда закрыты все копии главного приложения и/или мастера создания томов и нет смонтированных томов). Однако если
вы принудительно размонтируете том VeraCrypt, когда VeraCrypt запущен в переносном режиме, или смонтируете доступный
для записи том с файловой системой NTFS в Vista или более новой версии Windows, драйвер VeraCrypt может
<em>не</em> выгрузиться при выходе из VeraCrypt (он будет выгружен только при завершении работы системы или её
перезагрузке). Это предотвращает различные проблемы, вызванные ошибкой в ​​Windows (например, было бы невозможно снова
запустить VeraCrypt, пока есть приложения, использующие размонтированный том).</p>
<h3>Сервис &gt; Настройка Переносного диска</h3>
<p>Эта функция позволяет подготовить специальный носимый с собой диск и запускать оттуда VeraCrypt. Обратите внимание,
что такой "переносной диск" это <em>не</em> том VeraCrypt, а <em>незашифрованный</em> том. "Переносной диск" исполняемые
файлы VeraCrypt и, при необходимости, скрипт <i>autorun.inf</i> (см. ниже раздел
<em>Настройка автозапуска (файл autorun.inf)</em>). При выборе <em>Сервис &gt; Настройка Переносного диска</em>
появится окно <em>Настройка Переносного диска</em>. Далее описаны некоторые параметры в этом окне, нуждающиеся в пояснении.</p>
<h4>С мастером создания томов VeraCrypt</h4>
<p>Включите эту опцию, если вам нужно создавать новые тома VeraCrypt с помощью VeraCrypt, запускаемого с этого
Переносного диска. Отключение этой опции экономит место на Переносном диске.</p>
<h4>Настройка автозапуска (файл autorun.inf)</h4>
<p>Данная группа параметров позволяет настроить Переносной диск для автоматического запуска VeraCrypt или монтирования
указанного тома VeraCrypt при вставке диска. Это достигается путём создания на Переносном диске специального файла
сценария (скрипта) с именем &lsquo;<em>autorun.inf</em>&rsquo;. Этот файл автоматически запускается операционной
системой каждый раз, когда вставляется Переносной диск.<br>
<br>
Обратите, однако, внимание, что данная функция работает лишь с такими сменными носителями, как диски CD/DVD (для работы
с USB-флешками требуется Windows XP SP2, Vista или более новая версия Windows), и только если это разрешено в
операционной системе. В зависимости от конфигурации операционной системы эти функции автозапуска и автомонтирования
могут работать только в том случае, если файлы Переносного диска созданы на недоступном для записи CD/DVD-подобном
носителе (это не ошибка в VeraCrypt, а ограничение Windows).<br>
<br>
Также примите к сведению, что файл <em>autorun.inf</em> должен находиться в корневой папке (то есть, например, в
<em>G:\</em>, <em>X:\</em>, <em>Y:\</em> и т. д.) на <strong>незашифрованном</strong> диске, иначе эта функция
не будет работать.</p>
</div><div class="ClearBoth"></div></body></html>
