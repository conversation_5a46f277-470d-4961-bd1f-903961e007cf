<!DOCTYPE html>
<html lang="en">
<head>
<meta http-equiv="content-type" content="text/html; charset=utf-8" />
<title>VeraCrypt - Free Open source disk encryption with strong security for the Paranoid</title>
<meta name="description" content="VeraCrypt is free open-source disk encryption software for Windows, Mac OS X and Linux. In case an attacker forces you to reveal the password, VeraCrypt provides plausible deniability. In contrast to file encryption, data encryption performed by VeraCrypt is real-time (on-the-fly), automatic, transparent, needs very little memory, and does not involve temporary unencrypted files."/>
<meta name="keywords" content="encryption, security"/>
<link href="styles.css" rel="stylesheet" type="text/css" />
</head>
<body>

<div>
<a href="Documentation.html"><img src="VeraCrypt128x128.png" alt="VeraCrypt"/></a>
</div>

<div id="menu">
	<ul>
	  <li><a href="Home.html">Home</a></li>
	  <li><a href="Code.html">Source Code</a></li>
	  <li><a href="Downloads.html">Downloads</a></li>
	  <li><a class="active" href="Documentation.html">Documentation</a></li>
	  <li><a href="Donation.html">Donate</a></li>
	  <li><a href="https://sourceforge.net/p/veracrypt/discussion/" target="_blank">Forums</a></li>
	</ul>
</div>

<div>
<p>
<a href="Documentation.html">Documentation</a>
<img src="arrow_right.gif" alt=">>" style="margin-top: 5px">
<a href="System%20Encryption.html">System Encryption</a>
<img src="arrow_right.gif" alt=">>" style="margin-top: 5px">
<a href="VeraCrypt%20Rescue%20Disk.html">VeraCrypt Rescue Disk</a>
</p></div>

<div class="wikidoc">
<h1>VeraCrypt Rescue Disk</h1>
<div style="text-align:left; margin-top:19px; margin-bottom:19px; padding-top:0px; padding-bottom:0px">
During the process of preparing the encryption of a system partition/drive, VeraCrypt requires that you create a so-called VeraCrypt Rescue Disk (USB disk in EFI boot mode, CD/DVD in MBR legacy boot mode), which serves the following purposes:</div>
<ul style="text-align:left; margin-top:18px; margin-bottom:19px; padding-top:0px; padding-bottom:0px">
<li style="text-align:left; margin-top:0px; margin-bottom:0px; padding-top:0px; padding-bottom:0px">
If the VeraCrypt Boot Loader screen does not appear after you start your computer (or if Windows does not boot), the
<strong style="text-align:left">VeraCrypt Boot Loader may be damaged</strong>. The VeraCrypt Rescue Disk allows you restore it and thus to regain access to your encrypted system and data (however, note that you will still have to enter the correct password
 then). For EFI boot mode, select <em style="text-align:left">Restore VeraCrypt loader binaries to system disk</em> in the Rescue Disk screen. For MBR legacy boot mode, select instead <em style="text-align:left">Repair Options</em> &gt;
<em style="text-align:left">Restore VeraCrypt Boot Loader</em>. Then press 'Y' to confirm the action, remove the Rescue Disk from your USB port or CD/DVD drive and restart your computer.
</li><li style="text-align:left; margin-top:0px; margin-bottom:0px; padding-top:0px; padding-bottom:0px">
If the <strong style="text-align:left">VeraCrypt Boot Loader is frequently damaged
</strong>(for example, by inappropriately designed activation software) or if <strong style="text-align:left">
you do not want the VeraCrypt boot loader </strong><strong style="text-align:left">to reside on the hard drive
</strong>(for example, if you want to use an alternative boot loader/manager for other operating systems), you can boot directly from the VeraCrypt Rescue Disk (as it contains the VeraCrypt boot loader too) without restoring the boot loader to the hard drive.
 For EFI boot mode, just insert your Rescue Disk into a USB port, boot your computer on it and then select <em style="text-align:left">Boot VeraCrypt loader from rescue disk</em> on the Rescue Disk screen. For MBR legacy boot mode, you need to insert the Rescue Disk in your CD/DVD drive and then enter your password in the Rescue Disk screen.
</li><li style="text-align:left; margin-top:0px; margin-bottom:0px; padding-top:0px; padding-bottom:0px">
If you repeatedly enter the correct password but VeraCrypt says that the password is incorrect, it is possible that the
<strong style="text-align:left">master key or other critical data are damaged</strong>. The VeraCrypt Rescue Disk allows you to restore them and thus to regain access to your encrypted system and data (however, note that you will still have to enter the correct
 password then). For EFI boot mode, select <em style="text-align:left">Restore OS header keys</em> in the Rescue Disk screen. For MBR legacy boot mode, select instead <em style="text-align:left">Repair Options</em> &gt;
<em style="text-align:left">Restore VeraCrypt Boot Loader</em>. Then enter your password, press 'Y' to confirm the action, remove the Rescue Disk from the USB port or CD/DVD drive, and restart your computer.<br style="text-align:left">
<br style="text-align:left">
Note: This feature cannot be used to restore the header of a hidden volume within which a
<a href="Hidden%20Operating%20System.html" style="text-align:left; color:#0080c0; text-decoration:none">
hidden operating system</a> resides (see the section <a href="Hidden%20Operating%20System.html">
Hidden Operating System</a>). To restore such a volume header, click <em style="text-align:left">
Select Device</em>, select the partition behind the decoy system partition, click
<em style="text-align:left">OK</em>, select <em style="text-align:left">Tools</em> &gt;
<em style="text-align:left">Restore Volume Header</em> and then follow the instructions.<br style="text-align:left">
<br style="text-align:left">
WARNING: By restoring key data using a VeraCrypt Rescue Disk, you also restore the password that was valid when the VeraCrypt Rescue Disk was created. Therefore, whenever you change the password, you should destroy your VeraCrypt Rescue Disk and create a new
 one (select <em style="text-align:left">System</em> -&gt; <em style="text-align:left">
Create Rescue Disk</em>). Otherwise, if an attacker knows your old password (for example, captured by a keystroke logger) and if he then finds your old VeraCrypt Rescue Disk, he could use it to restore the key data (the master key encrypted with the old password)
 and thus decrypt your system partition/drive </li><li id="WindowsDamaged" style="text-align:left; margin-top:0px; margin-bottom:0px; padding-top:0px; padding-bottom:0px">
If <strong style="text-align:left">Windows is damaged and cannot start</strong> after typing the correct password on VeraCrypt password prompt, the VeraCrypt Rescue Disk allows you to permanently decrypt the partition/drive before Windows starts. For EFI boot, select
<em style="text-align:left">Decrypt OS</em> in the Rescue Disk screen. For MBR legacy boot mode, select instead <em style="text-align:left">Repair Options</em> &gt; <em style="text-align:left">
Permanently decrypt system partition/drive</em>. Enter the correct password and wait until decryption is complete. Then you can e.g. boot your MS Windows setup CD/DVD to repair your Windows installation. Note that this feature cannot be used to decrypt a hidden
 volume within which a <a href="Hidden%20Operating%20System.html" style="text-align:left; color:#0080c0; text-decoration:none">
hidden operating system</a> resides (see the section <a href="Hidden%20Operating%20System.html">
Hidden Operating System</a>).<br style="text-align:left">
<br style="text-align:left">
Note: Alternatively, if Windows is damaged (cannot start) and you need to repair it (or access files on it), you can avoid decrypting the system partition/drive by following these steps:&nbsp;If you have multiple operating systems installed on your computer,
 boot the one that does not require pre-boot authentication. If you do not have multiple operating systems installed on your computer, you can boot a WinPE or BartPE CD/DVD or a Linux Live CD/DVD/USB. You can also connect your system drive as a secondary or external drive to another computer
 and then boot the operating system installed on the computer. After you boot a system, run VeraCrypt, click Select Device, select the affected system partition, click OK , select System &gt; Mount Without Pre-Boot Authentication, enter your pre-boot-authentication
 password and click OK. The partition will be mounted as a regular VeraCrypt volume (data will be on-the-fly decrypted/encrypted in RAM on access, as usual).
</li><li style="text-align:left; margin-top:0px; margin-bottom:0px; padding-top:0px; padding-bottom:0px">
In case of MBR legacy boot mode, your VeraCrypt Rescue Disk contains a <strong style="text-align:left">backup of the original content of the first drive track</strong> (made before the VeraCrypt Boot Loader was written to it) and allows you to restore it if necessary. The first track typically
 contains a system loader or boot manager. In the Rescue Disk screen, select Repair Options &gt; Restore original system loader.
</li></ul>
<div style="text-align:left; margin-top:19px; margin-bottom:19px; padding-top:0px; padding-bottom:0px">
&nbsp;</div>
<div style="text-align:left; margin-top:19px; margin-bottom:19px; padding-top:0px; padding-bottom:0px">
<em style="text-align:left">Note that even if you lose your VeraCrypt Rescue Disk and an attacker finds it, he or she will
<strong style="text-align:left">not</strong> be able to decrypt the system partition or drive without the correct password.</em></div>
<div style="text-align:left; margin-top:19px; margin-bottom:19px; padding-top:0px; padding-bottom:0px">
To boot a VeraCrypt Rescue Disk, insert it into a USB port or your CD/DVD drive depending on its type and restart your computer. If the VeraCrypt Rescue Disk screen does not appear (or in case of MBR legacy boot mode if you do not see the 'Repair Options' item in the 'Keyboard Controls' section of the screen), it is possible
 that your BIOS is configured to attempt to boot from hard drives before USB drivers and CD/DVD drives. If that is the case, restart your computer, press F2 or Delete (as soon as you see a BIOS start-up screen), and wait until a BIOS configuration screen appears. If no BIOS
 configuration screen appears, restart (reset) the computer again and start pressing F2 or Delete repeatedly as soon as you restart (reset) the computer. When a BIOS configuration screen appears, configure your BIOS to boot from the USB drive and CD/DVD drive first (for
 information on how to do so, please refer to the documentation for your BIOS/motherboard or contact your computer vendor's technical support team for assistance). Then restart your computer. The VeraCrypt Rescue Disk screen should appear now. Note: In the
 case of MBR legacy boot mode, you can select 'Repair Options' on the VeraCrypt Rescue Disk screen by pressing F8 on your keyboard.</div>
<p>If your VeraCrypt Rescue Disk is damaged, you can create a new one by selecting
<em style="text-align:left">System</em> &gt; <em style="text-align:left">Create Rescue Disk</em>. To find out whether your VeraCrypt Rescue Disk is damaged, insert it into a USB port (or into your CD/DVD drive in case of MBR legacy boot mode) and select
<em style="text-align:left">System</em> &gt; <em style="text-align:left">Verify Rescue Disk</em>.</p>
</div><div class="ClearBoth"></div>


<h2>VeraCrypt Rescue Disk for MBR legacy boot mode on USB Stick</h2>
<div style="text-align:left; margin-top:19px; margin-bottom:19px; padding-top:0px; padding-bottom:0px">
It is also possible to create a VeraCrypt Rescue Disk for MBR legacy boot mode on a USB drive, in case your machine does not have a CD/DVD drive. <strong style="text-align:left">Please note that you must ensure that the data on the USB stick is not overwritten! If you lose the USB drive or your data is damaged, you will not be able to recover your system in case of a problem!</strong>
</div>
<div style="text-align:left; margin-top:19px; margin-bottom:19px; padding-top:0px; padding-bottom:0px">
To create a bootable VeraCrypt Rescue USB drive you have to create a bootable USB drive which bootloader runs up the iso image. Solutions like Unetbootin, which try to copy the data inside the iso image to the usb drive do not work yet. On Windows please follow the steps below:
</div>
<ul style="text-align:left; margin-top:18px; margin-bottom:19px; padding-top:0px; padding-bottom:0px">
	<li style="text-align:left; margin-top:0px; margin-bottom:0px; padding-top:0px; padding-bottom:0px">
		Download the required files from the official SourceForge repository of VeraCrypt: <a href="https://sourceforge.net/projects/veracrypt/files/Contributions/VeraCryptUsbRescueDisk.zip" style="text-align:left; 		color:#0080c0; text-decoration:none">
		https://sourceforge.net/projects/veracrypt/files/Contributions/VeraCryptUsbRescueDisk.zip</a>
	</li>

	<li style="text-align:left; margin-top:0px; margin-bottom:0px; padding-top:0px; padding-bottom:0px">
		Insert a USB drive.
	</li>
	<li style="text-align:left; margin-top:0px; margin-bottom:0px; padding-top:0px; padding-bottom:0px">
		Format the USB drive with FAT16 oder FAT32:
		<ul style="text-align:left; margin-top:6px; margin-bottom:6px; padding-top:0px; padding-bottom:0px">
			<li style="text-align:left; margin-top:0px; margin-bottom:0px; padding-top:0px; padding-bottom:0px">
				Launch usb_format.exe as an administrator (right click "Run as Administrator").
			</li>
			<li style="text-align:left; margin-top:0px; margin-bottom:0px; padding-top:0px; padding-bottom:0px">
				Select your USB drive in the Device list.
			</li>
			<li style="text-align:left; margin-top:0px; margin-bottom:0px; padding-top:0px; padding-bottom:0px">
				Choose FAT as filesystem and check "Quick Format". Click Start.
			</li>
		</ul>

	</li>
	<li style="text-align:left; margin-top:0px; margin-bottom:0px; padding-top:0px; padding-bottom:0px">
		Create a bootloader which can start up an iso image:
		<ul style="text-align:left; margin-top:6px; margin-bottom:6px; padding-top:0px; padding-bottom:0px">
			<li style="text-align:left; margin-top:0px; margin-bottom:0px; padding-top:0px; padding-bottom:0px">
				Launch grubinst_gui.exe.
			</li>
			<li style="text-align:left; margin-top:0px; margin-bottom:0px; padding-top:0px; padding-bottom:0px">
				Check "Disk" and then select your USB drive in the list.
			</li>
			<li style="text-align:left; margin-top:0px; margin-bottom:0px; padding-top:0px; padding-bottom:0px">
				Click the "Refresh" button in front of "Part List" and then choose "Whole disk (MBR)".
			</li>
			<li style="text-align:left; margin-top:0px; margin-bottom:0px; padding-top:0px; padding-bottom:0px">
				Leave all other options unchanged and then click "Install".
			</li>
			<li style="text-align:left; margin-top:0px; margin-bottom:0px; padding-top:0px; padding-bottom:0px">
				You should see an console window that reads "The MBR/BS has been successfully installed. Press &ltENTER&gt; to continue ..."
			</li>
			<li style="text-align:left; margin-top:0px; margin-bottom:0px; padding-top:0px; padding-bottom:0px">
				Close the tool.
			</li>
		</ul>
	</li>
	<li style="text-align:left; margin-top:0px; margin-bottom:0px; padding-top:0px; padding-bottom:0px">
		Copy the file "grldr" to your USB drive at the root (e.g. if the drive letter is I:, you should have I:\grldr). This file loads Grub4Dos.
	</li>
	<li style="text-align:left; margin-top:0px; margin-bottom:0px; padding-top:0px; padding-bottom:0px">
		Copy the file "menu.lst" to your USB drive at the root (e.g. if the drive letter is I:, you should have I:\menu.lst). This file configures the shown menu and its options.
	</li>
	<li style="text-align:left; margin-top:0px; margin-bottom:0px; padding-top:0px; padding-bottom:0px">
		Copy the rescue disk file "VeraCrypt Rescue Disk.iso" to the USB drive at the root and rename it "veracrypt.iso". Another possibility is to change the link in the "menu.lst" file.
	</li>

</ul>
</body></html>
