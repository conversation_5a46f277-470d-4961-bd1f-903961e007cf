<!DOCTYPE html>
<html lang="en">
<head>
<meta http-equiv="content-type" content="text/html; charset=utf-8" />
<title>VeraCrypt - Free Open source disk encryption with strong security for the Paranoid</title>
<meta name="description" content="VeraCrypt is free open-source disk encryption software for Windows, Mac OS X and Linux. In case an attacker forces you to reveal the password, VeraCrypt provides plausible deniability. In contrast to file encryption, data encryption performed by VeraCrypt is real-time (on-the-fly), automatic, transparent, needs very little memory, and does not involve temporary unencrypted files."/>
<meta name="keywords" content="encryption, security"/>
<link href="styles.css" rel="stylesheet" type="text/css" />
</head>
<body>

<div>
<a href="Documentation.html"><img src="VeraCrypt128x128.png" alt="VeraCrypt"/></a>
</div>

<div id="menu">
	<ul>
	  <li><a href="Home.html">Home</a></li>
	  <li><a href="Code.html">Source Code</a></li>
	  <li><a href="Downloads.html">Downloads</a></li>
	  <li><a class="active" href="Documentation.html">Documentation</a></li>
	  <li><a href="Donation.html">Donate</a></li>
	  <li><a href="https://sourceforge.net/p/veracrypt/discussion/" target="_blank">Forums</a></li>
	</ul>
</div>

<div>
<p>
<a href="Documentation.html">Documentation</a>
<img src="arrow_right.gif" alt=">>" style="margin-top: 5px">
<a href="Technical%20Details.html">Technical Details</a>
<img src="arrow_right.gif" alt=">>" style="margin-top: 5px">
<a href="Modes%20of%20Operation.html">Modes of Operation</a>
</p></div>

<div class="wikidoc">
<h1>Modes of Operation</h1>
<div style="text-align:left; margin-top:19px; margin-bottom:19px; padding-top:0px; padding-bottom:0px">
<br style="text-align:left">
The mode of operation used by VeraCrypt for encrypted partitions, drives, and virtual volumes is XTS.
<br style="text-align:left">
<br style="text-align:left">
XTS mode is in fact XEX mode <a href="http://www.cs.ucdavis.edu/%7Erogaway/papers/offsets.pdf">
[12]</a>, which was designed by Phillip Rogaway in 2003, with a minor modification (XEX mode uses a single key for two different purposes, whereas XTS mode uses two independent keys).<br style="text-align:left">
<br style="text-align:left">
In 2010, XTS mode was approved by NIST for protecting the confidentiality of data on storage devices [24]. In 2007, it was also approved by the IEEE for cryptographic protection of data on block-oriented storage devices (IEEE 1619).</div>
<div style="text-align:left; margin-top:19px; margin-bottom:19px; padding-top:0px; padding-bottom:0px">
&nbsp;</div>
<h2 style="text-align:left; margin-top:19px; margin-bottom:19px; padding-top:0px; padding-bottom:0px">
<strong style="text-align:left">Description of XTS mode</strong>:</h2>
<div style="text-align:left; margin-top:19px; margin-bottom:19px; padding-top:0px; padding-bottom:0px">
<em style="text-align:left">C<sub style="text-align:left; font-size:85%">i</sub></em> =
<em style="text-align:left">E</em><sub style="text-align:left; font-size:85%"><em style="text-align:left">K</em>1</sub>(<em style="text-align:left">P<sub style="text-align:left; font-size:85%">i</sub></em> ^ (<em style="text-align:left">E</em><sub style="text-align:left; font-size:85%"><em style="text-align:left">K</em>2</sub>(<em style="text-align:left">n</em>)
<img src="gf2_mul.gif" alt="" width="10" height="10">
<em style="text-align:left">a<sup style="text-align:left; font-size:85%">i</sup></em>)) ^ (<em style="text-align:left">E</em><sub style="text-align:left; font-size:85%"><em style="text-align:left">K</em>2</sub>(<em style="text-align:left">n</em>)
<img src="gf2_mul.gif" alt="" width="10" height="10"><em style="text-align:left"> a<sup style="text-align:left; font-size:85%">i</sup></em>)</div>
<div style="text-align:left; margin-top:19px; margin-bottom:19px; padding-top:0px; padding-bottom:0px">
Where:</div>
<table style="border-collapse:separate; border-spacing:0px; width:608px; text-align:left; font-size:11px; line-height:13px; font-family:Verdana,Arial,Helvetica,sans-serif; border:0px outset #999">
<tbody style="text-align:left">
<tr style="text-align:left">
<td style="vertical-align:top; color:#000000; text-align:left; font-size:11px; line-height:13px; font-family:Verdana,Arial,Helvetica,sans-serif; padding:0px">
&nbsp;<sup style="text-align:left; font-size:85%">&nbsp;<img src="gf2_mul.gif" alt="" width="10" height="10"></sup></td>
<td style="vertical-align:top; color:#000000; text-align:left; font-size:11px; line-height:13px; font-family:Verdana,Arial,Helvetica,sans-serif; padding:0px">
denotes multiplication of two polynomials over the binary field GF(2) modulo <em style="text-align:left">
x</em><sup style="text-align:left; font-size:85%">128</sup>&#43;<em style="text-align:left">x</em><sup style="text-align:left; font-size:85%">7</sup>&#43;<em style="text-align:left">x</em><sup style="text-align:left; font-size:85%">2</sup>&#43;<em style="text-align:left">x</em>&#43;1</td>
</tr>
<tr style="text-align:left">
<td style="width:30px; vertical-align:top; color:#000000; text-align:left; font-size:11px; line-height:13px; font-family:Verdana,Arial,Helvetica,sans-serif; padding:0px">
<br style="text-align:left">
<em style="text-align:left">K</em>1</td>
<td style="width:578px; vertical-align:top; color:#000000; text-align:left; font-size:11px; line-height:13px; font-family:Verdana,Arial,Helvetica,sans-serif; padding:0px">
<br style="text-align:left">
is the encryption key (256-bit for each supported cipher; i.e, AES, Serpent, and Twofish)</td>
</tr>
<tr style="text-align:left">
<td style="vertical-align:top; color:#000000; text-align:left; font-size:11px; line-height:13px; font-family:Verdana,Arial,Helvetica,sans-serif; padding:0px">
<br style="text-align:left">
<em style="text-align:left">K</em>2</td>
<td style="vertical-align:top; color:#000000; text-align:left; font-size:11px; line-height:13px; font-family:Verdana,Arial,Helvetica,sans-serif; padding:0px">
<br style="text-align:left">
is the secondary key (256-bit for each supported cipher; i.e, AES, Serpent, and Twofish)</td>
</tr>
<tr style="text-align:left">
<td style="vertical-align:top; color:#000000; text-align:left; font-size:11px; line-height:13px; font-family:Verdana,Arial,Helvetica,sans-serif; padding:0px">
<br style="text-align:left">
<em style="text-align:left">i</em></td>
<td style="vertical-align:top; color:#000000; text-align:left; font-size:11px; line-height:13px; font-family:Verdana,Arial,Helvetica,sans-serif; padding:0px">
<br style="text-align:left">
is the cipher block index within a data unit; &nbsp; for the first cipher block within a data unit,
<em style="text-align:left">i</em> = 0</td>
</tr>
<tr style="text-align:left">
<td style="vertical-align:top; color:#000000; text-align:left; font-size:11px; line-height:13px; font-family:Verdana,Arial,Helvetica,sans-serif; padding:0px">
<br style="text-align:left">
<em style="text-align:left">n</em></td>
<td style="vertical-align:top; color:#000000; text-align:left; font-size:11px; line-height:13px; font-family:Verdana,Arial,Helvetica,sans-serif; padding:0px">
<br style="text-align:left">
is the data unit index within the scope of <em style="text-align:left">K</em>1; &nbsp; for the first data unit,
<em style="text-align:left">n</em> = 0</td>
</tr>
<tr style="text-align:left">
<td style="vertical-align:top; color:#000000; text-align:left; font-size:11px; line-height:13px; font-family:Verdana,Arial,Helvetica,sans-serif; padding:0px">
<br style="text-align:left">
<em style="text-align:left">a</em></td>
<td style="vertical-align:top; color:#000000; text-align:left; font-size:11px; line-height:13px; font-family:Verdana,Arial,Helvetica,sans-serif; padding:0px">
<br style="text-align:left">
is a primitive element of Galois Field (2<sup style="text-align:left; font-size:85%">128</sup>) that corresponds to polynomial
<em style="text-align:left">x</em> (i.e., 2)</td>
</tr>
<tr style="text-align:left">
<td colspan="2" style="vertical-align:top; color:#000000; text-align:left; font-size:11px; line-height:13px; font-family:Verdana,Arial,Helvetica,sans-serif; padding:0px">
<br style="text-align:left">
<span style="text-align:left; font-size:10px; line-height:12px">Note: The remaining symbols are defined in the section
<a href="Notation.html" style="text-align:left; color:#0080c0; text-decoration:none">
Notation</a>. </span></td>
</tr>
</tbody>
</table>
<div style="text-align:left; margin-top:19px; margin-bottom:19px; padding-top:0px; padding-bottom:0px">
<br style="text-align:left">
The size of each data unit is always 512 bytes (regardless of the sector size).</div>
<div style="text-align:left; margin-top:19px; margin-bottom:19px; padding-top:0px; padding-bottom:0px">
For further information pertaining to XTS mode, see e.g. <a href="http://www.cs.ucdavis.edu/%7Erogaway/papers/offsets.pdf" style="text-align:left; color:#0080c0; text-decoration:none">
[12]</a> and <a href="http://csrc.nist.gov/publications/nistpubs/800-38E/nist-sp-800-38E.pdf" style="text-align:left; color:#0080c0; text-decoration:none">
[24]</a>.</div>
<div style="text-align:left; margin-top:19px; margin-bottom:19px; padding-top:0px; padding-bottom:0px">
<a href="Header%20Key%20Derivation.html" style="text-align:left; color:#0080c0; text-decoration:none; font-weight:bold">Next Section &gt;&gt;</a></div>
</div><div class="ClearBoth"></div></body></html>
