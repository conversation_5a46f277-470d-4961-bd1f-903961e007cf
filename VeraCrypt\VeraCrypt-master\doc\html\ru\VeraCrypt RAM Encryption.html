﻿<!DOCTYPE html>
<html lang="ru">
<head>
<meta http-equiv="content-type" content="text/html; charset=utf-8" />
<title>VeraCrypt - Бесплатное надёжное шифрование дисков с открытым исходным кодом</title>
<meta name="description" content="VeraCrypt это бесплатное программное обеспечение для шифрования дисков с открытым исходным кодом для Windows, Mac OS X (macOS) и Linux. В случае, если злоумышленник вынуждает вас раскрыть пароль, VeraCrypt обеспечивает правдоподобное отрицание наличия шифрования. В отличие от пофайлового шифрования, VeraCrypt шифрует данные в реальном времени (на лету), автоматически, прозрачно, требует очень мало памяти и не использует временные незашифрованные файлы."/>
<meta name="keywords" content="encryption, security, шифрование, безопасность"/>
<link href="styles.css" rel="stylesheet" type="text/css" />
</head>
<body>

<div>
<a href="Documentation.html"><img src="VeraCrypt128x128.png" alt="VeraCrypt"/></a>
</div>

<div id="menu">
	<ul>
	  <li><a href="Home.html">Начало</a></li>
	  <li><a href="Code.html">Исходный код</a></li>
	  <li><a href="Downloads.html">Загрузить</a></li>
	  <li><a class="active" href="Documentation.html">Документация</a></li>
	  <li><a href="Donation.html">Поддержать разработку</a></li>
	  <li><a href="https://sourceforge.net/p/veracrypt/discussion/" target="_blank">Форум</a></li>
	</ul>
</div>

<div>
<p>
<a href="Documentation.html">Документация</a>
<img src="arrow_right.gif" alt=">>" style="margin-top: 5px">
<a href="Security%20Requirements%20and%20Precautions.html">Требования безопасности и меры предосторожности</a>
<img src="arrow_right.gif" alt=">>" style="margin-top: 5px">
<a href="VeraCrypt%20RAM%20Encryption.html">Шифрование оперативной памяти в VeraCrypt</a>
</p></div>

<div class="wikidoc">
<h1>Шифрование оперативной памяти в VeraCrypt</h1>

<h2>Введение</h2>

<p>
    Шифрование ОЗУ в VeraCrypt направлено на защиту ключей шифрования диска, хранящихся в энергонезависимой памяти, от определённых типов атак. Основные цели шифрования:
    </p><ul>
        <li>Защита от атак при "холодной" загрузке компьютера (Cold Boot).</li>
        <li>Добавление уровня обфускации (запутывания кода), чтобы значительно усложнить восстановление мастер-ключей шифрования из дампов памяти, будь то в режиме реального времени или в автономном режиме.</li>
    </ul>
<p></p>

<h3>Реализация</h3>

<p>Краткое описание того, как достигается шифрование оперативной памяти:</p>
<ol>
    <li>При запуске Windows драйвер VeraCrypt выделяет область памяти размером 1 МиБ. Если это не удаётся, размер уменьшается вдвое до тех пор, пока распределение не завершится успешно (минимальный размер – 8 КиБ). Все эти переменные размещаются в невыгружаемом пространстве памяти ядра.</li>
    <li>Затем эта область памяти заполняется случайными байтами, сгенерированными CSPRNG на основе ChaCha20.</li>
    <li>Генерируются два случайных 64-битных целых числа: <code>HashSeedMask</code> и <code>CipherIVMask</code>.</li>
    <li>Для каждого мастер-ключа тома алгоритм шифрования ОЗУ получает уникальный ключ из комбинации области памяти и уникальных значений, извлечённых из памяти, подлежащей шифрованию. Это обеспечивает отдельный ключ для каждой зашифрованной области памяти. Использование ключей и IV, зависящих от местоположения, предотвращает лёгкое извлечение мастер-ключей из дампов памяти.</li>
    <li>Мастер-ключи расшифровываются для каждого запроса, что требует быстрого алгоритма расшифровки. Для этого используется ChaCha12.</li>
    <li>После монтирования тома его мастер-ключи немедленно шифруются по описанному алгоритму.</li>
    <li>Для каждого запроса ввода-вывода для тома мастер-ключи расшифровываются только на время этого запроса, а затем надёжно удаляются.</li>
    <li>При размонтировании тома зашифрованные мастер-ключи надёжно удаляются из памяти.</li>
    <li>При завершении работы или перезагрузке Windows область памяти, выделенная во время запуска, надёжно очищается.</li>
</ol>

<h3>Защита от атак при "холодной" перезагрузке ПК (Cold Boot)</h3>

<p>
    Предотвращение атак при "холодной" загрузке достигается благодаря использованию большой страницы памяти для формирования (деривации) ключа. Это гарантирует, что злоумышленники не смогут восстановить мастер-ключ, поскольку части этой большой области памяти, скорее всего, будут повреждены и их нельзя будет восстановить после завершения работы. Более подробную информацию об атаках с "холодной" загрузкой и методах их предотвращения см. в следующих документах:
</p>
<ul>
    <li><a href="https://www.blackhat.com/presentations/bh-usa-08/McGregor/BH_US_08_McGregor_Cold_Boot_Attacks.pdf" target="_blank">Cold Boot Attacks (BlackHat)</a></li>
    <li><a href="https://www.grc.com/sn/files/RAM_Hijacks.pdf" target="_blank">RAM Hijacks</a></li>
</ul>

<h3>Несовместимость с режимом гибернации и быстрым запуском Windows</h3>
<p>
	Шифрование оперативной памяти в VeraCrypt несовместимо с функциями гибернации и быстрого запуска Windows. Перед активацией шифрования ОЗУ эти функции будут отключены VeraCrypt, чтобы обеспечить безопасность и функциональность механизма шифрования.

</p>

<h3>Выбор алгоритма</h3>

<p>
    Выбор алгоритмов основывался на балансе между безопасностью и производительностью:
</p>
<ul>
    <li><strong>t1ha2:</strong> Некриптографическая хеш-функция, выбранная из-за её чрезвычайно высокой скорости хеширования. Это очень важно, поскольку ключи извлекаются для каждого запроса шифрования/дешифрования из области памяти размером 1 МиБ. Также соблюдаются строгие лавинные критерии, что имеет решающее значение для данного варианта использования.</li>
    <li><strong>ChaCha12:</strong> Этот алгоритм выбран вместо ChaCha20 по соображениям производительности; он обеспечивает достаточную надёжность шифрования при сохранении высокой скорости шифрования/дешифрования.</li>
</ul>

<h3>Ключевые алгоритмы</h3>

<p>
    Для шифрования оперативной памяти основополагающими являются два основных алгоритма:
</p>

<h4>1. VcGetEncryptionID</h4>

<p>
    Вычисляет уникальный идентификатор для набора буферов оперативной памяти, подлежащих шифрованию.
</p>
<pre>    <code>
    – Ввод: pCryptoInfo, переменная CRYPTO_INFO для шифрования/дешифрования
    – Вывод: 64-битное целое число, идентифицирующее переменную pCryptoInfo
    – Шаги:
      – Вычисление суммы адресов виртуальной памяти полей ks и ks2 в pCryptoInfo: encID = ((uint64) pCryptoInfo-&gt;ks) + ((uint64) pCryptoInfo-&gt;ks2)
      – Возврат результата
    </code>
</pre>

<h4>2. VcProtectMemory</h4>

<p>
    Шифрует буфер оперативной памяти, используя уникальный идентификатор, сгенерированный VcGetEncryptionID.
</p>
<pre>    <code>
    – Ввод:
      – encID, уникальный идентификатор памяти, подлежащей шифрованию
      – pbData, указатель на память, которую нужно зашифровать
      – pbKeyDerivationArea, область памяти, выделяемая драйвером при запуске
      – HashSeedMask и CipherIVMask, два 64-битных случайных целых числа при запуске
    – Вывод:
      – Отсутствует; память в pbData шифруется на месте
    – Шаги:
      – Формирование hashSeed: hashSeed = (((uint64) pbKeyDerivationArea) + encID) ^ HashSeedMask
      – Вычисление 128-битного хеша: hash128 = t1h2 (pbKeyDerivationArea,hashSeed)
      – Разложение hash128 на два 64-битных целых числа: hash128 = hash128_1 || hash128_2
      – Создание 256-битного ключа для ChaCha12: chachaKey = hash128_1 || hash128_2 || (hash128_1 OR hash128_2) || (hash128_1 + hash128_2)
      – Шифрование chachaKey с помощью ChaCha12, используя hashSeed как IV: ChaCha256Encrypt (chachaKey, hashSeed, chachaKey)
      – Формирование 64-битного IV для ChaCha12: chachaIV = (((uint64) pbKeyDerivationArea) + encID) ^ CipherIVMask
      – Шифрование памяти в pbData с помощью ChaCha12: ChaCha256Encrypt (chachaKey, chachaIV, pbData)
      – Надёжное стирание временных значений
    </code>
</pre>

<p>
    Важно отметить, что поскольку ChaCha12 – это потоковый шифр, процессы шифрования и дешифрования идентичны, и функция <code>VcProtectMemory</code> может использоваться для обоих.
</p>

<p>
    Для более глубокого понимания и ознакомления с кодовой базой посетите репозиторий VeraCrypt и изучите упомянутые функции в файле <code>src/Common/Crypto.c</code>.
</p>

<h3>Дополнительные меры безопасности</h3>

<p>
    Начиная с версии 1.24, в VeraCrypt встроен механизм, который обнаруживает установку новых устройств в систему, когда активно системное шифрование. При установке нового устройства мастер-ключи немедленно удаляются из памяти, что приводит к BSOD ("синему экрану смерти") в Windows. Это защищает от атак с помощью специализированных устройств для извлечения памяти из работающих систем. Однако для максимальной эффективности эта функция должна применяться вместе с шифрованием оперативной памяти.<br>
	Чтобы включить эту функцию, в меню <em>Система</em> выберите пункт <em>Установки</em> и активируйте опцию <em>Удалять ключи шифрования из ОЗУ при подключении нового устройства</em>.
</p>

<h3>Технические ограничения, связанные с гибернацией и быстрым запуском</h3>
<p>
Функции гибернации и быстрого запуска Windows сохраняют содержимое оперативной памяти на жёстком диске. В контексте шифрования оперативной памяти VeraCrypt поддержка этих функций представляет собой серьёзную проблему.<br>
Для обеспечения безопасности большáя область памяти, используемая для формирования ключа при шифровании ОЗУ, должна храниться в зашифрованном формате, отдельно от обычного шифрования VeraCrypt, применяемого к текущему диску. Это отдельное зашифрованное хранилище также должно быть разблокируемым тем же паролем, который использовался для предзагрузочной аутентификации. Более того, этот процесс должен произойти на ранней стадии загрузки, прежде чем появится доступ к файловой системе, что требует хранения необработанных зашифрованных данных в определённых секторах другого диска.<br>
Хотя это технически осуществимо, сложность и недружественность такого решения к пользователям делают его непрактичным для стандартных применений. Поэтому при включении шифрования оперативной памяти отключаются гибернация и быстрый запуск Windows.<br>
</p>

</div><div class="ClearBoth"></div></body></html>
