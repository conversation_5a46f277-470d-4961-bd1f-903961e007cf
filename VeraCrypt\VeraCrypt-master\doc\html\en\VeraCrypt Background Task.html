<!DOCTYPE html>
<html lang="en">
<head>
<meta http-equiv="content-type" content="text/html; charset=utf-8" />
<title>VeraCrypt - Free Open source disk encryption with strong security for the Paranoid</title>
<meta name="description" content="VeraCrypt is free open-source disk encryption software for Windows, Mac OS X and Linux. In case an attacker forces you to reveal the password, VeraCrypt provides plausible deniability. In contrast to file encryption, data encryption performed by VeraCrypt is real-time (on-the-fly), automatic, transparent, needs very little memory, and does not involve temporary unencrypted files."/>
<meta name="keywords" content="encryption, security"/>
<link href="styles.css" rel="stylesheet" type="text/css" />
</head>
<body>

<div>
<a href="Documentation.html"><img src="VeraCrypt128x128.png" alt="VeraCrypt"/></a>
</div>

<div id="menu">
	<ul>
	  <li><a href="Home.html">Home</a></li>
	  <li><a href="Code.html">Source Code</a></li>
	  <li><a href="Downloads.html">Downloads</a></li>
	  <li><a class="active" href="Documentation.html">Documentation</a></li>
	  <li><a href="Donation.html">Donate</a></li>
	  <li><a href="https://sourceforge.net/p/veracrypt/discussion/" target="_blank">Forums</a></li>
	</ul>
</div>

<div>
<p>
<a href="Documentation.html">Documentation</a>
<img src="arrow_right.gif" alt=">>" style="margin-top: 5px">
<a href="Miscellaneous.html">Miscellaneous</a>
<img src="arrow_right.gif" alt=">>" style="margin-top: 5px">
<a href="VeraCrypt%20Background%20Task.html">Background Task</a>
</p></div>

<div class="wikidoc">
<div>
<h1>VeraCrypt Background Task</h1>
<p>When the main VeraCrypt window is closed, the VeraCrypt Background Task takes care of the following tasks/functions:</p>
<ol>
<li>Hot keys </li><li>Auto-unmount (e.g., upon logoff, inadvertent host device removal, time-out, etc.)
</li><li>Auto-mount of favorite volumes </li><li>Notifications (e.g., when damage to hidden volume is prevented) </li><li>Tray icon </li></ol>
<p>WARNING: If neither the VeraCrypt Background Task nor VeraCrypt is running, the above- mentioned tasks/functions are disabled.<br>
<br>
The VeraCrypt Background Task is actually the Vera<em>Crypt.exe</em> application, which continues running in the background after you close the main VeraCrypt window. Whether it is running or not can be determined by looking at the system tray area. If you
 can see the VeraCrypt icon there, then the VeraCrypt Background Task is running. You can click the icon to open the main VeraCrypt window. Right-click on the icon opens a popup menu with various VeraCrypt-related functions.<br>
<br>
You can shut down the Background Task at any time by right-clicking the VeraCrypt tray icon and selecting
<em>Exit</em>. If you need to disable the VeraCrypt Background Task completely and permanently, select
<em>Settings</em> -&gt; <em>Preferences</em> and uncheck the option <em>Enabled</em> in the Vera<em>Crypt Background Task</em> area of the
<em>Preferences</em> dialog window.</p>
</div>
</div><div class="ClearBoth"></div></body></html>
