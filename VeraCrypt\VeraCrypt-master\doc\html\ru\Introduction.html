﻿<!DOCTYPE html>
<html lang="ru">
<head>
<meta http-equiv="content-type" content="text/html; charset=utf-8" />
<title>VeraCrypt - Бесплатное надёжное шифрование дисков с открытым исходным кодом</title>
<meta name="description" content="VeraCrypt это бесплатное программное обеспечение для шифрования дисков с открытым исходным кодом для Windows, Mac OS X (macOS) и Linux. В случае, если злоумышленник вынуждает вас раскрыть пароль, VeraCrypt обеспечивает правдоподобное отрицание наличия шифрования. В отличие от пофайлового шифрования, VeraCrypt шифрует данные в реальном времени (на лету), автоматически, прозрачно, требует очень мало памяти и не использует временные незашифрованные файлы."/>
<meta name="keywords" content="encryption, security, шифрование, безопасность"/>
<link href="styles.css" rel="stylesheet" type="text/css" />
</head>
<body>

<div>
<a href="Documentation.html"><img src="VeraCrypt128x128.png" alt="VeraCrypt"/></a>
</div>

<div id="menu">
	<ul>
	  <li><a href="Home.html">Начало</a></li>
	  <li><a href="Code.html">Исходный код</a></li>
	  <li><a href="Downloads.html">Загрузить</a></li>
	  <li><a class="active" href="Documentation.html">Документация</a></li>
	  <li><a href="Donation.html">Поддержать разработку</a></li>
	  <li><a href="https://sourceforge.net/p/veracrypt/discussion/" target="_blank">Форум</a></li>
	</ul>
</div>

<div>
<p>
<a href="Documentation.html">Документация</a>
<img src="arrow_right.gif" alt=">>" style="margin-top: 5px">
<a href="Introduction.html">Введение</a>
</p>
</div>

<div class="wikidoc">
<h1>Введение</h1>
<div style="text-align:left; margin-top:19px; margin-bottom:19px; padding-top:0px; padding-bottom:0px">
VeraCrypt это программное обеспечение, предназначенное для создания томов (устройств хранения данных) и
работы с ними с использованием шифрования на лету (on-the-fly encryption). Шифрование на лету означает, что
данные автоматически зашифровываются непосредственно перед записью их на диск и расшифровываются сразу же
после их считывания, то есть без какого-либо вмешательства пользователя. Никакие данные, хранящиеся в
зашифрованном томе, невозможно прочитать (расшифровать) без правильного указания пароля/ключевых файлов или
правильных ключей шифрования. Полностью шифруется вся файловая система (имена файлов и папок, содержимое
каждого файла, свободное место, метаданные и др.).</div>
<div style="text-align:left; margin-top:19px; margin-bottom:19px; padding-top:0px; padding-bottom:0px">
Файлы можно копировать со смонтированного тома VeraCrypt и на него точно так же, как и при использовании
любого обычного диска (например, с помощью перетаскивания). При чтении или копировании из зашифрованного
тома VeraCrypt файлы автоматически на лету расшифровываются (в память/ОЗУ). Аналогично, файлы, записываемые
или копируемые в том VeraCrypt, автоматически на лету зашифровываются в ОЗУ (непосредственно перед их
сохранением на диск). Обратите внимание: это <i>не</i> означает, что перед шифрованием/дешифрованием в ОЗУ
должен находиться <i>весь</i> обрабатываемый файл. Никакой дополнительной памяти (ОЗУ) для VeraCrypt
не требуется. Пояснение, как всё это работает, приведено в следующем абзаце.<br style="text-align:left">
<br style="text-align:left">
Предположим, у нас есть видеофайл формата .avi, хранящийся в томе VeraCrypt (следовательно, этот видеофайл
полностью зашифрован). Пользователь указывает правильный пароль (и/или ключевой файл) и монтирует (открывает)
том VeraCrypt. Когда пользователь дважды щёлкает мышью по значку этого видеофайла, операционная система
запускает приложение, ассоциированное с файлами такого типа – в данном случае это, как правило, мультимедийный
проигрыватель. Затем мультимедийный проигрыватель начинает загружать маленькую начальную часть видеофайла
из зашифрованного тома VeraCrypt в ОЗУ (память), чтобы приступить к воспроизведению. Во время загрузки части
файла VeraCrypt автоматически расшифровывает её (в ОЗУ), после чего расшифрованная часть видео (хранящаяся
в ОЗУ) воспроизводится медиапроигрывателем. Пока эта часть воспроизводится, медиапроигрыватель начинает
считывать другую небольшую часть видеофайла из зашифрованного тома VeraCrypt в ОЗУ (память), и процесс
повторяется. Данная операция называется шифрованием/дешифрованием на лету, она работает для файлов любых
типов (не только видео).</div>
<p>Обратите внимание: VeraCrypt никогда не сохраняет на диске никаких данных в незашифрованном виде – такие
данные временно хранятся только в ОЗУ (оперативной памяти). Даже когда том смонтирован, хранящиеся в нём
данные по-прежнему остаются зашифрованными. При перезагрузке Windows или выключении компьютера том будет
размонтирован, а хранящиеся в нём файлы станут недоступными (и зашифрованными). Даже в случае непредвиденного
перебоя питания (без правильного завершения работы системы), хранящиеся в томе файлы останутся недоступными
(и зашифрованными). Чтобы получить к ним доступ вновь, нужно смонтировать том (и правильно указать пароль
и/или ключевой файл).
<br><br>Краткий учебник по началу работы см. в главе <i>Руководство для начинающих</i>.</p>
</div>
</body></html>
