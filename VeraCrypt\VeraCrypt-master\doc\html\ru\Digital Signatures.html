﻿<!DOCTYPE html>
<html lang="ru">
<head>
<meta http-equiv="content-type" content="text/html; charset=utf-8" />
<title>VeraCrypt - Бесплатное надёжное шифрование дисков с открытым исходным кодом</title>
<meta name="description" content="VeraCrypt это бесплатное программное обеспечение для шифрования дисков с открытым исходным кодом для Windows, Mac OS X (macOS) и Linux. В случае, если злоумышленник вынуждает вас раскрыть пароль, VeraCrypt обеспечивает правдоподобное отрицание наличия шифрования. В отличие от пофайлового шифрования, VeraCrypt шифрует данные в реальном времени (на лету), автоматически, прозрачно, требует очень мало памяти и не использует временные незашифрованные файлы."/>
<meta name="keywords" content="encryption, security, шифрование, безопасность"/>
<link href="styles.css" rel="stylesheet" type="text/css" />
</head>
<body>

<div>
<a href="Documentation.html"><img src="VeraCrypt128x128.png" alt="VeraCrypt"/></a>
</div>

<div id="menu">
	<ul>
	  <li><a href="Home.html">Начало</a></li>
	  <li><a href="Code.html">Исходный код</a></li>
	  <li><a href="Downloads.html">Загрузить</a></li>
	  <li><a class="active" href="Documentation.html">Документация</a></li>
	  <li><a href="Donation.html">Поддержать разработку</a></li>
	  <li><a href="https://sourceforge.net/p/veracrypt/discussion/" target="_blank">Форум</a></li>
	</ul>
</div>

<div>
<p>
<a href="Documentation.html">Документация</a>
<img src="arrow_right.gif" alt=">>" style="margin-top: 5px">
<a href="Miscellaneous.html">Miscellaneous</a>
<img src="arrow_right.gif" alt=">>" style="margin-top: 5px">
<a href="Digital%20Signatures.html">Цифровые подписи</a>
</p></div>

<div class="wikidoc">
<h1>Цифровые подписи</h1>
<h3>Зачем нужно проверять цифровые подписи</h3>
<p>Может так случиться, что установочный пакет VeraCrypt, который вы загружаете с нашего сервера, был создан или модифицирован
взломщиком. Например, взломщик мог воспользоваться какой-либо уязвимостью в используемом нами серверном ПО и изменить хранящиеся
на сервере установочные пакеты, либо он мог изменить любые файлы при их пересылке к вам.<br>
<br>
По этой причине следует всегда проверять целостность и аутентичность любого установочного пакета VeraCrypt , который вы загружаете или получаете из какого-либо иного источника. Другими словами, следует всегда проверять, что файл создан именно нами и не был модифицирован злоумышленником. Единственный способ это
сделать – проверить у файла так называемые цифровые подписи.</p>
<h3>Типы используемых нами цифровых подписей</h3>
<p>В настоящий момент мы используем цифровые подписи двух типов:</p>
<ul>
<li>Подписи <strong>PGP</strong> (доступны для всех пакетов с бинарными файлами и с исходным кодом для всех операционных систем).
</li><li>Подписи <strong>X.509</strong> (доступны для пакетов с бинарными файлами для Windows).
</li></ul>
<h3>Преимущества подписей X.509</h3>
<p>По сравнению с подписями PGP, подписи X.509 имеют следующие преимущества:</p>
<ul>
<li>значительно проще проверить, что ключ, которым подписан файл, действительно наш (а не взломщика);
</li><li>чтобы поверить подлинность подписи X.509, не требуется загружать и устанавливать никакого дополнительного ПО (см. ниже);
</li><li>не нужно загружать и импортировать наш открытый ключ (он встроен в подписанный файл);
</li><li>не нужно загружать отдельный файл с подписью (она встроена в подписанный файл).
</li></ul>
<h3>Преимущества подписей PGP</h3>
<p>По сравнению с подписями X.509, подписи PGP имеют следующее преимущество:</p>
<ul>
<li>они не зависят от источника сертификации (который может, например, фильтроваться/контролироваться неприятелем или быть
ненадёжным по другим причинам).
</li></ul>
<h3>Как проверять подписи X.509</h3>
<p>Обратите внимание, что в настоящий момент подписи X.509 доступны только для самораспаковывающихся установочных пакетов
VeraCrypt для Windows. Подпись X.509, встроенная в каждый из таких файлов вместе с цифровым сертификатом VeraCrypt Foundation,
выпущена общественной организацией сертификации. Чтобы проверить целостность и подлинность самораспаковывающегося
установочного пакета для Windows, сделайте следующее:</p>
<ol>
<li>Загрузите (скачайте) самораспаковывающийся установочный пакет VeraCrypt. </li><li>В Проводнике Windows щёлкните правой
кнопкой мыши по загруженному файлу (&lsquo;<em>VeraCrypt Setup.exe</em>&rsquo;) и выберите в контекстном меню пункт
<em>Свойства</em>.
</li><li>В диалоговом окне <em>Свойства</em> перейдите на вкладку <em>Цифровые подписи</em>.
</li><li>На вкладке <em>Цифровые подписи</em> в поле <em>Список подписей</em> дважды щёлкните по
строке с надписью &quot;<em>IDRIX</em>&quot; или &quot;<em>IDRIX SARL</em>&quot;.
</li><li>Появится диалоговое окно <em>Состав цифровой подписи</em>. Если вверху этого окна вы увидите следующую
фразу, значит целостность и подлинность пакета успешно прошли проверку и подтверждены:<br>
<br>
&quot;<em>Эта цифровая подпись действительна.</em>&quot;<br>
<br>
Если такой фразы нет, это означает, что файл скорее всего повреждён.<br>
Примечание. В ряде устаревших версий Windows проверка подписей не работает, так как отсутствуют некоторые необходимые сертификаты.
</li></ol>
<h3 id="VerifyPGPSignature">Как проверять подписи PGP</h3>
<p>Чтобы проверить подпись PGP, проделайте следующее:</p>
<ol>
<li>Установите любую программу шифрования с открытым ключом, поддерживающую подписи PGP. Для Windows можно загрузить
<a href="http://www.gpg4win.org/" target="_blank">Gpg4win</a>. См. подробности на сайте <a href="https://www.gnupg.org/">https://www.gnupg.org/</a>. </li>
<li>Создайте закрытый (личный) ключ (о том, как это сделать, см. в документации на ПО шифрования с открытым ключом).</li>
<li>Загрузите наш открытый ключ PGP с сайта <strong>AM Crypto</strong> (<a href="https://amcrypto.jp/VeraCrypt/VeraCrypt_PGP_public_key.asc" target="_blank">https://amcrypto.jp/VeraCrypt/VeraCrypt_PGP_public_key.asc</a>) или из надёжного репозитария (ID=0x680D16DE)
открытых ключей и импортируйте загруженный ключ в свою связку ключей (keyring). О том, как это сделать, см. в документации
на ПО шифрования с открытым ключом. Убедитесь, что его отпечаток – <strong>5069A233D55A0EEB174A5FC3821ACD02680D16DE</strong>.
<ul>
<li>Для VeraCrypt версии 1.22 и старее проверка должна использовать открытый ключ PGP, доступный по адресу
<a href="https://amcrypto.jp/VeraCrypt/VeraCrypt_PGP_public_key_2014.asc" target="_blank">https://amcrypto.jp/VeraCrypt/VeraCrypt_PGP_public_key_2014.asc</a>
или из надёжного репозитария открытых ключей (ID=0x54DDD393), чей отпечаток – <strong>993B7D7E8E413809828F0F29EB559C7C54DDD393</strong>.
</li>
</ul>
</li>
<li>Подпишите импортированный ключ своим личным ключом, чтобы пометить его как надёжный (о том, как это сделать,
см. в документации на ПО шифрования с открытым ключом).<br>
<br>
Примечание: если вы пропустите этот шаг и попытаетесь проверить любую нашу PGP-подпись, то получите сообщение
об ошибке, гласящее, что цифровая подпись неверна.
</li>
<li>Загрузите цифровую подпись, нажав кнопку <em>PGP Signature</em> рядом с файлом, который вы хотите проверить
(на <a href="https://veracrypt.jp/en/Downloads.html">странице загрузок</a>).
</li>
<li>Проверьте загруженную подпись (о том, как это сделать, см. в документации на ПО шифрования с открытым ключом).</li>
</ol>
<p>В Linux эти шаги могут быть выполнены с помощью следующих команд:</p>
<ul>
<li>Проверьте, что отпечаток открытого ключа равен <strong>5069A233D55A0EEB174A5FC3821ACD02680D16DE</strong>: <strong>gpg --import --import-options show-only VeraCrypt_PGP_public_key.asc</strong> (для старых версий gpg вместо этого введите:
<strong>gpg --with-fingerprint VeraCrypt_PGP_public_key.asc</strong>)</li>
<li>Если отпечаток такой, как ожидалось, импортируйте открытый ключ: <strong>gpg --import VeraCrypt_PGP_public_key.asc</strong>
</li>
<li>Проверьте подпись у установочного архива Linux (в этом примере – для версии 1.23): <strong>
gpg --verify veracrypt-1.23-setup.tar.bz2.sig veracrypt-1.23-setup.tar.bz2</strong>
</li></ul>
</div><div class="ClearBoth"></div></body></html>
